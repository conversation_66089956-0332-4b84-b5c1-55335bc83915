package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import java.time.LocalDate
import java.time.ZoneOffset
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.modules.console.dto.ConsoleUserDetail
import jp.co.torihada.fanme.modules.console.usecases.GetUserUseCaseInput
import jp.co.torihada.fanme.modules.console.usecases.IGetUser
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class UserControllerTest {

    @Mock private lateinit var getUserUseCase: IGetUser

    @InjectMocks private lateinit var userController: UserController

    @Test
    @DisplayName("getUser - 成功時 - ユースケースがConsoleUserDetailを返した場合、それをそのまま返すこと")
    fun `getUser should return ConsoleUserDetail when use case is successful`() {
        val testUserId = 1L
        val testBirthday = LocalDate.parse("2000-01-01").atStartOfDay().toInstant(ZoneOffset.UTC)

        val expectedDetail =
            ConsoleUserDetail(
                id = testUserId,
                uid = "test-uid",
                name = "Test User",
                accountIdentity = "<EMAIL>",
                isPublic = true,
                birthday = testBirthday,
                birthdayConfirmed = true,
                isBirthdayWeek = 0,
                gender = "male",
                icon = "http://example.com/icon.png",
                filledProfile = true,
                allowPublicSharing = true,
                purpose = 1,
                role = "creator",
                agencyId = 5L,
            )

        val input = GetUserUseCaseInput(testUserId)

        `when`(getUserUseCase.execute(input)).thenReturn(Ok(expectedDetail))

        val actualResponse = userController.getUser(testUserId)

        assertEquals(expectedDetail, actualResponse)
        verify(getUserUseCase).execute(input)
    }

    @Test
    @DisplayName("getUser - エラー時 - ユースケースがエラーを返した場合、そのエラーをスローすること")
    fun `getUser should throw exception when use case returns error`() {
        val testUserId = 2L
        val expectedException =
            ConsoleResourceNotFoundException("User with ID $testUserId not found")
        val input = GetUserUseCaseInput(testUserId)

        `when`(getUserUseCase.execute(input)).thenReturn(Err(expectedException))

        val actualException =
            assertThrows(ConsoleResourceNotFoundException::class.java) {
                userController.getUser(testUserId)
            }

        assertEquals(expectedException.message, actualException.message)
        verify(getUserUseCase).execute(input)
    }

    @Test
    @DisplayName("getUser - エラー時 - ユースケースがConsoleExceptionを返した場合、それをスローすること")
    fun `getUser should throw ConsoleException when use case returns it`() {
        val testUserId = 3L
        val genericError = ConsoleException(5000, "Some generic error from use case")
        val input = GetUserUseCaseInput(testUserId)

        `when`(getUserUseCase.execute(input)).thenReturn(Err(genericError))

        val actualException =
            assertThrows(ConsoleException::class.java) { userController.getUser(testUserId) }
        assertEquals(genericError.message, actualException.message)
        verify(getUserUseCase).execute(input)
    }
}
