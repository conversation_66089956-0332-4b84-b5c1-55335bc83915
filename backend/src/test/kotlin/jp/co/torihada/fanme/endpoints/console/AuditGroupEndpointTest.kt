package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.fanme.factories.AuditGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.AuditObjectFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.OperationType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.jboss.logging.Logger
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AuditGroupEndpointTest {
    @Inject private lateinit var logger: Logger

    @BeforeEach
    @Transactional
    fun setup() {
        AuditObject.deleteAll()
        AuditGroup.deleteAll()

        val user1 =
            UserFactory.createTestUser("audit-user-1", "Audit User 1", "<EMAIL>")!!
        val group1 =
            AuditGroupFactory.new(
                userUid = user1.uid!!,
                auditType = AuditType.SHOP,
                operationType = OperationType.INSERT,
                metadata = AuditGroupMetadata(shopId = 1L),
            )
        group1.persistAndFlush()
        AuditObjectFactory.new(group1.id!!, "bucket1", "path1", AssetType.IMAGE).persistAndFlush()

        val user2 =
            UserFactory.createTestUser("audit-user-2", "Audit User 2", "<EMAIL>")!!
        val group2 =
            AuditGroupFactory.new(
                userUid = user2.uid!!,
                auditType = AuditType.SHOP_ITEM,
                operationType = OperationType.UPDATE,
                metadata = AuditGroupMetadata(shopId = 2L),
            )
        group2.persistAndFlush()
        AuditObjectFactory.new(group2.id!!, "bucket2", "path2", AssetType.MOVIE).persistAndFlush()
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `SUPERロールで監査グループ一覧を取得できること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(200)
            .body("data.audit_groups.size()", equalTo(2))
            .body("data.audit_groups[0].user.name", notNullValue())
            .body("data.audit_groups[0].audit_objects", notNullValue())
    }

    @Test
    @TestSecurity(user = "test-biz", roles = [UserRole.BIZ_VALUE])
    fun `BIZロールで監査グループ一覧を取得できること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(200)
            .body("data.audit_groups.size()", equalTo(2))
    }

    @Test
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun `AGENTロールでは監査グループ一覧にアクセスできないこと`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(403)
    }

    @Test
    fun `認証無しの場合は401エラーが返されること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/audit-groups")
            .then()
            .statusCode(401)
    }
}
