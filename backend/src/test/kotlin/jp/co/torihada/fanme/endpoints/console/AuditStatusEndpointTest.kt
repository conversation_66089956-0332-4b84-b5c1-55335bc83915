package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.fanme.factories.AuditGroupFactory
import jp.co.torihada.fanme.modules.fanme.factories.AuditObjectFactory
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.*
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class AuditStatusEndpointTest {
    private var auditGroupId: Long = 0

    @BeforeEach
    @Transactional
    fun setup() {
        AuditObject.deleteAll()
        AuditGroup.deleteAll()

        val user = UserFactory.createTestUser("audit-user-1", "Audit User", "<EMAIL>")!!
        val group =
            AuditGroupFactory.new(
                userUid = user.uid!!,
                auditType = AuditType.SHOP,
                operationType = OperationType.INSERT,
                metadata = AuditGroupMetadata(shopId = 1L),
            )
        group.persistAndFlush()
        auditGroupId = group.id!!
        AuditObjectFactory.new(group.id!!, "bucket", "path", AssetType.IMAGE).persistAndFlush()
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `監査ステータスを更新できること`() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.APPROVED.value,
                "comment" to "ok",
                "auditedUserUid" to "admin",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(200)
            .body("data.result", equalTo(true))

        val updated = AuditGroup.findById(auditGroupId)
        assertThat(updated?.status, equalTo(AuditStatus.APPROVED))
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `監査ステータスを-1で更新できること`() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.REJECTED.value,
                "comment" to "rejected",
                "auditedUserUid" to "admin",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(200)
            .body("data.result", equalTo(true))

        val updated = AuditGroup.findById(auditGroupId)
        assertThat(updated?.status, equalTo(AuditStatus.REJECTED))
    }

    @Test
    @TestSecurity(user = "test-biz", roles = [UserRole.BIZ_VALUE])
    fun `BIZロールでステータスを更新できること`() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.APPROVED.value,
                "comment" to "ok from biz",
                "auditedUserUid" to "biz-user",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(200)
            .body("data.result", equalTo(true))

        val updated = AuditGroup.findById(auditGroupId)
        assertThat(updated?.status, equalTo(AuditStatus.APPROVED))
        assertThat(updated?.comment, equalTo("ok from biz"))
    }

    @Test
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun `AGENTロールではステータスを更新できないこと`() {
        val requestBody =
            mapOf(
                "status" to AuditStatus.APPROVED.value,
                "comment" to "attempt from agent",
                "auditedUserUid" to "agent-user",
            )

        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(403)

        val notUpdated = AuditGroup.findById(auditGroupId)
        assertThat(notUpdated?.status, equalTo(AuditStatus.UNAUDITED)) // ステータスが変更されていないことを確認
    }

    @Test
    fun `認証無しの場合は401が返ること`() {
        val requestBody = mapOf("status" to 9)
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .body(requestBody)
            .`when`()
            .put("/console/audit-groups/${auditGroupId}/status")
            .then()
            .statusCode(401)
    }
}
