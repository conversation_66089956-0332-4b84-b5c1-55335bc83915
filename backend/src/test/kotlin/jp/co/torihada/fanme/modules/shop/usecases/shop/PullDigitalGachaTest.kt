package jp.co.torihada.fanme.modules.shop.usecases.shop

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.DigitalGachaNotPurchasedException
import jp.co.torihada.fanme.exception.DigitalGachaPullCountExceededException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.controllers.DigitalGachaController
import jp.co.torihada.fanme.modules.shop.controllers.ShopController
import jp.co.torihada.fanme.modules.shop.controllers.requests.ShopRequest
import jp.co.torihada.fanme.modules.shop.factories.*
import jp.co.torihada.fanme.modules.shop.lib.TestResultLogger
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Tag
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class PullDigitalGachaTest {
    @Inject private lateinit var digitalGachaController: DigitalGachaController
    @Inject private lateinit var shopController: ShopController

    val creatorUid = "1234567890"
    val name = "test shop"
    val description = "test description"
    val headerImageUri = "https://example.com"
    val message = "test message"

    companion object {
        private var setupDone = false
    }

    @BeforeEach
    fun setup() {
        if (!setupDone) {
            shopController.createShop(
                ShopRequest.CreateShop(
                    creatorUid = creatorUid,
                    name = name,
                    description = description,
                    headerImageUri = headerImageUri,
                    message = message,
                )
            )
            setupDone = true
        }
    }

    // 引いたガチャがgacha_received_filesに追加される(重複なし)
    @Test
    @TestTransaction
    fun `should add pulled gacha item to gacha_received_files without duplicates`() {
        val purchaserUid = "test-purchaser-uid"
        val quantity = 1
        val (shop, item) = createDigitalGachaItem(isDuplicated = false)
        val purchasedItem = purchaseDigitalGachaItem(purchaserUid, item, shop.id!!, quantity)

        digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)
        // ガチャを引いた結果を確認
        val gachaReceivedFiles =
            GachaReceivedFile.findByPurchaserUidAndItemId(purchaserUid, item.id!!)
        // 引いたガチャの数が購入した数と一致することを確認
        assertEquals(quantity, gachaReceivedFiles.size)

        val received = gachaReceivedFiles.first()

        // 引いた商品と購入商品が一致することを確認
        assertEquals(purchasedItem.id, received?.purchasedItem?.id)

        val expectedItemFileIds = ItemFile.findByItemId(item.id!!).map { it.id }
        // 引いた商品が購入した商品のファイルのいずれかであることを確認
        assert(expectedItemFileIds.contains(received?.itemFile?.id))
    }

    // 引いたガチャがgacha_received_filesに追加される(重複あり)
    @Test
    @TestTransaction
    fun `should add pulled gacha item to gacha_received_files with duplicates`() {
        val purchaserUid = "test-purchaser-uid"
        val quantity = 1
        val (shop, item) = createDigitalGachaItem(isDuplicated = true)
        val purchasedItem = purchaseDigitalGachaItem(purchaserUid, item, shop.id!!, quantity)

        // ガチャを引く
        digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)

        // ガチャを引いた結果を確認
        val gachaReceivedFiles =
            GachaReceivedFile.findByPurchaserUidAndItemId(purchaserUid, item.id!!)
        // 引いたガチャの数が購入した数と一致することを確認
        assertEquals(quantity, gachaReceivedFiles.size)

        val received = gachaReceivedFiles.first()
        // 引いた商品と購入商品が一致することを確認
        assertEquals(purchasedItem.id, received?.purchasedItem?.id)
        val expectedItemFileIds = ItemFile.findByItemId(item.id!!).map { it.id }
        // 引いた商品が購入した商品のファイルのいずれかであることを確認
        assert(expectedItemFileIds.contains(received?.itemFile?.id))
    }

    @Test
    @TestTransaction
    fun `should not allow pulled gacha if not purchased`() {
        val purchaserUid = "test-purchaser-uid"
        val (_, item) = createDigitalGachaItem()

        val exception =
            assertThrows(DigitalGachaNotPurchasedException::class.java) {
                digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)
            }

        assertEquals(
            "Digital gacha not purchased. Please purchase the digital gacha before pulling.",
            exception.message,
        )
    }

    @Test
    @TestTransaction
    fun `should not allow pulling gacha if already pulled`() {
        val purchaserUid = "test-purchaser-uid"
        val quantity = 1

        val (shop, item) = createDigitalGachaItem()
        purchaseDigitalGachaItem(purchaserUid, item, shop.id!!, quantity)

        // 最初のドローは成功する
        digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)

        // 2回目は失敗する
        val exception =
            assertThrows(DigitalGachaNotPurchasedException::class.java) {
                digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)
            }
        assertEquals(
            "Digital gacha not purchased. Please purchase the digital gacha before pulling.",
            exception.message,
        )
    }

    // SからC賞まで、確率に応じてガチャを排出する
    @Tag("slow")
    @Test
    @TestTransaction
    fun `should pull gacha according to probabilities from S to C awards`() {
        val purchaserUid = "test-purchaser-uid"
        val pullCount = 20_000

        val (shop, item) = createDigitalGachaItem(isDuplicated = true)

        // ガチャ購入
        purchaseDigitalGachaItem(purchaserUid, item, shop.id!!, pullCount)

        // ガチャを引く(10000回)
        val received = digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)

        val awardResults = mutableMapOf<AwardType, Int>()
        received.forEach { file ->
            val awardType = AwardType.fromValue(file.awardType)
            awardResults[awardType] = awardResults.getOrDefault(awardType, 0) + 1
        }

        val tolerance = 0.1 // ±10%

        val totalPulls = received.size
        val expectedMap =
            mapOf(AwardType.S to 5, AwardType.A to 15, AwardType.B to 20, AwardType.C to 60)

        expectedMap.forEach { (awardType, expectedRate) ->
            val expectedCount = totalPulls * (expectedRate / 100.0)
            val actualCount = awardResults[awardType] ?: 0
            val lowerBound = expectedCount * (1 - tolerance)
            val upperBound = expectedCount * (1 + tolerance)

            assertTrue(
                actualCount in lowerBound.toInt()..upperBound.toInt(),
                "Expected ${awardType.name} to be within ${lowerBound.toInt()}..${upperBound.toInt()}, but got $actualCount",
            )
        }
    }

    //    // 残りの商品よりも引く回数が多い(重複なし)
    @Test
    @TestTransaction
    fun `should not allow pulling more items than available without duplicates`() {
        val purchaserUid = "test-purchaser-uid"
        val (shop, item) = createDigitalGachaItem(isDuplicated = false)
        // 4つしかない商品に対して5回引く
        val pullCount = 5

        purchaseDigitalGachaItem(purchaserUid, item, shop.id!!, pullCount)

        assertThrows(DigitalGachaPullCountExceededException::class.java) {
            digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)
        }
    }

    // 重複なしでは商品が被らない
    @Test
    @TestTransaction
    fun `should not allow pulling duplicate items without duplicates`() {
        val purchaserUid = "test-purchaser-uid"
        val (shop, item) = createDigitalGachaItem(isDuplicated = false)
        // 4つしかない商品に対して4回引く
        val pullCount = 4

        purchaseDigitalGachaItem(purchaserUid, item, shop.id!!, pullCount)

        digitalGachaController.pullDigitalGacha(item.id!!, purchaserUid)

        val gachaReceivedFiles =
            GachaReceivedFile.findByPurchaserUidAndItemId(purchaserUid, item.id!!)
        val receivedItemFileIds = gachaReceivedFiles.map { it?.itemFile?.id }
        val expectedItemFileIds = ItemFile.findByItemId(item.id!!).map { it.id }

        assert(receivedItemFileIds.all { expectedItemFileIds.contains(it) })
    }

    /** ガチャアイテムを作成する共通メソッド */
    private fun createDigitalGachaItem(isDuplicated: Boolean = false): Pair<Shop, Item> {
        val shop = Shop.findByCreatorUid(creatorUid) ?: throw ResourceNotFoundException("Shop")

        // ガチャを出品
        val item =
            ItemFactory.new(
                shopId = shop.id!!,
                name = "test item",
                description = "test description",
                itemType = ItemType.DIGITAL_GACHA,
                thumbnailUri = "https://example.com",
                thumbnailFrom = 0,
                thumbnailBlurLevel = 0,
                thumbnailWatermarkLevel = 1,
                price = 100,
            )
        item.persist()

        val gachaItem = GachaItemFactory.new(itemId = item.id!!, isDuplicated = isDuplicated)
        gachaItem.persist()

        // 確率設定を追加
        listOf(AwardType.C to 60, AwardType.B to 20, AwardType.A to 15, AwardType.S to 5).forEach {
            (awardType, probability) ->
            GachaProbabilityFactory.new(
                    gachaItemId = gachaItem.id!!,
                    awardType = awardType,
                    probability = probability,
                )
                .persist()
        }

        // 商品ファイルを追加
        listOf("S賞" to 4, "A賞" to 3, "B賞" to 2, "C賞" to 1).forEach { (name, awardType) ->
            val itemFile =
                ItemFileFactory.new(name = name, itemId = item.id!!, size = 1.0f, price = 100)
            itemFile.persist()

            GachaItemFileFactory.new(
                    itemFileId = itemFile.id!!,
                    awardType = AwardType.fromValue(awardType).value,
                )
                .persist()
        }

        return Pair(shop, item)
    }

    /** ガチャを購入する共通メソッド */
    private fun purchaseDigitalGachaItem(
        purchaserUid: String,
        item: Item,
        shopId: Long,
        quantity: Int = 1,
    ): PurchasedItem {
        val order = OrderFactory.new(purchaserUid = purchaserUid, shopId = shopId)
        order.persist()

        val purchasedItem =
            PurchasedItemFactory.new(
                order = order,
                purchaserUid = purchaserUid,
                itemId = item.id!!,
                price = 100,
                quantity = quantity,
                status = Util.PurchasedItemStatus.PAYSUCCESS.value,
            )
        purchasedItem.persist()

        return purchasedItem
    }
}
