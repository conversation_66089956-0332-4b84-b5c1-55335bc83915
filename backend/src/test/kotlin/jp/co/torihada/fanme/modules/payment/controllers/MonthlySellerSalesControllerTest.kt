package jp.co.torihada.fanme.modules.payment.controllers

import io.quarkus.test.TestTransaction
import io.quarkus.test.common.QuarkusTestResource
import io.quarkus.test.junit.QuarkusTest
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.Config as CommonConfig
import jp.co.torihada.fanme.exception.InvalidYearMonthFormatException
import jp.co.torihada.fanme.modules.payment.Config
import jp.co.torihada.fanme.modules.payment.factories.MonthlySellerSaleFactory
import jp.co.torihada.fanme.modules.payment.lib.TestResultLogger
import jp.co.torihada.fanme.modules.payment.usecases.TestResourceLifecycleManager
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith

@QuarkusTest
@ExtendWith(TestResultLogger::class)
@QuarkusTestResource(TestResourceLifecycleManager::class)
class MonthlySellerSalesControllerTest {

    @Inject lateinit var commonConfig: CommonConfig

    @Inject lateinit var config: Config

    @Inject lateinit var monthlySellerSalesController: MonthlySellerSalesController

    @Test
    @TestTransaction
    fun `test getMonthlySellerSales returns correct sales data`() {
        val sellerUserId1 = "testSeller1"
        val sellerUserId2 = "testSeller2"
        val yearMonth1 = "202301"
        val yearMonth2 = "202302"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale1 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId1,
                yearMonth = yearMonth1,
                merged = true,
                expirationDate = expirationDate,
            )
        sale1.sellerSalesAmount = 1000
        sale1.persist()

        val sale2 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId1,
                yearMonth = yearMonth2,
                merged = false,
                expirationDate = expirationDate,
            )
        sale2.sellerSalesAmount = 1500
        sale2.persist()

        val sale3 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId2,
                yearMonth = yearMonth1,
                merged = true,
                expirationDate = expirationDate,
            )
        sale3.sellerSalesAmount = 750
        sale3.persist()

        val result =
            monthlySellerSalesController.getMonthlySellerSales(
                sellerUserIds = listOf(sellerUserId1, sellerUserId2),
                fromYearMonth = "202301",
                toYearMonth = "202302",
            )

        assertNotNull(result.monthlySales)
        assertEquals(3, result.monthlySales.size)

        val seller1Sales = result.monthlySales.filter { it.sellerUserId == sellerUserId1 }
        assertEquals(2, seller1Sales.size)

        val seller1YearMonth1 = seller1Sales.find { it.yearMonth == yearMonth1 }
        assertNotNull(seller1YearMonth1)
        assertEquals(sellerUserId1, seller1YearMonth1!!.sellerUserId)
        assertEquals(yearMonth1, seller1YearMonth1.yearMonth)
        assertEquals(1000, seller1YearMonth1.sellerSalesAmount)
        assertTrue(seller1YearMonth1.merged)
        assertEquals(expirationDate, seller1YearMonth1.expirationDate)

        val seller1YearMonth2 = seller1Sales.find { it.yearMonth == yearMonth2 }
        assertNotNull(seller1YearMonth2)
        assertEquals(sellerUserId1, seller1YearMonth2!!.sellerUserId)
        assertEquals(yearMonth2, seller1YearMonth2.yearMonth)
        assertEquals(1500, seller1YearMonth2.sellerSalesAmount)
        assertTrue(!seller1YearMonth2.merged)
        assertEquals(expirationDate, seller1YearMonth2.expirationDate)

        val seller2Sales = result.monthlySales.filter { it.sellerUserId == sellerUserId2 }
        assertEquals(1, seller2Sales.size)

        val seller2YearMonth1 = seller2Sales.find { it.yearMonth == yearMonth1 }
        assertNotNull(seller2YearMonth1)
        assertEquals(sellerUserId2, seller2YearMonth1!!.sellerUserId)
        assertEquals(yearMonth1, seller2YearMonth1.yearMonth)
        assertEquals(750, seller2YearMonth1.sellerSalesAmount)
        assertTrue(seller2YearMonth1.merged)
        assertEquals(expirationDate, seller2YearMonth1.expirationDate)
    }

    @Test
    @TestTransaction
    fun `test getMonthlySellerSales with invalid fromYearMonth throws exception`() {
        val exception =
            assertThrows(InvalidYearMonthFormatException::class.java) {
                monthlySellerSalesController.getMonthlySellerSales(
                    sellerUserIds = listOf("testSeller"),
                    fromYearMonth = "invalid",
                    toYearMonth = null,
                )
            }

        assertNotNull(exception)
        assertTrue(exception.message!!.contains("Invalid format"))
    }

    @Test
    @TestTransaction
    fun `test getMonthlySellerSales with invalid toYearMonth throws exception`() {
        val exception =
            assertThrows(InvalidYearMonthFormatException::class.java) {
                monthlySellerSalesController.getMonthlySellerSales(
                    sellerUserIds = listOf("testSeller"),
                    fromYearMonth = null,
                    toYearMonth = "invalid",
                )
            }

        assertTrue(exception.message?.contains("Invalid format") ?: false)
    }

    @Test
    @TestTransaction
    fun `test getMonthlySellerSales with date range filtering works correctly`() {
        val sellerUserId = "testSeller"
        val yearMonth1 = "202301"
        val yearMonth2 = "202302"
        val yearMonth3 = "202303"
        val expirationDate = Instant.now().plusSeconds(3600)

        val sale1 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth1,
                merged = true,
                expirationDate = expirationDate,
            )
        sale1.sellerSalesAmount = 1000
        sale1.persist()

        val sale2 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth2,
                merged = true,
                expirationDate = expirationDate,
            )
        sale2.sellerSalesAmount = 1500
        sale2.persist()

        val sale3 =
            MonthlySellerSaleFactory.new(
                tenant = commonConfig.tenant(),
                sellerUserId = sellerUserId,
                yearMonth = yearMonth3,
                merged = true,
                expirationDate = expirationDate,
            )
        sale3.sellerSalesAmount = 2000
        sale3.persist()

        val result =
            monthlySellerSalesController.getMonthlySellerSales(
                sellerUserIds = listOf(sellerUserId),
                fromYearMonth = "202302",
                toYearMonth = "202302",
            )

        assertNotNull(result.monthlySales)
        assertEquals(1, result.monthlySales.size)
        assertEquals(yearMonth2, result.monthlySales[0].yearMonth)
        assertEquals(1500, result.monthlySales[0].sellerSalesAmount)
    }

    @Test
    @TestTransaction
    fun `test getMonthlySellerSales with empty seller IDs list returns empty result`() {
        val result =
            monthlySellerSalesController.getMonthlySellerSales(
                sellerUserIds = emptyList(),
                fromYearMonth = null,
                toYearMonth = null,
            )

        assertNotNull(result.monthlySales)
        assertEquals(0, result.monthlySales.size)
    }
}
