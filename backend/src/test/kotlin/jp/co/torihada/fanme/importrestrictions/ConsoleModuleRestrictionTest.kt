package jp.co.torihada.fanme.importrestrictions

import java.io.File
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

class ConsoleModuleRestrictionTest {

    private val consoleDir = File("src/main/kotlin/jp/co/torihada/fanme/modules/console")

    private fun shouldSkipLine(lines: List<String>, lineNum: Int): Boolean {
        if (lineNum > 0) {
            val prevLine = lines[lineNum - 1].trim()
            if (prevLine.contains("// console-test-ignore")) {
                return true
            }
        }
        return false
    }

    @Test
    fun `ConsoleモジュールがPaymentのusecasesをimportしていないこと`() {
        val violations = checkUsecasesImport("payment")
        assertTrue(
            violations.isEmpty(),
            "以下のファイルでPaymentモジュールのusecasesの不適切なimportが見つかりました:\n${violations.joinToString("\n")}",
        )
    }

    @Test
    fun `ConsoleモジュールがShopのusecasesをimportしていないこと`() {
        val violations = checkUsecasesImport("shop")
        assertTrue(
            violations.isEmpty(),
            "以下のファイルでShopモジュールのusecasesの不適切なimportが見つかりました:\n${violations.joinToString("\n")}",
        )
    }

    @Test
    fun `ConsoleモジュールがFanmeのusecasesをimportしていないこと`() {
        val violations = checkUsecasesImport("fanme")
        assertTrue(
            violations.isEmpty(),
            "以下のファイルでFanmeモジュールのusecasesの不適切なimportが見つかりました:\n${violations.joinToString("\n")}",
        )
    }

    @Test
    fun `ConsoleモジュールがPaymentモデルのcompanion objectを直接使用していないこと`() {
        val violations = checkCompanionObjectUsage("payment")
        assertTrue(
            violations.isEmpty(),
            "以下のファイルでPaymentモデルのcompanion objectの直接使用が見つかりました:\n${violations.joinToString("\n")}",
        )
    }

    @Test
    fun `ConsoleモジュールがShopモデルのcompanion objectを直接使用していないこと`() {
        val violations = checkCompanionObjectUsage("shop")
        assertTrue(
            violations.isEmpty(),
            "以下のファイルでShopモデルのcompanion objectの直接使用が見つかりました:\n${violations.joinToString("\n")}",
        )
    }

    @Test
    fun `ConsoleモジュールがFanmeモデルのcompanion objectを直接使用していないこと`() {
        val violations = checkCompanionObjectUsage("fanme")
        assertTrue(
            violations.isEmpty(),
            "以下のファイルでFanmeモデルのcompanion objectの直接使用が見つかりました:\n${violations.joinToString("\n")}",
        )
    }

    private fun checkUsecasesImport(targetModule: String): List<String> {
        val violations = mutableListOf<String>()
        consoleDir
            .walkTopDown()
            .filter { it.isFile && it.extension == "kt" }
            .forEach { file ->
                val lines = file.readLines()
                lines.forEachIndexed { lineNum, line ->
                    if (shouldSkipLine(lines, lineNum)) {
                        return@forEachIndexed
                    }

                    if (
                        line.contains("import jp.co.torihada.fanme.modules.$targetModule.usecases")
                    ) {
                        violations.add(
                            "${file.relativeTo(File("src/main/kotlin"))}:${lineNum + 1} - $line"
                        )
                    }
                }
            }
        return violations
    }

    private fun checkCompanionObjectUsage(targetModule: String): List<String> {
        val violations = mutableListOf<String>()
        consoleDir
            .walkTopDown()
            .filter { it.isFile && it.extension == "kt" }
            .forEach { file ->
                val content = file.readText()
                val importedModels = mutableSetOf<String>()

                content.lines().forEach { line ->
                    val importMatch =
                        """import\s+jp\.co\.torihada\.fanme\.modules\.$targetModule\.models\.(\w+)"""
                            .toRegex()
                            .find(line)
                    importMatch?.let { importedModels.add(it.groupValues[1]) }
                }

                val lines = content.lines()
                lines.forEachIndexed { lineNum, line ->
                    if (shouldSkipLine(lines, lineNum)) {
                        return@forEachIndexed
                    }

                    importedModels.forEach { modelName ->
                        val companionMethodPattern =
                            """\b$modelName\.(find|delete|create|update|count|list|persist|flush)"""
                                .toRegex()
                        if (companionMethodPattern.containsMatchIn(line)) {
                            violations.add(
                                "${file.relativeTo(File("src/main/kotlin"))}:${lineNum + 1} - $line"
                            )
                        }
                    }
                }
            }
        return violations
    }
}
