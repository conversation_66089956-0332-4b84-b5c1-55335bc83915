package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class UserEndpointTest {

    private var userId: Long = 0

    @BeforeEach
    @Transactional
    fun setup() {
        ConsoleUser.deleteAll()
        User.delete("uid LIKE ?1", "test-user-%")

        val user =
            UserFactory.createTestUser("test-user-1", "Test User", "<EMAIL>")
                ?: throw IllegalStateException("Failed to create test user")
        userId = user.id ?: throw IllegalStateException("User id is null")
        val consoleUser = ConsoleUserFactory.new(userId, null, UserRole.CREATOR_VALUE)
        consoleUser.persistAndFlush()
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `SUPERロールでユーザー情報を取得できること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$userId")
            .then()
            .statusCode(200)
            .body("data.user", notNullValue())
            .body("data.user.id", equalTo(userId.toInt()))
            .body("data.user.name", equalTo("Test User"))
            .body("data.user.accountIdentity", equalTo("<EMAIL>"))
    }

    @Test
    @TestSecurity(user = "test-biz", roles = [UserRole.BIZ_VALUE])
    fun `BIZロールでユーザー情報を取得できること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$userId")
            .then()
            .statusCode(200)
            .body("data.user", notNullValue())
    }

    @Test
    @TestSecurity(user = "test-creator", roles = [UserRole.CREATOR_VALUE])
    fun `CREATORロールではユーザー情報を取得できない`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$userId")
            .then()
            .statusCode(403)
            .body("data", equalTo(emptyMap<String, Any>()))
    }

    @Test
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun `AGENTロールでユーザー情報を取得できないこと`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$userId")
            .then()
            .statusCode(403)
            .body("data", equalTo(emptyMap<String, Any>()))
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `不正なIDの場合はJAX-RSのリソースメソッドでResponseFilter通らず500返すこと`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/0")
            .then()
            .statusCode(500)
            .body("data", equalTo(null))
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `存在しないユーザーIDの場合は400エラーが返されること`() {
        val nonExistentId = 99999L
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/$nonExistentId")
            .then()
            .statusCode(400)
            .body("data", equalTo(emptyMap<String, Any>()))
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `JAX-RSフレームワークの不正な文字列入力の場合は404エラーが返されること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users/abc")
            .then()
            .statusCode(404)
            .body("data", equalTo(emptyMap<String, Any>()))
    }
}
