package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.InjectMock
import io.quarkus.test.junit.QuarkusTest
import jakarta.transaction.Transactional
import java.time.Instant
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.payment.controllers.MonthlySellerSalesController
import jp.co.torihada.fanme.modules.payment.controllers.SellerAccountBalanceController
import jp.co.torihada.fanme.modules.payment.controllers.responses.MonthlySellerSalesResponse
import jp.co.torihada.fanme.modules.payment.controllers.responses.SellerAccountBalanceResponse
import jp.co.torihada.fanme.modules.payment.dto.MonthlySalesDto
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.TestInstance
import org.mockito.Mockito.`when`

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
abstract class AgencyEndpointTestBase {

    @InjectMock lateinit var sellerAccountBalanceController: SellerAccountBalanceController
    @InjectMock lateinit var monthlySellerSalesController: MonthlySellerSalesController

    @BeforeEach
    @Transactional
    fun setup() {
        ConsoleUser.deleteAll()
        Agency.deleteAll()

        val agency =
            Agency().apply {
                name = "Test Agency"
                createdAt = Instant.now()
            }
        agency.persist()

        val unauthorizedAgency =
            Agency().apply {
                name = "Unauthorized Agency"
                createdAt = Instant.now()
            }
        unauthorizedAgency.persist()

        val testAgentAgency =
            Agency().apply {
                name = "Test Agent Agency"
                createdAt = Instant.now()
            }
        testAgentAgency.persist()

        val superUser =
            ConsoleUserFactory.createTestUser(
                "super-user-1",
                "Super User",
                "<EMAIL>",
            )
        val bizUser =
            ConsoleUserFactory.createTestUser("biz-user-1", "Biz User", "<EMAIL>")
        val agentUser =
            ConsoleUserFactory.createTestUser(
                "agent-user-1",
                "Agent User",
                "<EMAIL>",
            )
        val testAgentUser =
            ConsoleUserFactory.createTestUser(
                "test-agent",
                "Test Agent User",
                "<EMAIL>",
            )
        val creatorUser1 =
            ConsoleUserFactory.createTestUser(
                "creator-user-1",
                "Creator User 1",
                "<EMAIL>",
            )
        val creatorUser2 =
            ConsoleUserFactory.createTestUser(
                "creator-user-2",
                "Creator User 2",
                "<EMAIL>",
            )

        val users =
            listOf(
                    superUser?.id?.let { ConsoleUserFactory.new(it, null, UserRole.SUPER_VALUE) },
                    bizUser?.id?.let { ConsoleUserFactory.new(it, null, UserRole.BIZ_VALUE) },
                    agentUser?.id?.let {
                        ConsoleUserFactory.new(it, agency.id, UserRole.AGENT_VALUE)
                    },
                    testAgentUser?.id?.let {
                        ConsoleUserFactory.new(it, testAgentAgency.id, UserRole.AGENT_VALUE)
                    },
                    creatorUser1?.id?.let {
                        ConsoleUserFactory.new(it, agency.id, UserRole.CREATOR_VALUE)
                    },
                    creatorUser2?.id?.let {
                        ConsoleUserFactory.new(it, agency.id, UserRole.CREATOR_VALUE)
                    },
                )
                .filterNotNull()

        users.forEach { it.persistAndFlush() }

        setupMockBalances()
    }

    private fun setupMockBalances() {
        val allBalances =
            mapOf(
                "agent-user-1" to
                    SellerAccountBalanceResponse.Balance(
                        sellerUserId = "agent-user-1",
                        accumulatedSales = 15000,
                        withdrawableAmount = 7500,
                    ),
                "test-agent" to
                    SellerAccountBalanceResponse.Balance(
                        sellerUserId = "test-agent",
                        accumulatedSales = 8000,
                        withdrawableAmount = 4000,
                    ),
                "creator-user-1" to
                    SellerAccountBalanceResponse.Balance(
                        sellerUserId = "creator-user-1",
                        accumulatedSales = 10000,
                        withdrawableAmount = 5000,
                    ),
                "creator-user-2" to
                    SellerAccountBalanceResponse.Balance(
                        sellerUserId = "creator-user-2",
                        accumulatedSales = 20000,
                        withdrawableAmount = 10000,
                    ),
            )

        val monthlySales =
            mapOf(
                "agent-user-1" to
                    listOf(
                        MonthlySalesDto(
                            sellerUserId = "agent-user-1",
                            yearMonth = "202401",
                            sellerSalesAmount = 8000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                        MonthlySalesDto(
                            sellerUserId = "agent-user-1",
                            yearMonth = "202402",
                            sellerSalesAmount = 7000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                    ),
                "test-agent" to
                    listOf(
                        MonthlySalesDto(
                            sellerUserId = "test-agent",
                            yearMonth = "202401",
                            sellerSalesAmount = 4000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                        MonthlySalesDto(
                            sellerUserId = "test-agent",
                            yearMonth = "202402",
                            sellerSalesAmount = 4000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                    ),
                "creator-user-1" to
                    listOf(
                        MonthlySalesDto(
                            sellerUserId = "creator-user-1",
                            yearMonth = "202401",
                            sellerSalesAmount = 3000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                        MonthlySalesDto(
                            sellerUserId = "creator-user-1",
                            yearMonth = "202402",
                            sellerSalesAmount = 2000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                    ),
                "creator-user-2" to
                    listOf(
                        MonthlySalesDto(
                            sellerUserId = "creator-user-2",
                            yearMonth = "202401",
                            sellerSalesAmount = 5000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                        MonthlySalesDto(
                            sellerUserId = "creator-user-2",
                            yearMonth = "202402",
                            sellerSalesAmount = 5000,
                            merged = false,
                            expirationDate = Instant.now().plusSeconds(86400),
                        ),
                    ),
            )

        `when`(
                sellerAccountBalanceController.getSellerAccountBalances(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2")
                )
            )
            .thenReturn(
                SellerAccountBalanceResponse.GetBalances(
                    balances =
                        listOf(
                            allBalances.getValue("agent-user-1"),
                            allBalances.getValue("creator-user-1"),
                            allBalances.getValue("creator-user-2"),
                        )
                )
            )

        `when`(sellerAccountBalanceController.getSellerAccountBalances(listOf("test-agent")))
            .thenReturn(
                SellerAccountBalanceResponse.GetBalances(
                    balances = listOf(allBalances.getValue("test-agent"))
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                    null,
                    null,
                )
            )
            .thenReturn(
                MonthlySellerSalesResponse.GetMonthlySales(
                    monthlySales =
                        monthlySales.getValue("agent-user-1") +
                            monthlySales.getValue("creator-user-1") +
                            monthlySales.getValue("creator-user-2")
                )
            )

        `when`(monthlySellerSalesController.getMonthlySellerSales(listOf("test-agent"), null, null))
            .thenReturn(
                MonthlySellerSalesResponse.GetMonthlySales(
                    monthlySales = monthlySales.getValue("test-agent")
                )
            )
    }
}
