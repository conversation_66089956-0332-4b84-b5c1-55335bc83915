package jp.co.torihada.fanme.modules.console.factories

import io.quarkus.test.junit.QuarkusTest
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConsoleUserFactoryTest {

    private val testUid = "test-factory-uid"
    private val testName = "Test User"
    private val testEmail = "<EMAIL>"

    @BeforeEach
    @Transactional
    fun setup() {
        ConsoleUser.deleteAll()
        User.delete("uid", testUid)
    }

    @AfterEach
    @Transactional
    fun cleanup() {
        ConsoleUser.deleteAll()
        User.delete("uid", testUid)
    }

    @Test
    @Transactional
    fun testCreateConsoleUserWithNewUser() {
        val user = UserFactory.createTestUser(testUid, testName, testEmail)
        assertNotNull(user)

        val consoleUser = ConsoleUserFactory.new(user!!.id!!, null, UserRole.CREATOR_VALUE)

        assertNotNull(consoleUser)
        assertEquals(user.id, consoleUser.user?.id)
        assertEquals(UserRole.CREATOR_VALUE, consoleUser.role)
        assertEquals(null, consoleUser.agencyId)
    }

    @Test
    @Transactional
    fun testCreateConsoleUserWithAgency() {
        val user = UserFactory.createTestUser(testUid, testName, testEmail)
        assertNotNull(user)

        val agencyId = 123L
        val consoleUser = ConsoleUserFactory.new(user!!.id!!, agencyId, UserRole.BIZ_VALUE)

        assertNotNull(consoleUser)
        assertEquals(user.id, consoleUser.user?.id)
        assertEquals(UserRole.BIZ_VALUE, consoleUser.role)
        assertEquals(agencyId, consoleUser.agencyId)
    }
}
