package jp.co.torihada.fanme.modules.console.factories

import io.quarkus.test.junit.QuarkusTest
import jakarta.transaction.Transactional
import java.time.Instant
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class FactoryTest {

    @BeforeEach
    @Transactional
    fun setup() {
        ConsoleUser.deleteAll()
        User.deleteAll()
    }

    @AfterEach
    @Transactional
    fun cleanup() {
        ConsoleUser.deleteAll()
        User.deleteAll()
    }

    @Test
    fun testUserFactory() {
        val user =
            UserFactory.new(
                uid = "test-factory-user-1",
                name = "Test Factory User",
                accountIdentity = "<EMAIL>",
            )

        assertNotNull(user)
        assertEquals("test-factory-user-1", user.uid)
    }

    @Test
    @Transactional
    fun testUserFactoryCreate() {
        val user =
            UserFactory.createTestUser(
                uid = "test-factory-user-1",
                name = "Test Factory User",
                accountIdentity = "<EMAIL>",
            )

        assertNotNull(user)
        assertEquals("test-factory-user-1", user?.uid)

        val savedUser = User.find("uid", "test-factory-user-1").firstResult()
        assertNotNull(savedUser)
    }

    @Test
    fun testUserFactoryWithAllFields() {
        val user =
            UserFactory.new(
                uid = "test-factory-user-2",
                name = "Full User",
                accountIdentity = "<EMAIL>",
                gender = "MALE",
                birthday = Instant.parse("1990-01-01T00:00:00Z"),
                birthdayConfirmed = true,
                isPublic = false,
                allowPublicSharing = true,
                filledProfile = false,
                purpose = 2,
                icon = "https://example.com/icon.png",
            )

        assertNotNull(user)
        assertEquals("test-factory-user-2", user.uid)
    }

    @Test
    @Transactional
    fun testConsoleUserFactory() {
        val testUser =
            UserFactory.createTestUser(
                uid = "test-console-user-1",
                name = "Test Console User",
                accountIdentity = "<EMAIL>",
            )

        val consoleUser =
            ConsoleUserFactory.new(
                creatorId = testUser?.id ?: throw AssertionError("Test user not created"),
                agencyId = null,
                role = UserRole.SUPER_VALUE,
            )

        assertNotNull(consoleUser)
        assertEquals(UserRole.SUPER_VALUE, consoleUser.role)
        assertEquals(testUser.id, consoleUser.user?.id)
        assertEquals(null, consoleUser.agencyId)
    }

    @Test
    @Transactional
    fun testConsoleUserFactoryWithAllFields() {
        val testUser =
            UserFactory.createTestUser(
                uid = "test-console-user-2",
                name = "Test Console User 2",
                accountIdentity = "<EMAIL>",
            )

        val consoleUser =
            ConsoleUserFactory.new(
                creatorId = testUser?.id ?: throw AssertionError("Test user not created"),
                agencyId = 3L,
                role = UserRole.BIZ_VALUE,
                deletedAt = null,
            )

        assertNotNull(consoleUser)
        assertEquals(UserRole.BIZ_VALUE, consoleUser.role)
        assertEquals(testUser.id, consoleUser.user?.id)
        assertEquals(3L, consoleUser.agencyId)
        assertEquals(null, consoleUser.deletedAt)
    }
}
