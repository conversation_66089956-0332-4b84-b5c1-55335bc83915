package jp.co.torihada.fanme.endpoints.console.agency

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.SecurityAttribute
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.exception.InvalidYearMonthFormatException
import jp.co.torihada.fanme.exception.PaymentServiceErrorException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.payment.controllers.responses.SellerAccountBalanceResponse
import org.hamcrest.Matchers.`is`
import org.junit.jupiter.api.Test
import org.mockito.Mockito.reset
import org.mockito.Mockito.times
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`

@QuarkusTest
class AgencyErrorHandlingTest : AgencyEndpointTestBase() {

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `無効な日付フォーマットを指定した場合はエラーが返ること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        val errorFromYearMonth = "2024-01"
        reset(sellerAccountBalanceController)
        reset(monthlySellerSalesController)

        `when`(
                sellerAccountBalanceController.getSellerAccountBalances(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2")
                )
            )
            .thenReturn(
                SellerAccountBalanceResponse.GetBalances(
                    balances =
                        listOf(
                            SellerAccountBalanceResponse.Balance(
                                sellerUserId = "creator-user-1",
                                accumulatedSales = 10000,
                                withdrawableAmount = 5000,
                            ),
                            SellerAccountBalanceResponse.Balance(
                                sellerUserId = "creator-user-2",
                                accumulatedSales = 20000,
                                withdrawableAmount = 10000,
                            ),
                        )
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                    errorFromYearMonth,
                    null,
                )
            )
            .thenAnswer {
                throw InvalidYearMonthFormatException(
                    "Invalid fromYearMonth format: $errorFromYearMonth. Expected format: YYYYMM"
                )
            }

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .queryParam("from", errorFromYearMonth)
                .`when`()
                .get("/console/agencies/$agencyId/sales")

        response
            .then()
            .statusCode(400)
            .body("errors[0].code", `is`(4004))
            .body(
                "errors[0].message",
                `is`("Invalid fromYearMonth format: $errorFromYearMonth. Expected format: YYYYMM"),
            )

        verify(monthlySellerSalesController, times(1))
            .getMonthlySellerSales(
                listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                errorFromYearMonth,
                null,
            )
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `月次売上データが見つからない場合は404エラーが返ること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        reset(sellerAccountBalanceController)
        reset(monthlySellerSalesController)

        `when`(
                sellerAccountBalanceController.getSellerAccountBalances(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2")
                )
            )
            .thenReturn(
                SellerAccountBalanceResponse.GetBalances(
                    balances =
                        listOf(
                            SellerAccountBalanceResponse.Balance(
                                sellerUserId = "creator-user-1",
                                accumulatedSales = 10000,
                                withdrawableAmount = 5000,
                            ),
                            SellerAccountBalanceResponse.Balance(
                                sellerUserId = "creator-user-2",
                                accumulatedSales = 20000,
                                withdrawableAmount = 10000,
                            ),
                        )
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                    null,
                    null,
                )
            )
            .thenAnswer { throw ResourceNotFoundException("MonthlySellerSales") }

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .`when`()
                .get("/console/agencies/$agencyId/sales")

        response
            .then()
            .statusCode(404)
            .body("errors[0].code", `is`(1000))
            .body("errors[0].message", `is`("MonthlySellerSales not found."))

        verify(monthlySellerSalesController, times(1))
            .getMonthlySellerSales(
                listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                null,
                null,
            )
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `残高データ取得でエラーが発生した場合は404エラーが返ること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        reset(sellerAccountBalanceController)
        reset(monthlySellerSalesController)
        `when`(
                sellerAccountBalanceController.getSellerAccountBalances(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2")
                )
            )
            .thenAnswer { throw ResourceNotFoundException("SellerAccountBalance") }

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .`when`()
                .get("/console/agencies/$agencyId/sales")

        response
            .then()
            .statusCode(404)
            .body("errors[0].code", `is`(1000))
            .body("errors[0].message", `is`("SellerAccountBalance not found."))

        verify(sellerAccountBalanceController, times(1))
            .getSellerAccountBalances(listOf("agent-user-1", "creator-user-1", "creator-user-2"))
    }

    @Test
    @TestSecurity(
        user = "super-user-1",
        roles = [UserRole.SUPER_VALUE],
        attributes = [SecurityAttribute(key = "login_user_uid", value = "super-user-1")],
    )
    fun `ペイメントサービスからのサーバーエラーが適切にハンドリングされること`() {
        val agency = Agency.findAll().firstResult()
        if (agency == null) {
            throw IllegalStateException("No agency found for test")
        }
        val agencyId = agency.id.toString()

        reset(sellerAccountBalanceController)
        reset(monthlySellerSalesController)

        `when`(
                sellerAccountBalanceController.getSellerAccountBalances(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2")
                )
            )
            .thenReturn(
                SellerAccountBalanceResponse.GetBalances(
                    balances =
                        listOf(
                            SellerAccountBalanceResponse.Balance(
                                sellerUserId = "creator-user-1",
                                accumulatedSales = 10000,
                                withdrawableAmount = 5000,
                            ),
                            SellerAccountBalanceResponse.Balance(
                                sellerUserId = "creator-user-2",
                                accumulatedSales = 20000,
                                withdrawableAmount = 10000,
                            ),
                        )
                )
            )

        `when`(
                monthlySellerSalesController.getMonthlySellerSales(
                    listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                    null,
                    null,
                )
            )
            .thenAnswer {
                throw PaymentServiceErrorException("Internal server error in payment service")
            }

        val response =
            given()
                .contentType(MediaType.APPLICATION_JSON)
                .`when`()
                .get("/console/agencies/$agencyId/sales")

        response
            .then()
            .statusCode(503)
            .body("errors[0].code", `is`(1202))
            .body("errors[0].message", `is`("Internal server error in payment service"))

        verify(monthlySellerSalesController, times(1))
            .getMonthlySellerSales(
                listOf("agent-user-1", "creator-user-1", "creator-user-2"),
                null,
                null,
            )
    }
}
