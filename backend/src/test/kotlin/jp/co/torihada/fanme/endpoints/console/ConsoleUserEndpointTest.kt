package jp.co.torihada.fanme.endpoints.console

import io.quarkus.test.junit.QuarkusTest
import io.quarkus.test.security.TestSecurity
import io.restassured.RestAssured.given
import jakarta.transaction.Transactional
import jakarta.ws.rs.core.MediaType
import jp.co.torihada.fanme.modules.console.const.UserRole
import jp.co.torihada.fanme.modules.console.factories.ConsoleUserFactory
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.fanme.factories.UserFactory
import jp.co.torihada.fanme.modules.fanme.models.User
import org.hamcrest.CoreMatchers.equalTo
import org.hamcrest.CoreMatchers.notNullValue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.TestInstance

@QuarkusTest
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ConsoleUserEndpointTest {

    @BeforeEach
    @Transactional
    fun setup() {
        ConsoleUser.deleteAll()
        User.delete("uid LIKE ?1", "test-user-%")

        val consoleSuperUser =
            UserFactory.createTestUser("test-user-1", "Super User", "<EMAIL>")
        val consoleBizUser =
            UserFactory.createTestUser("test-user-2", "Biz User", "<EMAIL>")
        val consoleCreatorUser =
            UserFactory.createTestUser("test-user-3", "Normal User", "<EMAIL>")

        val consoleUsers =
            listOf(
                    consoleSuperUser?.id?.let {
                        ConsoleUserFactory.new(it, null, UserRole.SUPER_VALUE)
                    },
                    consoleBizUser?.id?.let {
                        ConsoleUserFactory.new(it, null, UserRole.BIZ_VALUE)
                    },
                    consoleCreatorUser?.id?.let {
                        ConsoleUserFactory.new(it, null, UserRole.CREATOR_VALUE)
                    },
                )
                .filterNotNull()

        consoleUsers.forEach { it.persistAndFlush() }
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `SUPERロールでユーザー一覧を取得できること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users")
            .then()
            .statusCode(200)
            .body(notNullValue())
    }

    @Test
    @TestSecurity(user = "test-biz", roles = [UserRole.BIZ_VALUE])
    fun `BIZロールでユーザー一覧を取得できること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users")
            .then()
            .statusCode(200)
            .body(notNullValue())
    }

    @Test
    @TestSecurity(user = "test-agent", roles = [UserRole.AGENT_VALUE])
    fun `AGENTロールではユーザー一覧にアクセスできないこと`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users")
            .then()
            .statusCode(403)
    }

    @Test
    @TestSecurity(user = "test-creator", roles = [UserRole.CREATOR_VALUE])
    fun `CREATORロールではユーザー一覧にアクセスできないこと`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users")
            .then()
            .statusCode(403)
    }

    @Test
    fun `認証無しの場合は401エラーが返されること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users")
            .then()
            .statusCode(401)
    }

    @Test
    @TestSecurity(user = "test-super", roles = [UserRole.SUPER_VALUE])
    fun `セットアップしたデータが正しく返されること`() {
        given()
            .contentType(MediaType.APPLICATION_JSON)
            .`when`()
            .get("/console/users")
            .then()
            .statusCode(200)
            .body(
                "data.consoleUsers.find { it.role == '${UserRole.SUPER_VALUE}' }.role",
                equalTo(UserRole.SUPER_VALUE),
            )
            .body(
                "data.consoleUsers.find { it.role == '${UserRole.BIZ_VALUE}' }.role",
                equalTo(UserRole.BIZ_VALUE),
            )
            .body(
                "data.consoleUsers.find { it.role == '${UserRole.CREATOR_VALUE}' }.role",
                equalTo(UserRole.CREATOR_VALUE),
            )
            .body("data.consoleUsers.size()", equalTo(3))
    }
}
