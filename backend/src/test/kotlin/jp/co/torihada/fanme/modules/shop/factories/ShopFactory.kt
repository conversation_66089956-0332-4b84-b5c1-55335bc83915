package jp.co.torihada.fanme.modules.shop.factories

import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_MARGIN_RATE
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.shop.models.Shop

object ShopFactory {
    fun new(
        name: String = "ショップ名",
        tenant: String? = DEFAULT_TENANT,
        creatorUid: String? = "test-uid",
        description: String? = "ショップ説明",
        headerImageUri: String? = null,
        message: String = "TESTメッセージ",
        marginRate: Float = DEFAULT_MARGIN_RATE,
    ): Shop {
        return Shop().apply {
            this.name = name
            this.tenant = tenant ?: DEFAULT_TENANT
            this.creatorUid = creatorUid
            this.description = description
            this.headerImageUri = headerImageUri
            this.message = message
            this.marginRate = marginRate
            this.isOpen = true
        }
    }
}
