-- Monthly seller sales data for last 12 months
INSERT IGNORE INTO monthly_seller_sales (`tenant`, `seller_user_id`, `year_month`, `transaction_amount`, `miniapp_sales_amount`, `transfer_sales_amount`, `seller_sales_amount`, `gmo_sales_amount`, `developer_sales_amount`, `gmo_transfer_fee_amount`, `approved`, `merged`, `remaining_amount`, `expiration_date`, `transfer_status`, `created_at`, `updated_at`) VALUES 
-- Current month (merged = false)
('fanme', 'test-creator-1', DATE_FORMAT(NOW(), '%Y%m'), 50000, 5000, 10000, 35000, 1800, 5000, 250, true, false, 35000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(NOW(), '%Y%m'), 30000, 3000, 6000, 21000, 1080, 3000, 150, true, false, 21000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(NOW(), '%Y%m'), 20000, 2000, 4000, 14000, 720, 2000, 100, true, false, 14000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(NOW(), '%Y%m'), 40000, 4000, 8000, 28000, 1440, 4000, 200, true, false, 28000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(NOW(), '%Y%m'), 60000, 6000, 12000, 42000, 2160, 6000, 300, true, false, 42000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-6', DATE_FORMAT(NOW(), '%Y%m'), 75000, 7500, 15000, 52500, 2700, 7500, 375, true, false, 52500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-7', DATE_FORMAT(NOW(), '%Y%m'), 90000, 9000, 18000, 63000, 3240, 9000, 450, true, false, 63000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-8', DATE_FORMAT(NOW(), '%Y%m'), 100000, 10000, 20000, 70000, 3600, 10000, 500, true, false, 70000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
-- 1 month ago
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 45000, 4500, 9000, 31500, 1620, 4500, 225, true, true, 31500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 25000, 2500, 5000, 17500, 900, 2500, 125, true, true, 17500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 15000, 1500, 3000, 10500, 540, 1500, 75, true, true, 10500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 35000, 3500, 7000, 24500, 1260, 3500, 175, true, true, 24500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 55000, 5500, 11000, 38500, 1980, 5500, 275, true, true, 38500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-6', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 70000, 7000, 14000, 49000, 2520, 7000, 350, true, true, 49000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-7', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 85000, 8500, 17000, 59500, 3060, 8500, 425, true, true, 59500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-8', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y%m'), 95000, 9500, 19000, 66500, 3420, 9500, 475, true, true, 66500, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
-- 2 months ago
('fanme', 'test-creator-1', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 40000, 4000, 8000, 28000, 1440, 4000, 200, true, true, 28000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-2', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 20000, 2000, 4000, 14000, 720, 2000, 100, true, true, 14000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-3', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 10000, 1000, 2000, 7000, 360, 1000, 50, true, true, 7000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-4', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 30000, 3000, 6000, 21000, 1080, 3000, 150, true, true, 21000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW()),
('fanme', 'test-creator-5', DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 2 MONTH), '%Y%m'), 50000, 5000, 10000, 35000, 1800, 5000, 250, true, true, 35000, DATE_ADD(NOW(), INTERVAL 1 YEAR), '0', NOW(), NOW());

-- Seller account balances
INSERT IGNORE INTO seller_account_balances (`tenant`, `seller_user_id`, `amount`, `accumulated_sales`, `created_at`, `updated_at`) VALUES 
('fanme', 'test-creator-1', 50000, 100000, NOW(), NOW()),
('fanme', 'test-creator-2', 30000, 60000, NOW(), NOW()),
('fanme', 'test-creator-3', 20000, 40000, NOW(), NOW()),
('fanme', 'test-creator-4', 40000, 80000, NOW(), NOW()),
('fanme', 'test-creator-5', 60000, 120000, NOW(), NOW());