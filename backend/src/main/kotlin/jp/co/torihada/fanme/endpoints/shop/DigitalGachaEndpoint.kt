package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.annotation.security.RolesAllowed
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.BadgeRankingResponseBody
import jp.co.torihada.fanme.dto.CompleteBadgeResponseBody
import jp.co.torihada.fanme.dto.DigitalGachaPullResponseBody
import jp.co.torihada.fanme.dto.DigitalGachaPullableCountResponseBody
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.exception.UnAuthorizedException
import jp.co.torihada.fanme.modules.shop.controllers.DigitalGachaController
import jp.co.torihada.fanme.modules.shop.controllers.requests.DigitalGachaItemRequest
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/digital-gacha")
class DigitalGachaEndpoint {
    @Inject private lateinit var securityIdentity: SecurityIdentity
    @Inject private lateinit var handler: DigitalGachaController
    @Inject private lateinit var util: Util

    @POST
    @Path("/pull")
    @RolesAllowed("LoginUser")
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(DigitalGachaPullResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun pull(requestBody: DigitalGachaItemRequest.PullDigitalGachaRequest): Response {
        return try {
            val purchaserUid =
                util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.pullDigitalGacha(requestBody.itemId, purchaserUid)
            val entity = ResponseEntity(result, "files")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{item_id}/pullable-count")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(DigitalGachaPullableCountResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getPullableGachaCount(@PathParam("item_id") itemId: Long): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getPullableGachaCount(userUid, itemId)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{item_id}/complete-badge")
    @RolesAllowed("LoginUser")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(CompleteBadgeResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getCompleteBadge(@PathParam("item_id") itemId: Long): Response {
        return try {
            val userUid = util.getCurrentUserUid(securityIdentity) ?: throw UnAuthorizedException()
            val result = handler.getDigitalGachaCompleteBadge(userUid, itemId)
            val entity = ResponseEntity(result, "badge")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{item_id}/complete-badge-ranking")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(BadgeRankingResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getCompleteBadgeRanking(@PathParam("item_id") itemId: Long): Response {
        return try {
            val result = handler.getDigitalGachaCompleteBadgeRanking(itemId)
            val entity = ResponseEntity(result, "ranking")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
