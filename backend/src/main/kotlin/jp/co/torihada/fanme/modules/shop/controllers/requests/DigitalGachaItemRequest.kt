package jp.co.torihada.fanme.modules.shop.controllers.requests

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.enterprise.context.ApplicationScoped
import jakarta.validation.constraints.*
import jp.co.torihada.fanme.modules.shop.Const.FILE_NAME_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.MAX_PRICE
import jp.co.torihada.fanme.modules.shop.Const.MIN_PRICE
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.AVAILABLE_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.CREATOR_UID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.DESCRIPTION_TOO_MANY_LENGTH_100
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.DESCRIPTION_TOO_MANY_LENGTH_800
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.DISCOUNT_RATE_MUST_BE_LESS_THAN_99
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.DISCOUNT_RATE_MUST_BE_OR_MORE_1
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.FILES_IS_INVALID_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.FILES_IS_REQUIRED_AT_LEAST_ONE
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.FILE_TYPE_IS_INVALID_PATTERN
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.FILE_TYPE_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.IS_DUPLICATED_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.NAME_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.NAME_TOO_MANY_LENGTH_100
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.NAME_TOO_MANY_LENGTH_30
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.PASSWORD_IS_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.PRICE_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.PRICE_MUST_BE_LESS_THAN_1000000
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.PRICE_MUST_BE_OR_MORE_100
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.PROBABILITY_MUST_BE_LESS_THAN_OR_EQUAL_TO_100
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.PROBABILITY_MUST_BE_POSITIVE
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.QTY_TOTAL_MUST_BE_LESS_THAN_1000
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.QTY_TOTAL_MUST_BE_POSITIVE
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.SIZE_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.SIZE_MUST_BE_POSITIVE
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.THUMBNAIL_URI_IS_INVALID_URL
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.THUMBNAIL_URI_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.URI_IS_INVALID_URL
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.URI_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.models.ItemType
import org.hibernate.validator.constraints.URL

@ApplicationScoped
class DigitalGachaItemRequest {
    data class CreateItem(
        @NotBlank(message = CREATOR_UID_IS_REQUIRED)
        @Size(max = USER_UID_MAX_LENGTH, message = CREATOR_UID_TOO_MANY_LENGTH)
        val creatorUid: String,
        @NotBlank(message = NAME_IS_REQUIRED)
        @Size(max = 100, message = NAME_TOO_MANY_LENGTH_100)
        val name: String,
        @Size(max = 800, message = DESCRIPTION_TOO_MANY_LENGTH_800) val description: String?,
        @NotBlank(message = THUMBNAIL_URI_IS_REQUIRED) @URL val thumbnailUri: String,
        @NotNull @Min(0) @Max(1) val thumbnailFrom: Int,
        @NotNull @Min(0) @Max(2) val thumbnailBlurLevel: Int,
        @NotNull @Min(0) @Max(2) val thumbnailWatermarkLevel: Int,
        @NotNull(message = PRICE_IS_REQUIRED)
        @Min(MIN_PRICE, message = PRICE_MUST_BE_OR_MORE_100)
        @Max(MAX_PRICE, message = PRICE_MUST_BE_LESS_THAN_1000000)
        val price: Int,
        @NotBlank(message = FILE_TYPE_IS_REQUIRED)
        @NotNull(message = AVAILABLE_IS_REQUIRED)
        val available: Boolean,
        @Size(min = 1, message = FILES_IS_REQUIRED_AT_LEAST_ONE) val files: List<File>,
        val samples: List<SampleFile>?,
        val benefits: List<Benefit>?,
        val tags: List<String>?,
        val itemOption: ItemOption,
        val itemType: ItemType = ItemType.DIGITAL_GACHA,
        @NotNull(message = IS_DUPLICATED_IS_REQUIRED) val isDuplicated: Boolean,
        val awardProbabilities: List<AwardProbability>,
    )

    data class UpdateItem(
        val itemId: Long,
        @NotBlank(message = CREATOR_UID_IS_REQUIRED)
        @Size(max = USER_UID_MAX_LENGTH, message = CREATOR_UID_TOO_MANY_LENGTH)
        val creatorUid: String,
        @NotBlank(message = NAME_IS_REQUIRED)
        @Size(max = 100, message = NAME_TOO_MANY_LENGTH_100)
        val name: String,
        @Size(max = 800, message = DESCRIPTION_TOO_MANY_LENGTH_800) val description: String?,
        @NotBlank(message = THUMBNAIL_URI_IS_REQUIRED) @URL val thumbnailUri: String,
        @NotNull @Min(0) @Max(1) val thumbnailFrom: Int,
        @NotNull @Min(0) @Max(2) val thumbnailBlurLevel: Int,
        @NotNull @Min(0) @Max(2) val thumbnailWatermarkLevel: Int,
        val itemOption: ItemOption?,
        @NotNull(message = PRICE_IS_REQUIRED)
        @Min(MIN_PRICE, message = PRICE_MUST_BE_OR_MORE_100)
        @Max(MAX_PRICE, message = PRICE_MUST_BE_LESS_THAN_1000000)
        val price: Int,
        @Size(min = 1, message = FILES_IS_REQUIRED_AT_LEAST_ONE) val files: List<UpdateFile>?,
        @NotNull(message = AVAILABLE_IS_REQUIRED) val available: Boolean,
        val benefits: List<Benefit>?,
        val samples: List<SampleFile>?,
        val tags: List<String>?,
    )

    data class File(
        val id: Long?,
        @NotBlank(message = NAME_IS_REQUIRED)
        @Size(max = FILE_NAME_MAX_LENGTH, message = NAME_TOO_MANY_LENGTH_30)
        val name: String,
        @NotBlank(message = URI_IS_REQUIRED)
        @URL(message = URI_IS_INVALID_URL)
        val objectUri: String,
        @URL(message = THUMBNAIL_URI_IS_INVALID_URL) val thumbnailUri: String?,
        @URL(message = THUMBNAIL_URI_IS_INVALID_URL) val maskedThumbnailUri: String?,
        @Min(MIN_PRICE, message = PRICE_MUST_BE_OR_MORE_100)
        @Max(MAX_PRICE, message = PRICE_MUST_BE_LESS_THAN_1000000)
        val price: Int?,
        @NotBlank(message = FILE_TYPE_IS_REQUIRED)
        @Pattern(regexp = "^(image|audio|video|any)$", message = FILE_TYPE_IS_INVALID_PATTERN)
        val fileType: String,
        @NotNull(message = SIZE_IS_REQUIRED)
        @Min(0, message = SIZE_MUST_BE_POSITIVE)
        val size: Float,
        val duration: Int?,
        val itemThumbnailSelected: Boolean? = false,
        val sortOrder: Int?,
        val awardType: Int,
        val isSecret: Boolean,
    )

    data class UpdateFile(
        val id: Long,
        @NotBlank(message = NAME_IS_REQUIRED)
        @Size(max = FILE_NAME_MAX_LENGTH, message = NAME_TOO_MANY_LENGTH_30)
        val name: String,
        val isSecret: Boolean,
    )

    data class SampleFile(
        val id: Long?,
        @NotBlank(message = NAME_IS_REQUIRED)
        @Size(max = FILE_NAME_MAX_LENGTH, message = NAME_TOO_MANY_LENGTH_30)
        val name: String,
        @NotBlank(message = URI_IS_REQUIRED)
        @URL(message = URI_IS_INVALID_URL)
        val objectUri: String,
        @URL(message = THUMBNAIL_URI_IS_INVALID_URL) val thumbnailUri: String?,
        @Pattern(regexp = "^(image|audio|video|any)$", message = FILE_TYPE_IS_INVALID_PATTERN)
        val fileType: String,
        @NotNull(message = SIZE_IS_REQUIRED)
        @Min(0, message = SIZE_MUST_BE_POSITIVE)
        val size: Float,
        val duration: Int,
    )

    data class Benefit(
        val id: Long?,
        @Size(max = 100, message = DESCRIPTION_TOO_MANY_LENGTH_100) val description: String?,
        val conditionType: Int?,
        @Size(min = 1, max = 5, message = FILES_IS_INVALID_LENGTH) val files: List<BenefitFile>,
    )

    data class BenefitFile(
        val id: Long?,
        @NotBlank(message = NAME_IS_REQUIRED)
        @Size(max = FILE_NAME_MAX_LENGTH, message = NAME_TOO_MANY_LENGTH_30)
        val name: String,
        @NotBlank(message = URI_IS_REQUIRED)
        @URL(message = URI_IS_INVALID_URL)
        val objectUri: String,
        @URL(message = THUMBNAIL_URI_IS_INVALID_URL) val thumbnailUri: String?,
        @Pattern(regexp = "^(image|audio|video|any)$", message = FILE_TYPE_IS_INVALID_PATTERN)
        val fileType: String,
        @NotNull(message = SIZE_IS_REQUIRED)
        @Min(0, message = SIZE_MUST_BE_POSITIVE)
        val size: Float,
        val duration: Int?,
    )

    data class ItemOption(
        @Min(1, message = QTY_TOTAL_MUST_BE_POSITIVE)
        @Max(100000, message = QTY_TOTAL_MUST_BE_LESS_THAN_1000)
        val qtyTotal: Int?,
        val forSale: ForSale?,
        @Size(max = 20, message = PASSWORD_IS_TOO_MANY_LENGTH) val password: String?,
        val onSale: OnSale?,
    )

    data class ForSale(val startAt: String?, val endAt: String?)

    data class OnSale(
        @Min(1, message = DISCOUNT_RATE_MUST_BE_OR_MORE_1)
        @Max(99, message = DISCOUNT_RATE_MUST_BE_LESS_THAN_99)
        val discountRate: Float,
        val startAt: String?,
        val endAt: String?,
    )

    data class AwardProbability(
        @Min(0, message = PROBABILITY_MUST_BE_POSITIVE)
        @Max(100, message = PROBABILITY_MUST_BE_LESS_THAN_OR_EQUAL_TO_100)
        val probability: Int,
        val awardType: Int,
    )

    data class PullDigitalGachaRequest(@JsonProperty("itemId") val itemId: Long)
}
