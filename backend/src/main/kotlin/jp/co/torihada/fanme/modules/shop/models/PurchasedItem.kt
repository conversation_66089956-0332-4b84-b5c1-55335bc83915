package jp.co.torihada.fanme.modules.shop.models

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.Util.Companion.getEntityListWithPagination
import jp.co.torihada.fanme.modules.shop.Const.MAX_PRICE
import jp.co.torihada.fanme.modules.shop.Const.MIN_PRICE
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.Util.PurchasedItemStatus

@PersistenceUnit(name = "purchased_item")
@Entity
@Table(name = "purchased_items")
class PurchasedItem : BaseModel() {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "order_id", nullable = false, updatable = false)
    var order: Order = Order()

    @NotBlank
    @Column(name = "purchaser_uid", nullable = false, length = USER_UID_MAX_LENGTH)
    var purchaserUid: String = ""

    @NotNull
    @ManyToOne
    @JoinColumn(name = "item_id", nullable = false, updatable = false)
    @JsonIgnoreProperties("shop", "files")
    var item: Item = Item()

    @ManyToOne
    @JoinColumn(name = "item_file_id", nullable = true, updatable = false)
    @JsonIgnoreProperties("item")
    var itemFile: ItemFile? = null

    @Min(MIN_PRICE) @Max(MAX_PRICE) @Column(name = "price") var price: Int = 0

    @Min(1) @Column(name = "quantity") var quantity: Int = 1

    @Column(name = "purchaser_comment", columnDefinition = "TEXT")
    var purchaserComment: String? = null

    @NotBlank
    @Column(nullable = false, length = 10)
    var status: String = Util.PurchasedItemStatus.REQSUCCESS.value

    @Column(name = "purchased_at") var purchasedAt: Instant? = null

    @OneToMany(mappedBy = "purchasedItem", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var gachaReceivedFile: MutableSet<GachaReceivedFile> = mutableSetOf()

    companion object : PanacheCompanion<PurchasedItem> {

        fun findByOrderId(orderId: Long): List<PurchasedItem> {
            return find("order.id", orderId).list()
        }

        fun findByItemIdAndPurchaserUid(itemId: Long, purchaserUid: String): List<PurchasedItem> {
            return find("item.id = ?1 AND purchaserUid = ?2", itemId, purchaserUid).list()
        }

        fun findPaySuccessItemsByItemIdAndPurchaserUid(
            itemId: Long,
            purchaserUid: String,
        ): List<PurchasedItem> {
            return find(
                    "item.id = ?1 AND purchaserUid = ?2 AND status = ?3",
                    itemId,
                    purchaserUid,
                    Util.PurchasedItemStatus.PAYSUCCESS.value,
                )
                .list()
        }

        fun findUnpulledGachaItemsByPurchaserUidAndItemId(
            purchaserUid: String,
            itemId: Long,
        ): List<PurchasedItem?> {
            // 既にガチャを引いたアイテムは除外するクエリを書く
            val query =
                """
    SELECT pi
    FROM PurchasedItem pi
    LEFT JOIN pi.gachaReceivedFile grf
    WHERE pi.purchaserUid = ?1 and pi.status = "${Util.PurchasedItemStatus.PAYSUCCESS.value}"
    AND pi.item.id = ?2
    GROUP BY pi
    HAVING COUNT(grf.id) < pi.quantity OR COUNT(grf.id) = 0
"""

            return find(query, purchaserUid, itemId).list()
        }

        fun findValidItemsByPurchaserUid(
            purchaserUid: String,
            top: Int?,
            skip: Int?,
        ): List<PurchasedItem> {
            val find =
                find(
                    "purchaserUid = ?1 AND status in ?2 ORDER BY createdAt DESC",
                    purchaserUid,
                    listOf(
                        Util.PurchasedItemStatus.PAYSUCCESS.value,
                        Util.PurchasedItemStatus.REQSUCCESS.value,
                    ),
                )
            return getEntityListWithPagination(find, top, skip) as List<PurchasedItem>
        }

        fun findOrderedChekiItemByTerms(
            startDateTime: Instant,
            endDateTime: Instant,
        ): List<PurchasedItem> {
            return find(
                    "item.itemType = ?1 AND status = ?2 AND purchasedAt >= ?3 AND purchasedAt < ?4",
                    ItemType.CHEKI,
                    PurchasedItemStatus.PAYSUCCESS.value,
                    startDateTime,
                    endDateTime,
                )
                .list()
        }

        fun countPurchasedItem(itemId: Long): Int {
            return find(
                    "item.id = ?1 AND status in ?2",
                    itemId,
                    listOf(
                        Util.PurchasedItemStatus.PAYSUCCESS.value,
                        Util.PurchasedItemStatus.REQSUCCESS.value,
                    ),
                )
                .list()
                .sumOf { it.quantity }
        }

        fun countPurchasedItemByPurchaserUid(itemId: Long, purchaserUid: String): Int {
            return find(
                    "item.id = ?1 AND purchaserUid = ?2 AND status in ?3",
                    itemId,
                    purchaserUid,
                    listOf(
                        Util.PurchasedItemStatus.PAYSUCCESS.value,
                        Util.PurchasedItemStatus.REQSUCCESS.value,
                    ),
                )
                .list()
                .sumOf { it.quantity }
        }

        fun isPurchased(itemId: Long, uid: String, itemFileId: Long?): Boolean {
            if (itemFileId != null) {
                return count(
                    "item.id = ?1 AND itemFile.id = ?2 AND purchaserUid = ?3 AND status = ?4",
                    itemId,
                    itemFileId,
                    uid,
                    Util.PurchasedItemStatus.PAYSUCCESS.value,
                ) > 0
            }
            return count(
                "item.id = ?1 AND itemFile IS null AND purchaserUid = ?2 AND status = ?3",
                itemId,
                uid,
                Util.PurchasedItemStatus.PAYSUCCESS.value,
            ) > 0
        }

        fun isCheckout(itemId: Long, uid: String, itemFileId: Long?): Boolean {
            if (itemFileId != null) {
                return count(
                    "item.id = ?1 AND itemFile.id = ?2 AND purchaserUid = ?3 AND status = ?4",
                    itemId,
                    itemFileId,
                    uid,
                    Util.PurchasedItemStatus.REQSUCCESS.value,
                ) > 0
            }
            return count(
                "item.id = ?1 AND itemFile IS null AND purchaserUid = ?2 AND status = ?3",
                itemId,
                uid,
                Util.PurchasedItemStatus.REQSUCCESS.value,
            ) > 0
        }

        fun create(
            orderId: Long,
            purchaserUid: String,
            itemId: Long,
            itemFileId: Long?,
            price: Int,
            quantity: Int,
            status: String,
            purchaserComment: String? = null,
        ): PurchasedItem {
            val purchasedItem = PurchasedItem()
            purchasedItem.order = Order.findById(orderId)!!
            purchasedItem.purchaserUid = purchaserUid
            purchasedItem.item = Item.findById(itemId)!!
            purchasedItem.itemFile = if (itemFileId != null) ItemFile.findById(itemFileId) else null
            purchasedItem.price = price
            purchasedItem.quantity = quantity
            purchasedItem.status = status
            purchasedItem.purchasedAt =
                if (status == PurchasedItemStatus.PAYSUCCESS.value) {
                    Instant.now()
                } else {
                    null
                }
            purchasedItem.purchaserComment = purchaserComment
            purchasedItem.persist()
            return purchasedItem
        }

        fun updatePurchasedItemStatus(purchasedItem: PurchasedItem, status: String): PurchasedItem {
            purchasedItem.status = status
            purchasedItem.purchasedAt =
                if (status == PurchasedItemStatus.PAYSUCCESS.value) {
                    Instant.now()
                } else {
                    null
                }
            purchasedItem.persistAndFlush()
            return purchasedItem
        }
    }
}
