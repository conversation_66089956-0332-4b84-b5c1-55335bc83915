package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.Clock
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.externals.BaseExternals
import jp.co.torihada.fanme.modules.payment.externals.client.auth.ExtAuthClient
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer
import jp.co.torihada.fanme.modules.shop.services.EmailService
import org.eclipse.microprofile.rest.client.inject.RestClient
import org.jboss.logging.Logger

@ApplicationScoped
class TransferDeadLineContactBatch {
    @Inject private lateinit var logger: Logger
    private val today = LocalDate.now(Clock.systemUTC())
    @Inject private lateinit var emailService: EmailService
    @Inject @RestClient private lateinit var extAuthClient: ExtAuthClient
    @Inject private lateinit var baseExternals: BaseExternals

    fun execute() {
        // メール送信日
        val after30 = today.plusDays(30).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        val after14 = today.plusDays(14).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
        val after1 = today.plusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

        val dataList = listOf(after30, after14, after1)
        val data = MonthlySellerSale.findByExpirationFateNotTransferStatus(dataList)

        data.forEach {
            val sellerAccountBalance = SellerAccountBalance.findBySellerUserId(it.sellerUserId)
            if (sellerAccountBalance != null) {
                if (sellerAccountBalance.amount >= 441) {
                    val sellerGmoTransfer =
                        SellerGmoTransfer.findBySellerUserIdLast(it.sellerUserId)

                    // 一回も送金を行ったことがないかたの場合
                    // 送金指示は行ったが最新の送金が失敗している人の場合
                    if (
                        sellerGmoTransfer == null ||
                            sellerGmoTransfer.status !=
                                Const.GmoTransferStatus.TransferCompleted.value
                    ) {
                        try {
                            val authorization = baseExternals.getAuthorization()
                            val userInfo =
                                extAuthClient.retrieveAuthInfo(
                                    uuid = it.sellerUserId,
                                    authorization = authorization,
                                )
                            if (userInfo.email == null) {
                                return@forEach
                            }
                            emailService.sendTransferDeadlineContactEmail(
                                sellerEmail = userInfo.email,
                                sellerName = userInfo.name,
                            )
                        } catch (e: Exception) {
                            if (sellerGmoTransfer != null) {
                                logger.error(
                                    "Failed to send email to seller: ${it.sellerUserId} ",
                                    e,
                                )
                            }
                        }
                    }
                }
            }
        }

        return
    }
}
