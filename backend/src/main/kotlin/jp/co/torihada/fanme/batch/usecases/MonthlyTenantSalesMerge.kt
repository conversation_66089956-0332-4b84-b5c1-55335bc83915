package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.MonthlyTenantSale
import jp.co.torihada.fanme.modules.payment.models.TenantAccountActivity
import jp.co.torihada.fanme.modules.payment.models.TenantAccountBalance
import org.jboss.logging.Logger

@ApplicationScoped
class MonthlyTenantSalesMerge {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var config: Config

    fun execute(yearMonth: String) {
        logger.info("MonthlyTenantSalesMerge start yearMonth: $yearMonth")
        val monthlyTenantSale =
            MonthlyTenantSale.findByTenantAndYearMonthAndMerge(
                config.tenant(),
                yearMonth,
                merged = false,
            )

        if (monthlyTenantSale == null) {
            logger.info("No monthly tenant sales data for the specified month.")
            return
        }

        val tenantAccountBalance = TenantAccountBalance.findByTenant(config.tenant())
        // テナントの現在の残高
        val currentBalanceAmount = tenantAccountBalance?.amount ?: 0
        // 前月分を加算した残高
        val nextBalanceAmount = currentBalanceAmount + monthlyTenantSale.miniappSalesAmount

        TenantAccountActivity().apply {
            this.tenant = config.tenant()
            this.activityType = Const.AccountActivityType.Payment.value
            this.activityCode = Const.AccountActivityCode.AppSales.value
            this.yearMonth = yearMonth
            this.amount = monthlyTenantSale.miniappSalesAmount
            this.balance = currentBalanceAmount + monthlyTenantSale.miniappSalesAmount
        }

        if (tenantAccountBalance == null) {
            TenantAccountBalance().apply {
                this.tenant = config.tenant()
                this.amount = nextBalanceAmount
            }
        } else {
            tenantAccountBalance.amount = nextBalanceAmount
        }

        // 残高への取り込み済みフラグをTrueにする
        monthlyTenantSale.merged = true

        logger.info(
            "miniapp sale amount=${monthlyTenantSale.miniappSalesAmount}, " +
                "developer sale amount=${monthlyTenantSale.developerSalesAmount}, " +
                "balance=$currentBalanceAmount->$nextBalanceAmount"
        )

        logger.info("MonthlyTenantSalesMerge end yearMonth: $yearMonth")
    }
}
