package jp.co.torihada.fanme.modules.console.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.exception.ConsoleException
import jp.co.torihada.fanme.exception.ConsoleResourceNotFoundException
import jp.co.torihada.fanme.modules.console.dto.ConsoleUserDetail
import jp.co.torihada.fanme.modules.fanme.models.User

data class GetUserUseCaseInput(val id: Long)

interface IGetUser {
    fun execute(input: GetUserUseCaseInput): Result<ConsoleUserDetail, ConsoleException>
}

@ApplicationScoped
class GetUser @Inject constructor() : IGetUser {

    override fun execute(input: GetUserUseCaseInput): Result<ConsoleUserDetail, ConsoleException> {

        val user =
            // console-test-ignore
            User.findById(input.id)
                ?: return Err(
                    ConsoleResourceNotFoundException(
                        "User (User) with ID ${input.id} was not found."
                    )
                )

        val consoleUser =
            user.consoleUser
                ?: return Err(
                    ConsoleResourceNotFoundException(
                        "Console user with User id ${user.id} was not found."
                    )
                )

        return Ok(
            ConsoleUserDetail(
                id = user.id!!,
                uid = user.uid,
                name = user.name!!,
                accountIdentity = user.accountIdentity!!,
                isPublic = user.isPublic,
                birthday = user.birthday,
                birthdayConfirmed = user.birthdayConfirmed,
                isBirthdayWeek = user.isBirthdayWeek,
                gender = user.gender,
                icon = user.iconUrl,
                filledProfile = user.filledProfile,
                allowPublicSharing = user.allowPublicSharing,
                purpose = user.purpose,
                role = consoleUser.role,
                agencyId = consoleUser.agencyId,
            )
        )
    }
}
