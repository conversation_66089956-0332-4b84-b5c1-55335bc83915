package jp.co.torihada.fanme.endpoints.shop.request.item.digitalGacha.create

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle.*
import jp.co.torihada.fanme.modules.shop.controllers.requests.DigitalGachaItemRequest

data class CreateDigitalGachaItemRequest(
    val name: String,
    val description: String?,
    val thumbnailUri: String,
    val thumbnailFrom: Int,
    val thumbnailBlurLevel: Int,
    val thumbnailWatermarkLevel: Int,
    val price: Int,
    val available: Boolean,
    val itemFiles: List<DigitalGachaFile>,
    val samples: List<DigitalGachaSampleFile>?,
    val benefits: List<Benefit>?,
    val tags: List<String>?,
    val itemOption: ItemOption,
    val isDuplicated: Boolean,
    val awardProbabilities: List<AwardProbability>,
)

data class DigitalGachaFile(
    val id: Long?,
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val maskedThumbnailUri: String?,
    val price: Int?,
    val fileType: String,
    val size: Float,
    val duration: Int,
    val itemThumbnailSelected: Boolean?,
    val sortOrder: Int?,
    val awardType: Int,
    val isSecret: Boolean?,
)

data class DigitalGachaSampleFile(
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
)

data class AwardProbability(val awardType: Int, val probability: Int)

@ApplicationScoped
class CreateDigitalGachaItemRequestConverter() {
    fun requestToCreateItem(
        uid: String,
        request: CreateDigitalGachaItemRequest,
    ): DigitalGachaItemRequest.CreateItem {
        return DigitalGachaItemRequest.CreateItem(
            creatorUid = uid,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            price = request.price,
            available = request.available,
            files = convertFiles(request.itemFiles),
            samples = request.samples?.let { convertSampleFiles(it) },
            benefits = request.benefits?.let { convertBenefit(it) },
            tags = request.tags,
            itemOption = convertItemOption(request.itemOption),
            isDuplicated = request.isDuplicated,
            awardProbabilities = convertAwardProbabilities(request.awardProbabilities),
        )
    }

    private fun convertAwardProbabilities(
        request: List<AwardProbability>
    ): List<DigitalGachaItemRequest.AwardProbability> {
        return request.map {
            DigitalGachaItemRequest.AwardProbability(
                awardType = it.awardType,
                probability = it.probability,
            )
        }
    }

    private fun convertFiles(request: List<DigitalGachaFile>): List<DigitalGachaItemRequest.File> {
        return request.map {
            DigitalGachaItemRequest.File(
                id = it.id,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                maskedThumbnailUri = it.maskedThumbnailUri,
                price = it.price,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
                itemThumbnailSelected = it.itemThumbnailSelected,
                sortOrder = it.sortOrder,
                awardType = it.awardType,
                isSecret = it.isSecret ?: false,
            )
        }
    }

    private fun convertSampleFiles(
        request: List<DigitalGachaSampleFile>
    ): List<DigitalGachaItemRequest.SampleFile> {
        return request.map {
            DigitalGachaItemRequest.SampleFile(
                id = null,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertBenefit(request: List<Benefit>): List<DigitalGachaItemRequest.Benefit> {
        return request.map {
            DigitalGachaItemRequest.Benefit(
                id = null,
                description = it.description,
                conditionType = it.conditionType,
                files = convertBenefitFiles(it.files),
            )
        }
    }

    private fun convertBenefitFiles(
        request: List<BenefitFile>
    ): List<DigitalGachaItemRequest.BenefitFile> {
        return request.map {
            DigitalGachaItemRequest.BenefitFile(
                id = null,
                name = it.name,
                objectUri = it.objectUri.substringBefore("?"),
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertForSale(request: ForSale?): DigitalGachaItemRequest.ForSale? {
        return request?.let {
            DigitalGachaItemRequest.ForSale(startAt = request.startAt, endAt = request.endAt)
        }
    }

    private fun convertOnSale(request: OnSale?): DigitalGachaItemRequest.OnSale? {
        return request?.let {
            DigitalGachaItemRequest.OnSale(
                discountRate = request.discountRate,
                startAt = request.startAt,
                endAt = request.endAt,
            )
        }
    }

    private fun convertItemOption(request: ItemOption): DigitalGachaItemRequest.ItemOption {
        return DigitalGachaItemRequest.ItemOption(
            qtyTotal = request.qtyTotal,
            forSale = convertForSale(request.forSale),
            password = request.password,
            onSale = convertOnSale(request.onSale),
        )
    }
}
