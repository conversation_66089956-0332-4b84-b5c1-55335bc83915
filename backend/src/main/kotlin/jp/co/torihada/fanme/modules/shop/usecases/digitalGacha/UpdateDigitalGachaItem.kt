package jp.co.torihada.fanme.modules.shop.usecases.digitalGacha

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.models.BenefitFile as BenefitFileModel
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.CreateDigitalGachaItem.OnSale

@ApplicationScoped
class UpdateDigitalGachaItem {
    data class Input(
        val itemId: Long,
        val creatorUid: String,
        val name: String,
        val description: String?,
        val thumbnailUri: String,
        val thumbnailFrom: Int,
        val thumbnailBlurLevel: Int,
        val thumbnailWatermarkLevel: Int,
        val available: Boolean,
        val price: Int,
        val itemFiles: List<File>?,
        val itemOption: ItemOptionParam,
        val benefits: List<BenefitParam>?,
        val sample: List<SampleFile>?,
        val tags: List<String>?,
    )

    data class ItemOptionParam(
        val password: String?,
        val onSale: OnSale?,
        val forSale: CreateDigitalGachaItem.ForSale?,
    )

    data class BenefitParam(val id: Long, val description: String?, val files: List<BenefitFile>)

    data class File(val id: Long?, val name: String, val isSecret: Boolean)

    data class SampleFile(
        val id: Long?,
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
    )

    data class BenefitFile(
        val id: Long?,
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
    )

    fun execute(params: Input): Result<Item, FanmeException> {
        // shopチェック
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))

        // itemIdが存在するか確認
        // itemTypeがDigitalGachaか確認
        var item = Item.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))
        if (item.itemType != ItemType.DIGITAL_GACHA) {
            return Err(ResourceNotFoundException("Item"))
        }

        item =
            Item.update(
                id = params.itemId,
                name = params.name,
                description = params.description,
                thumbnailUri = params.thumbnailUri,
                thumbnailFrom = params.thumbnailFrom,
                thumbnailBlurLevel = params.thumbnailBlurLevel,
                thumbnailWatermarkLevel = params.thumbnailWatermarkLevel,
                price = params.price,
                fileType = 0,
                available = params.available,
            )

        params.itemFiles?.map {
            if (it.id != null) {
                val file = ItemFile.findById(it.id)
                if (file != null) {
                    ItemFile.update(
                        id = it.id,
                        name = it.name,
                        objectUri = file.objectUri!!,
                        thumbnailUri = file.thumbnailUri,
                        maskedThumbnailUri = file.maskedThumbnailUri,
                        price = file.price,
                        fileType = file.fileType,
                        size = file.size,
                        duration = file.duration,
                        itemThumbnailSelected = file.itemThumbnailSelected,
                        sortOrder = file.sortOrder,
                    )
                }
                GachaItemFile.updateByItemFileId(it.id, it.isSecret)
            }
        }

        val files = ItemFile.findByItemId(item.id!!)
        val itemFileType = Util.getItemFileType(files)
        item.fileType = itemFileType
        item.persist()

        // ガチャの特典は特典のdescriptionと中のファイルのみ変更可能
        if (params.benefits != null) {
            params.benefits.forEach {
                val benefit = Benefit.findById(it.id)
                if (benefit != null) {
                    Benefit.update(id = it.id, description = it.description)
                    it.files.forEach {
                        if (it.id != null) {
                            val benefitFile = BenefitFileModel.findById(it.id)
                            benefitFile?.let {
                                BenefitFileModel.update(
                                    id = it.id!!,
                                    name = it.name,
                                    objectUri = it.objectUri,
                                    thumbnailUri = it.thumbnailUri,
                                    fileType = it.fileType,
                                    size = it.size,
                                    duration = it.duration ?: 0,
                                )
                            }
                        } else {
                            BenefitFileModel.create(
                                benefitId = benefit.id!!,
                                name = it.name,
                                objectUri = it.objectUri,
                                thumbnailUri = it.thumbnailUri,
                                fileType = it.fileType,
                                size = it.size,
                                duration = it.duration ?: 0,
                            )
                        }
                    }
                }
            }
        }

        val option = ItemOption.findByItemId(item.id!!)
        ItemOption.updateByItemId(
            item.id!!,
            isSingleSales = false,
            password =
                if (params.itemOption.password.isNullOrEmpty()) option?.password
                else params.itemOption.password,
            forSaleStartAt = params.itemOption.forSale?.startAt,
            forSaleEndAt = params.itemOption.forSale?.endAt,
            qtyTotal = option?.qtyTotal,
            qtyPerUser = null,
        )

        val existingOnSale = item.onSale
        params.itemOption.onSale?.let { newOnSale ->
            when (existingOnSale) {
                null ->
                    ItemOnSale.create(
                        item.id!!,
                        newOnSale.discountRate,
                        newOnSale.startAt,
                        newOnSale.endAt,
                    )
                else ->
                    ItemOnSale.updateByItemId(
                        item.id!!,
                        newOnSale.discountRate,
                        newOnSale.startAt,
                        newOnSale.endAt,
                    )
            }
        } ?: existingOnSale?.let { ItemOnSale.updateByItemId(item.id!!, 0.0f, null, null) }

        var samples: List<Sample>? = null
        if (params.sample != null) {
            val sampleIdsToDelete =
                item.samples
                    .filter { existingSample ->
                        params.sample.none { paramSample -> paramSample.id == existingSample.id }
                    }
                    .mapNotNull { it.id }
            Sample.deleteByIds(sampleIdsToDelete)

            samples =
                params.sample.map {
                    Sample.upsert(
                        item.id!!,
                        it.id,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        it.fileType,
                        it.size,
                        it.duration,
                    )
                }
        }

        item.tags.forEach() {
            if (params.tags?.find { t -> t == it.tag.tag } == null) {
                ItemTag.deleteById(it.id!!)
            }
        }
        params.tags?.forEach {
            if (item.tags.find { t -> t.tag.tag == it } == null) {
                Tag.create(shop.id!!, it)
            }
            ItemTag.create(item.id!!, it)
        }

        return Ok(item)
    }
}
