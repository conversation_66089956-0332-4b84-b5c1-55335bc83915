package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.LocalDateTime
import java.time.ZoneId
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.*
import org.jboss.logging.Logger

@ApplicationScoped
class SellerSalesExpire {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var config: Config

    fun execute() {
        logger.info("start SellerSalesExpire")
        // 日本時間取得
        val now = LocalDateTime.now(ZoneId.of("Asia/Tokyo"))

        // 期限切れのデータを取得
        val monthlySellerSales =
            MonthlySellerSale.findByExpirationDataNotTransfer(
                now.atZone(ZoneId.of("Asia/Tokyo")).toInstant()
            )

        val monthlySellerSalesCount = monthlySellerSales.count()
        // yearMonthごとにグループ化し、それぞれのカウントをログに出力
        val groupedData = monthlySellerSales.groupBy { it.yearMonth }

        // 期限切れのデータの総数をログに出力
        logger.info("monthlySellerSalesCount: $monthlySellerSalesCount")

        // 各　yearMonthごとの期限切れデータの数をログに出力
        groupedData.forEach { (yearMonth, data) ->
            logger.info("yearMonth: $yearMonth, count: ${data.count()}")
        }

        monthlySellerSales.forEach {
            val sellerUserId = it.sellerUserId
            var remainingAmount = it.remainingAmount ?: 0

            // クリエイターの残高データを取得
            val sellerAccountBalance =
                SellerAccountBalance.findBySellerUserId(sellerUserId!!)
                    ?: run {
                        logger.error("sellerAccountBalance not found. sellerUserId: $sellerUserId")
                        return@forEach
                    }

            // テナントに金額を追加
            val tenantAccountBalance = TenantAccountBalance.findByTenant(config.tenant())
            if (tenantAccountBalance != null) {

                // ステータスを失効に変更
                it.transferStatus = Const.MonthlySellerSaleStatus.Expired.value
                it.remainingAmount = 0

                // クリエイターの売上から金額を引く
                // 売上は0円以下にならないようにする
                if ((sellerAccountBalance.amount ?: 0) < (remainingAmount ?: 0)) {
                    remainingAmount = sellerAccountBalance.amount ?: 0
                    sellerAccountBalance.amount = 0
                } else {
                    sellerAccountBalance.amount = sellerAccountBalance.amount.minus(remainingAmount)
                }

                if (remainingAmount > 0) {
                    SellerAccountActivity()
                        .apply {
                            this.tenant = config.tenant()
                            this.sellerUserId = sellerUserId
                            this.amount = remainingAmount
                            this.activityType = Const.AccountActivityType.Withdrawal.value
                            this.activityCode = Const.AccountActivityCode.SalesExpired.value
                            this.yearMonth = it.yearMonth
                        }
                        .persist()

                    tenantAccountBalance.amount = tenantAccountBalance.amount.plus(remainingAmount)

                    TenantAccountActivity()
                        .apply {
                            this.tenant = config.tenant()
                            this.activityType = Const.AccountActivityType.Payment.value
                            this.activityCode = Const.AccountActivityCode.SalesExpired.value
                            this.amount = remainingAmount
                            this.balance = tenantAccountBalance.amount
                            this.yearMonth = it.yearMonth
                        }
                        .persist()
                }
            }
        }

        logger.info("end SellerSalesExpire")
    }
}
