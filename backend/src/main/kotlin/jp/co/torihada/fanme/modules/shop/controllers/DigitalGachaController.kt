package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jp.co.torihada.fanme.modules.fanme.Config as FanmeConfig
import jp.co.torihada.fanme.modules.fanme.Const
import jp.co.torihada.fanme.modules.fanme.controllers.UserController
import jp.co.torihada.fanme.modules.shop.Util.Companion.stringToInstantOnJST
import jp.co.torihada.fanme.modules.shop.annotations.Maskable
import jp.co.torihada.fanme.modules.shop.controllers.requests.DigitalGachaItemRequest
import jp.co.torihada.fanme.modules.shop.models.AwardType
import jp.co.torihada.fanme.modules.shop.models.BadgeType
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.usecases.badge.CreateDigitalGachaCompleteBadge
import jp.co.torihada.fanme.modules.shop.usecases.badge.GetBadge
import jp.co.torihada.fanme.modules.shop.usecases.badge.GetBadgeRanking
import jp.co.torihada.fanme.modules.shop.usecases.benefitLog.CreateBenefitLog
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.CreateDigitalGachaItem
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.CreateDigitalGachaItem.AwardProbability
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.GetPullableGachaCount
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.PullDigitalGachaItems
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.UpdateDigitalGachaItem
import jp.co.torihada.fanme.modules.shop.utils.MaskingProcessor

@ApplicationScoped
class DigitalGachaController {

    @Inject private lateinit var createDigitalGachaItem: CreateDigitalGachaItem
    @Inject private lateinit var updateDigitalGachaItem: UpdateDigitalGachaItem
    @Inject private lateinit var pullDigitalGachaItems: PullDigitalGachaItems

    @Inject private lateinit var getPullableGachaCount: GetPullableGachaCount
    @Inject private lateinit var createDigitalGachaCompleteBadge: CreateDigitalGachaCompleteBadge
    @Inject private lateinit var getDigitalgachaCompleteBadge: GetBadge
    @Inject private lateinit var createBenefitLog: CreateBenefitLog
    @Inject private lateinit var getDigitalGachaCompleteBadgeRanking: GetBadgeRanking
    @Inject private lateinit var userController: UserController
    @Inject private lateinit var fanmeConfig: FanmeConfig

    @Transactional
    fun createItem(@Valid item: DigitalGachaItemRequest.CreateItem): Item {
        return createDigitalGachaItem
            .execute(
                CreateDigitalGachaItem.Input(
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    price = item.price,
                    available = item.available,
                    isDuplicated = item.isDuplicated,
                    files = item.files.map { mapToCreateFile(it) },
                    samples = item.samples?.map { mapToCreateSampleFile(it) },
                    benefit =
                        item.benefits?.map {
                            CreateDigitalGachaItem.BenefitParam(
                                description = it.description,
                                conditionType = it.conditionType!!,
                                files = it.files.map { file -> mapToCreateBenefitFile(file) },
                            )
                        },
                    tags = item.tags,
                    itemOption =
                        CreateDigitalGachaItem.ItemOptionParam(
                            qtyTotal = item.itemOption.qtyTotal,
                            forSale =
                                CreateDigitalGachaItem.ForSale(
                                    startAt =
                                        if (item.itemOption.forSale?.startAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.startAt),
                                    endAt =
                                        if (item.itemOption.forSale?.endAt == null) null
                                        else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                ),
                            password = item.itemOption.password,
                            onSale =
                                if (item.itemOption.onSale != null) {
                                    CreateDigitalGachaItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                    awardProbabilities =
                        item.awardProbabilities.map {
                            AwardProbability(
                                awardType = AwardType.fromValue(it.awardType),
                                probability = it.probability,
                            )
                        },
                )
            )
            .getOrElse { throw it }
    }

    fun getPullableGachaCount(userId: String, itemId: Long): GetPullableGachaCount.Output {
        return getPullableGachaCount
            .execute(GetPullableGachaCount.Input(purchaserUserId = userId, itemId = itemId))
            .getOrElse { throw it }
    }

    @Transactional
    fun updateItem(@Valid item: DigitalGachaItemRequest.UpdateItem): Item {
        return updateDigitalGachaItem
            .execute(
                UpdateDigitalGachaItem.Input(
                    itemId = item.itemId,
                    creatorUid = item.creatorUid,
                    name = item.name,
                    description = item.description,
                    thumbnailUri = item.thumbnailUri,
                    thumbnailFrom = item.thumbnailFrom,
                    thumbnailBlurLevel = item.thumbnailBlurLevel,
                    thumbnailWatermarkLevel = item.thumbnailWatermarkLevel,
                    available = item.available,
                    price = item.price,
                    itemFiles = item.files?.map { mapToUpdateFile(it) },
                    itemOption =
                        UpdateDigitalGachaItem.ItemOptionParam(
                            password = item.itemOption?.password,
                            onSale =
                                if (item.itemOption?.onSale != null) {
                                    CreateDigitalGachaItem.OnSale(
                                        discountRate = item.itemOption.onSale.discountRate,
                                        startAt =
                                            if (item.itemOption.onSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.onSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.onSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.onSale.endAt),
                                    )
                                } else {
                                    null
                                },
                            forSale =
                                if (item.itemOption?.forSale != null) {
                                    CreateDigitalGachaItem.ForSale(
                                        startAt =
                                            if (item.itemOption.forSale.startAt == null) null
                                            else
                                                stringToInstantOnJST(
                                                    item.itemOption.forSale.startAt
                                                ),
                                        endAt =
                                            if (item.itemOption.forSale.endAt == null) null
                                            else stringToInstantOnJST(item.itemOption.forSale.endAt),
                                    )
                                } else {
                                    null
                                },
                        ),
                    tags = item.tags,
                    sample = item.samples?.map { mapToUpdateSampleFile(it) },
                    benefits =
                        item.benefits?.map {
                            UpdateDigitalGachaItem.BenefitParam(
                                id = it.id!!,
                                description = it.description,
                                files = it.files.map { file -> mapToUpdateBenefitFile(file) },
                            )
                        },
                )
            )
            .getOrElse { throw it }
    }

    @Transactional
    fun pullDigitalGacha(
        itemId: Long,
        purchaserUid: String,
        pullCount: Int? = 0,
    ): List<PullDigitalGachaItems.FileForPullDigitalGachaItems> {

        val result =
            pullDigitalGachaItems
                .execute(
                    PullDigitalGachaItems.Input(
                        itemId = itemId,
                        purchaserUid = purchaserUid,
                        pullCount = pullCount,
                    )
                )
                .getOrElse { throw it }

        // 特典取得
        createBenefitLog.execute(
            CreateBenefitLog.Input(purchaserUid = purchaserUid, itemId = itemId)
        )

        // ガチャコンプリートバッジ付与
        createDigitalGachaCompleteBadge.execute(
            CreateDigitalGachaCompleteBadge.Input(userUid = purchaserUid, itemId = itemId)
        )

        return result
    }

    // ガチャコンプリートバッジを持っているか確認
    fun getDigitalGachaCompleteBadge(userUid: String, itemId: Long): GetBadge.Output {
        return getDigitalgachaCompleteBadge
            .execute(
                GetBadge.Input(
                    userUid = userUid,
                    itemId = itemId,
                    badgeType = BadgeType.DIGITAL_GACHA_COMPLETE,
                )
            )
            .getOrElse { throw it }
    }

    data class GetDigitalGachaCompleteBadgeRankingResponse(
        val userUid: String,
        val userAccountIdentity: String,
        @Maskable val userName: String,
        val userIcon: String,
        val getBadgeAt: String,
    )

    fun getDigitalGachaCompleteBadgeRanking(
        itemId: Long
    ): List<GetDigitalGachaCompleteBadgeRankingResponse> {
        val badgeRanking =
            getDigitalGachaCompleteBadgeRanking
                .execute(
                    GetBadgeRanking.Input(
                        itemId = itemId,
                        badgeType = BadgeType.DIGITAL_GACHA_COMPLETE,
                        limit = 100, // 上位100件を取得
                    )
                )
                .getOrElse { throw it }

        val userUids = badgeRanking.map { it.userUid }.distinct()
        // ユーザー情報を取得して Map 化（uid をキーに）
        val users = userController.getUsers(userUids).associateBy { it.uid }

        return badgeRanking.map { ranking ->
            val user = users[ranking.userUid]
            val response =
                GetDigitalGachaCompleteBadgeRankingResponse(
                    userUid = ranking.userUid,
                    userAccountIdentity = user?.accountIdentity ?: UNKNOWN_USER_ACCOUNT_IDENTITY,
                    userName = user?.name ?: UNKNOWN_USER_NAME,
                    userIcon =
                        user?.iconUrl
                            ?: "${fanmeConfig.fanmeApiServerUrl()}/${Const.FANME_DEFAULT_CREATOR_ICON_PATH}",
                    getBadgeAt = ranking.createdAt.toString(),
                )
            MaskingProcessor.process(response)
        }
    }

    private fun mapToCreateFile(file: DigitalGachaItemRequest.File): CreateDigitalGachaItem.File {
        return CreateDigitalGachaItem.File(
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            maskedThumbnailUri = file.maskedThumbnailUri,
            price = file.price,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
            itemThumbnailSelected = file.itemThumbnailSelected ?: false,
            sortOrder = file.sortOrder ?: 0,
            awardType = AwardType.fromValue(file.awardType),
            isSecret = file.isSecret,
        )
    }

    private fun mapToCreateSampleFile(
        file: DigitalGachaItemRequest.SampleFile
    ): CreateDigitalGachaItem.SampleFile {
        return CreateDigitalGachaItem.SampleFile(
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    private fun mapToCreateBenefitFile(
        file: DigitalGachaItemRequest.BenefitFile
    ): CreateDigitalGachaItem.BenefitFile {
        return CreateDigitalGachaItem.BenefitFile(
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    private fun mapToUpdateFile(
        file: DigitalGachaItemRequest.UpdateFile
    ): UpdateDigitalGachaItem.File {
        return UpdateDigitalGachaItem.File(id = file.id, name = file.name, isSecret = file.isSecret)
    }

    private fun mapToUpdateSampleFile(
        file: DigitalGachaItemRequest.SampleFile
    ): UpdateDigitalGachaItem.SampleFile {
        return UpdateDigitalGachaItem.SampleFile(
            id = file.id,
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    private fun mapToUpdateBenefitFile(
        file: DigitalGachaItemRequest.BenefitFile
    ): UpdateDigitalGachaItem.BenefitFile {
        return UpdateDigitalGachaItem.BenefitFile(
            id = file.id,
            name = file.name,
            objectUri = file.objectUri,
            thumbnailUri = file.thumbnailUri,
            fileType = file.fileType,
            size = file.size,
            duration = file.duration,
        )
    }

    companion object {
        private const val UNKNOWN_USER_ACCOUNT_IDENTITY = ""
        private const val UNKNOWN_USER_NAME = "不明なユーザー"
    }
}
