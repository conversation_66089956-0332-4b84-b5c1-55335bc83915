package jp.co.torihada.fanme.modules.fanme.models

import BaseModel
import com.fasterxml.jackson.annotation.JsonManagedReference
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import java.time.LocalDateTime
import jp.co.torihada.fanme.Util
import jp.co.torihada.fanme.modules.fanme.utils.JsonUtil
import org.eclipse.microprofile.openapi.annotations.media.Schema

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
data class AuditGroupMetadata(
    @field:Schema(description = "ショップID", example = "123") var shopId: Long? = null,
    @field:Schema(description = "商品ID", example = "456") var itemId: String? = null,
    @field:Schema(description = "タイトル", example = "商品名") var title: String? = null,
    @field:Schema(description = "説明", example = "商品の説明文") var description: String? = null,
)

@Entity
@Table(name = "audit_groups")
class AuditGroup : BaseModel() {
    enum class AuditType(val value: String) {
        SHOP("SHOP"),
        SHOP_ITEM("SHOP_ITEM"),
        FANME_PROFILE("FANME_PROFILE"),
        FANME_CONTENT("FANME_CONTENT"),
    }

    enum class OperationType(val value: String) {
        INSERT("INSERT"),
        UPDATE("UPDATE");

        companion object {
            fun fromValue(value: String): OperationType {
                return values().find { it.value == value }
                    ?: throw IllegalArgumentException("Invalid OperationType value: $value")
            }
        }
    }

    enum class AuditStatus(val value: Int) {
        UNAUDITED(0), // 未監査
        REJECTED(-1), // 却下
        PENDING(1), // PENDING
        RESEND(5), // 再提出
        APPROVED(9), // 承認
    }

    @JsonManagedReference
    @OneToMany(fetch = FetchType.LAZY, cascade = [CascadeType.ALL])
    @JoinColumn(name = "audit_group_id")
    var auditObjects: MutableList<AuditObject> = mutableListOf()

    @Size(max = 50) @NotNull @Column(name = "user_id", nullable = false) var userUid: String = ""

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
        name = "user_id",
        referencedColumnName = "uid",
        insertable = false,
        updatable = false,
        foreignKey = ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT),
    )
    @JsonProperty("user")
    var user: User? = null

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "audit_type", nullable = false)
    var auditType: AuditType = AuditType.SHOP_ITEM

    // INSERT, UPDATE
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false)
    var operationType: OperationType = OperationType.INSERT

    // json形式（shop_id,item_id,title,description,など動的に入る）
    @Column(name = "metadata", columnDefinition = "json") var metadata: String? = null

    // Jackson用のプロパティ - JSONシリアライズ時にはこれが使用される
    @JsonProperty("metadata_object")
    fun getMetadataAsObject(): AuditGroupMetadata? {
        val map = JsonUtil.parseJsonToMap(metadata)
        return if (map is Map<*, *>) {
            try {
                val mapper = jacksonObjectMapper()
                mapper.convertValue(map, AuditGroupMetadata::class.java)
            } catch (e: Exception) {
                null
            }
        } else {
            null
        }
    }

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "status", nullable = false)
    var status: AuditStatus = AuditStatus.UNAUDITED

    // 監査コメント
    @Column(name = "comment", columnDefinition = "text") var comment: String? = null

    // 監査結果
    @Column(name = "audited_at") var auditedAt: LocalDateTime? = null

    // 監査ユーザID
    @Size(max = 50) @Column(name = "audited_user_id") var auditedUserUid: String? = null

    companion object : PanacheCompanion<AuditGroup> {
        fun getMetadata(id: Long): AuditGroupMetadata {
            val auditGroup = findById(id)
            return JsonUtil.parseJson<AuditGroupMetadata>(auditGroup?.metadata)
                ?: AuditGroupMetadata()
        }

        fun updateMetadata(id: Long, block: (AuditGroupMetadata) -> AuditGroupMetadata) {
            val auditGroup = findById(id)
            if (auditGroup != null) {
                val currentMetadata = getMetadata(id)
                val updatedMetadata = block(currentMetadata)
                auditGroup.metadata = JsonUtil.toJsonString(updatedMetadata)
                auditGroup.persist()
            }
        }

        fun findAll(top: Int?, skip: Int?): List<AuditGroup> {
            val orderByQuery = "ORDER BY id DESC"
            val find = find(orderByQuery)
            val list = Util.getEntityListWithPagination(find, top, skip)
            return list.filterIsInstance<AuditGroup>().onEach { it.auditObjects.size }
        }
    }
}
