package jp.co.torihada.fanme.modules.fanme.usecases.audit

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class GetAuditGroups {
    data class Input(val odata: OData?)

    fun execute(params: Input): Result<List<AuditGroup>, FanmeException> {
        return try {
            val auditGroups = AuditGroup.findAll(params.odata?.top, params.odata?.skip)
            Ok(auditGroups)
        } catch (e: Exception) {
            Err(FanmeException(0, e.message ?: "Unknown error"))
        }
    }
}
