package jp.co.torihada.fanme.endpoints.shop.request.item.digitalBundle

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.modules.shop.controllers.requests.ItemRequest
import jp.co.torihada.fanme.modules.shop.models.ItemType

data class CreateOrUpdateItemRequest(
    val name: String,
    val description: String?,
    val thumbnailUri: String,
    val thumbnailFrom: Int,
    val thumbnailBlurLevel: Int,
    val thumbnailWatermarkLevel: Int,
    val price: Int,
    val available: Boolean,
    val itemFiles: List<File>,
    val samples: List<SampleFile>?,
    val benefits: List<Benefit>?,
    val tags: List<String>?,
    val itemOption: ItemOption,
    val itemType: Int? = ItemType.DIGITAL_BUNDLE.value,
)

data class File(
    val name: String,
    val objectUri: String,
    val thumbnailUri: String,
    val price: Int?,
    val fileType: String,
    val size: Float,
    val duration: Int,
    val itemThumbnailSelected: Boolean?,
    val sortOrder: Int,
)

data class SampleFile(
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
)

data class Benefit(
    val id: Long,
    val description: String?,
    val conditionType: Int,
    val files: List<BenefitFile>,
)

data class BenefitFile(
    val name: String,
    val objectUri: String,
    val thumbnailUri: String?,
    val fileType: String,
    val size: Float,
    val duration: Int,
    val itemThumbnailSelected: Boolean?,
    val sortOrder: Int?,
    val conditionType: Int,
)

data class ItemOption(
    val isSingleSales: Boolean,
    val qtyTotal: Int?,
    val qtyPerUser: Int?,
    val forSale: ForSale?,
    val password: String?,
    val onSale: OnSale?,
)

data class ForSale(val startAt: String?, val endAt: String?)

data class OnSale(val discountRate: Float, val startAt: String?, val endAt: String?)

data class SortItemsRequest(val items: List<SortItem>)

data class SortItem(val id: Long, val sortOrder: Int)

@ApplicationScoped
class RequestConverter() {
    fun requestToCreateItem(
        uid: String,
        request: CreateOrUpdateItemRequest,
    ): ItemRequest.CreateItem {
        return ItemRequest.CreateItem(
            creatorUid = uid,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            price = request.price,
            available = request.available,
            files = convertFiles(request.itemFiles),
            samples = request.samples?.let { convertSampleFiles(it) },
            benefits = request.benefits?.let { convertBenefit(it) },
            tags = request.tags,
            itemOption = convertItemOption(request.itemOption),
            itemType = request.itemType?.let { ItemType.fromValue(it) } ?: ItemType.DIGITAL_BUNDLE,
        )
    }

    fun requestToUpdateItem(
        uid: String,
        id: Long,
        request: CreateOrUpdateItemRequest,
    ): ItemRequest.UpdateItem {
        return ItemRequest.UpdateItem(
            creatorUid = uid,
            itemId = id,
            name = request.name,
            description = request.description,
            thumbnailUri = request.thumbnailUri,
            thumbnailFrom = request.thumbnailFrom,
            thumbnailBlurLevel = request.thumbnailBlurLevel,
            thumbnailWatermarkLevel = request.thumbnailWatermarkLevel,
            price = request.price,
            available = request.available,
            files = convertFiles(request.itemFiles),
            samples = request.samples?.let { convertSampleFiles(it) },
            benefits = request.benefits?.let { convertBenefit(it) },
            tags = request.tags,
            itemOption = convertItemOption(request.itemOption),
        )
    }

    private fun convertFiles(request: List<File>): List<ItemRequest.File> {
        return request.map {
            ItemRequest.File(
                id = null,
                name = it.name,
                objectUri = it.objectUri,
                thumbnailUri = it.thumbnailUri,
                price = it.price,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
                itemThumbnailSelected = it.itemThumbnailSelected,
                sortOrder = it.sortOrder,
            )
        }
    }

    private fun convertSampleFiles(request: List<SampleFile>): List<ItemRequest.SampleFile> {
        return request.map {
            ItemRequest.SampleFile(
                id = null,
                name = it.name,
                objectUri = it.objectUri,
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertBenefitFiles(request: List<BenefitFile>): List<ItemRequest.BenefitFile> {
        return request.map {
            ItemRequest.BenefitFile(
                id = null,
                name = it.name,
                objectUri = it.objectUri,
                thumbnailUri = it.thumbnailUri,
                fileType = it.fileType,
                size = it.size,
                duration = it.duration,
            )
        }
    }

    private fun convertBenefit(request: List<Benefit>): List<ItemRequest.Benefit> {
        return request.map {
            ItemRequest.Benefit(
                id = it.id,
                description = it.description,
                conditionType = it.conditionType,
                files = convertBenefitFiles(it.files),
            )
        }
    }

    private fun convertForSale(request: ForSale?): ItemRequest.ForSale? {
        return request?.let {
            ItemRequest.ForSale(startAt = request.startAt, endAt = request.endAt)
        }
    }

    private fun convertOnSale(request: OnSale?): ItemRequest.OnSale? {
        return request?.let {
            ItemRequest.OnSale(
                discountRate = request.discountRate,
                startAt = request.startAt,
                endAt = request.endAt,
            )
        }
    }

    private fun convertItemOption(request: ItemOption): ItemRequest.ItemOption {
        return ItemRequest.ItemOption(
            isSingleSales = request.isSingleSales,
            qtyTotal = request.qtyTotal,
            qtyPerUser = request.qtyPerUser,
            forSale = convertForSale(request.forSale),
            password = request.password,
            onSale = convertOnSale(request.onSale),
        )
    }

    fun requestToSortItems(userUid: String, request: SortItemsRequest): ItemRequest.SortItems {
        return ItemRequest.SortItems(
            userUid = userUid,
            items = request.items.map { ItemRequest.SortItem(it.id, it.sortOrder) },
        )
    }
}
