package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.models.BenefitFile as BenefitFileModel
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService

@ApplicationScoped
class UpdateItem {

    @Inject private lateinit var shopAuditService: ShopAuditService

    data class Input(
        val itemId: Long,
        val creatorUid: String,
        val name: String,
        val description: String?,
        val thumbnailUri: String,
        val thumbnailFrom: Int,
        val thumbnailBlurLevel: Int,
        val thumbnailWatermarkLevel: Int,
        val price: Int,
        val available: Boolean,
        val files: List<File>?,
        val samples: List<SampleFile>?,
        val benefits: List<BenefitParam>?,
        val tags: List<String>?,
        val itemOption: ItemOptionParam,
    )

    data class File(
        val id: Long?,
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val price: Int?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val itemThumbnailSelected: Boolean = false,
        val sortOrder: Int = 0,
    )

    data class SampleFile(
        val id: Long?,
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
    )

    data class BenefitFile(
        val id: Long?,
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val itemThumbnailSelected: Boolean? = false,
        val sortOrder: Int? = 0,
    )

    data class BenefitParam(
        val id: Long?,
        val description: String?,
        val conditionType: Int,
        val files: List<BenefitFile>,
    )

    data class ItemOptionParam(
        val isSingleSales: Boolean,
        val qtyTotal: Int?,
        val qtyPerUser: Int?,
        val forSale: ForSale?,
        val password: String?,
        val onSale: OnSale?,
    )

    data class ForSale(val startAt: Instant?, val endAt: Instant?)

    data class OnSale(val discountRate: Float, val startAt: Instant?, val endAt: Instant?)

    fun execute(params: Input): Result<Item, FanmeException> {
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))

        // itemIdが存在するか確認
        // itemTypeがガチャ以外か確認
        var item = Item.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))
        val isTargetType = item.itemType in listOf(ItemType.DIGITAL_BUNDLE, ItemType.CHEKI)
        if (!isTargetType) {
            return Err(ResourceNotFoundException("Item"))
        }

        item =
            Item.update(
                params.itemId,
                params.name,
                params.description,
                params.thumbnailUri,
                params.thumbnailFrom,
                params.thumbnailBlurLevel,
                params.thumbnailWatermarkLevel,
                params.price,
                0,
                params.available,
            )

        item.files.forEach() {
            if (params.files?.find { f -> f.id == it.id } == null) {
                ItemFile.deleteById(it.id!!)
            }
        }
        val files =
            params.files?.map {
                if (it.id != null) {
                    val file = ItemFile.findById(it.id)
                    if (file != null) {
                        ItemFile.update(
                            it.id,
                            it.name,
                            it.objectUri,
                            it.thumbnailUri,
                            null, // maskedThumbnailUriはデジタルガチャ用のため、ここではnull
                            it.price,
                            it.fileType,
                            it.size,
                            it.duration,
                            it.itemThumbnailSelected,
                            it.sortOrder,
                        )
                    } else {
                        ItemFile.create(
                            item.id!!,
                            it.name,
                            it.objectUri,
                            it.thumbnailUri,
                            null,
                            it.price,
                            it.fileType,
                            it.size,
                            it.duration,
                            it.itemThumbnailSelected,
                            it.sortOrder,
                        )
                    }
                } else {
                    ItemFile.create(
                        item.id!!,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        null,
                        it.price,
                        it.fileType,
                        it.size,
                        it.duration,
                        it.itemThumbnailSelected,
                        it.sortOrder,
                    )
                }
            }
        val itemFileType = if (!files.isNullOrEmpty()) Util.getItemFileType(files) else 0
        item.fileType = itemFileType
        item.persist()
        ItemOption.updateByItemId(
            item.id!!,
            params.itemOption.isSingleSales,
            params.itemOption.qtyTotal,
            params.itemOption.qtyPerUser,
            params.itemOption.forSale?.startAt,
            params.itemOption.forSale?.endAt,
            params.itemOption.password,
        )

        val onSale = item.onSale
        params.itemOption.onSale?.let {
            if (onSale != null) {
                ItemOnSale.updateByItemId(item.id!!, it.discountRate, it.startAt, it.endAt)
            } else {
                ItemOnSale.create(item.id!!, it.discountRate, it.startAt, it.endAt)
            }
        }

        var samples: List<Sample>? = null
        if (params.samples != null) {
            val sampleIdsToDelete =
                item.samples
                    .filter { existingSample ->
                        params.samples.none { paramSample -> paramSample.id == existingSample.id }
                    }
                    .mapNotNull { it.id }
            Sample.deleteByIds(sampleIdsToDelete)

            samples =
                params.samples.map {
                    Sample.upsert(
                        item.id!!,
                        it.id,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        it.fileType,
                        it.size,
                        it.duration,
                    )
                }
        }

        item.benefits.forEach() {
            if (params.benefits?.find { s -> s.id == it.id } == null) {
                Benefit.deleteById(it.id!!)
            }
        }

        var benefitFiles: List<BenefitFileModel> = mutableListOf()
        if (params.benefits == null) {
            item.benefits.forEach() { Benefit.deleteById(it.id!!) }
        } else {
            params.benefits.forEach {
                if (it.id != null) {
                    val benefit = Benefit.findById(it.id)
                    if (benefit != null) {
                        Benefit.update(it.id, it.description)
                        BenefitFileModel.delete(it.id)
                        benefitFiles += createBenefitFiles(benefit, it.files)
                    } else {
                        val benefit =
                            Benefit.create(
                                item.id!!,
                                it.description,
                                BenefitCondition.fromValue(it.conditionType),
                            )
                        benefitFiles += createBenefitFiles(benefit, it.files)
                    }
                } else {
                    val benefit =
                        Benefit.create(
                            item.id!!,
                            it.description,
                            BenefitCondition.fromValue(it.conditionType),
                        )
                    benefitFiles += createBenefitFiles(benefit, it.files)
                }
            }
        }

        item.tags.forEach() {
            if (params.tags?.find { t -> t == it.tag.tag } == null) {
                ItemTag.deleteById(it.id!!)
            }
        }
        params.tags?.forEach {
            if (item.tags.find { t -> t.tag.tag == it } == null) {
                Tag.create(shop.id!!, it)
            }
            ItemTag.create(item.id!!, it)
        }

        // 監査データの作成
        shopAuditService.createAuditDataForShopItem(
            params.creatorUid,
            ShopAuditService.OperationType.UPDATE,
            item,
            files,
            samples,
            benefitFiles.ifEmpty { null },
        )

        return Ok(item)
    }

    private fun createBenefitFiles(
        benefit: Benefit,
        files: List<BenefitFile>,
    ): List<BenefitFileModel> {
        return files.map {
            if (it.id != null) {
                val benefitFile = BenefitFileModel.findById(it.id)
                if (benefitFile != null) {
                    BenefitFileModel.update(
                        it.id,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        it.fileType,
                        it.size,
                        it.duration,
                    )
                } else {
                    BenefitFileModel.create(
                        benefit.id!!,
                        it.name,
                        it.objectUri,
                        it.thumbnailUri,
                        it.fileType,
                        it.size,
                        it.duration,
                    )
                }
            } else {
                BenefitFileModel.create(
                    benefit.id!!,
                    it.name,
                    it.objectUri,
                    it.thumbnailUri,
                    it.fileType,
                    it.size,
                    it.duration,
                )
            }
        }
    }
}
