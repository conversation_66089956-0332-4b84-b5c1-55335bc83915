package jp.co.torihada.fanme.modules.shop.controllers

import com.fasterxml.jackson.databind.PropertyNamingStrategies
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jp.co.torihada.fanme.Const
import jp.co.torihada.fanme.Const.RestrictedAccountType
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.fanme.controllers.RestrictedAccountController
import jp.co.torihada.fanme.modules.payment.Const as PaymentConst
import jp.co.torihada.fanme.modules.payment.controllers.CheckoutController
import jp.co.torihada.fanme.modules.payment.models.Checkout
import jp.co.torihada.fanme.modules.payment.models.Transaction
import jp.co.torihada.fanme.modules.payment.usecases.payments.cvs.ExecTransaction
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.USER_ID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Util.OrderAmounts
import jp.co.torihada.fanme.modules.shop.Util.PurchasedItemStatus
import jp.co.torihada.fanme.modules.shop.controllers.common.BaseOrderController
import jp.co.torihada.fanme.modules.shop.controllers.requests.OrderRequest
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.usecases.benefitLog.CreateBenefitLog
import jp.co.torihada.fanme.modules.shop.usecases.cart.UnlockCart
import jp.co.torihada.fanme.modules.shop.usecases.cartItem.DeleteCartItems
import jp.co.torihada.fanme.modules.shop.usecases.order.*
import jp.co.torihada.fanme.odata.OData
import org.jboss.logging.Logger

@ApplicationScoped
class OrderController : BaseOrderController() {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var shopConfig: ShopConfig

    @Inject private lateinit var restrictedAccountController: RestrictedAccountController

    @Inject private lateinit var checkoutController: CheckoutController

    @Inject private lateinit var getOrders: GetOrders

    @Inject private lateinit var checkOrder: CheckOrder

    @Inject private lateinit var getAmounts: GetAmounts

    @Inject private lateinit var createOrder: CreateOrder

    @Inject private lateinit var postOrderProcessor: PostOrderProcessor

    @Inject private lateinit var updateOrder: UpdateOrder

    @Inject private lateinit var updatePurchasedItemStatus: UpdatePurchasedItemStatus

    @Inject private lateinit var createPurchasedItems: CreatePurchasedItems

    @Inject private lateinit var getTipUpperLimit: GetTipUpperLimit

    @Inject private lateinit var getConvenienceFees: GetConvenienceFees

    @Inject private lateinit var unlockCart: UnlockCart

    @Inject private lateinit var deleteCartItems: DeleteCartItems

    @Inject private lateinit var createBenefitLog: CreateBenefitLog

    fun getOrders(
        @NotNull(message = USER_ID_IS_REQUIRED) userId: String,
        odata: OData,
    ): List<Order> {
        return getOrders.execute(GetOrders.Input(userId = userId, odata = odata)).getOrElse {
            emptyList()
        }
    }

    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy::class)
    data class OrderResult(
        val order: Order,
        val purchasedItems: List<PurchasedItem>?,
        val convenienceCheckout: ExecTransaction.Output?,
        val redirectUrl: String? = null,
    )

    fun createOrder(@Valid request: OrderRequest.CreateOrder, fanmeToken: String?): OrderResult {
        // 注文内容のvalidation
        checkOrder
            .execute(
                CheckOrder.Input(
                    userUid = request.userId,
                    cartId = request.cartId,
                    cartItemIds = request.cartItemIds,
                    tip = request.tip,
                )
            )
            .getOrElse { throw it }

        // 必要なリソースの計算・取得
        val amounts =
            getAmounts
                .execute(
                    GetAmounts.Input(
                        cartId = request.cartId,
                        cartItemIds = request.cartItemIds,
                        tip = request.tip,
                    )
                )
                .getOrElse { throw it }
        val shop = Cart.findById(request.cartId)?.shop ?: throw ResourceNotFoundException("Shop")

        // checkoutの作成(payment)
        val checkout =
            createCheckout(
                shop = shop,
                amounts = amounts,
                userId = request.userId,
                tip = request.tip,
                paymentMethod = request.paymentMethod,
                isSingleOrder = false,
                fanmeToken = fanmeToken,
                convenienceParam = request.convenienceParam,
                itemId = null,
            )
        // orderの作成(shop)
        val order = createOrderRecord(request = request, checkout = checkout)
        // 決済の実行(payment)
        val resultExecuteOrder =
            executeOrder(
                sellerUserUid = shop.creatorUid!!,
                purchaserUserUid = request.userId,
                paymentMethod = request.paymentMethod,
                checkout = checkout,
                amounts = amounts,
                isSingleOrder = false,
                cardParam = request.cardParam,
                convenienceParam = request.convenienceParam,
                googlePayParam = request.googlePayParam,
                applePayParam = request.applePayParam,
            )
        // 注文後のレコード更新
        val resultPostOrderProcess =
            executePostOrderProcess(
                request = request,
                shop = shop,
                checkout = checkout,
                amounts = amounts,
                resultExecuteOrder = resultExecuteOrder,
            )

        // 特典ログ追加
        if (resultExecuteOrder.transaction != null) {
            request.cartItemIds.forEach { id ->
                createBenefitLog.execute(
                    CreateBenefitLog.Input(itemId = id, purchaserUid = request.userId)
                )
            }
        }

        // メール送信
        try {
            if (resultExecuteOrder.transaction != null) {
                mailer.sendPurchasedEmail(
                    resultPostOrderProcess.order,
                    resultPostOrderProcess.purchasedItems,
                    resultExecuteOrder.transaction,
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to send email to purchaser", e)
        }
        try {
            if (resultExecuteOrder.transaction != null) {
                val showSales =
                    !restrictedAccountController.isRestricted(
                        RestrictedAccountType.HIDE_SALES,
                        resultPostOrderProcess.order.shop.creatorUid,
                    )
                if (showSales) {
                    mailer.sendPurchasedSellerEmail(
                        resultPostOrderProcess.order,
                        resultPostOrderProcess.purchasedItems,
                        resultExecuteOrder.transaction,
                    )
                }
                mailer.sendPurchasedSellerEmailForManager(
                    resultPostOrderProcess.order,
                    resultPostOrderProcess.purchasedItems,
                    resultExecuteOrder.transaction,
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to send email to seller", e)
        }
        // エールログの追加
        if (resultExecuteOrder.transaction != null) {
            try {
                addYellLog(resultPostOrderProcess.order, checkout, resultExecuteOrder.status.value)
            } catch (e: Exception) {
                logger.error("Failed to insert yell log", e)
            }
        }

        return OrderResult(
            order = order,
            purchasedItems = resultPostOrderProcess.purchasedItems,
            convenienceCheckout = resultExecuteOrder.convenienceCheckout,
            redirectUrl = resultExecuteOrder.redirectUrl,
        )
    }

    // エール付与のためにfanmeのTransactionalな処理を使うので、ここではTransactionalを使わない
    fun updateOrder(
        @Min(0) transactionId: Long?,
        @NotNull @Min(0) checkoutId: Long,
        status: String,
    ): Order {
        PurchasedItemStatus.fromValue(status) ?: throw Exception("Invalid status")
        val checkout = checkoutController.getCheckout(checkoutId)
        val order = updateOrderRelatedRecords(checkout, status, transactionId)

        // 特典ログ追加
        val purchaseItemIds = PurchasedItem.findByOrderId(order.id!!).map { it.item.id!! }
        purchaseItemIds.forEach { itemId ->
            try {
                createBenefitLog.execute(
                    CreateBenefitLog.Input(
                        itemId = itemId,
                        purchaserUid = checkout.purchaserUserId!!,
                    )
                )
            } catch (e: Exception) {
                logger.error("Failed to create benefit log for item $itemId", e)
            }
        }

        // PayPay決済成功；カートの中身を削除しカートのロックを解除する
        // PayPay決済失敗；カートのロックを解除する
        updateCartForPayPayStatus(status = status, order = order, checkout = checkout)

        // エールログの追加
        try {
            addYellLog(order = order, checkout = checkout, status = status)
        } catch (e: Exception) {
            logger.error("Failed to insert yell log", e)
        }
        return order
    }

    fun finalizeCreditCard3DSecure(@Min(0) transactionId: Long, @NotNull @Min(0) checkoutId: Long) {
        val checkout = checkoutController.getCheckout(checkoutId)
        val order = Order.findByCheckoutId(checkoutId) ?: throw ResourceNotFoundException("Order")
        val transaction =
            Transaction.findById(transactionId) ?: throw ResourceNotFoundException("Transaction")
        val cart =
            Cart.findByUserUidAndShopId(checkout.purchaserUserId!!, order.shop.id!!)
                ?: throw ResourceNotFoundException("CartItem")

        val modifyRecordsFor3DSecureOutput =
            modifyRecordsFor3DSecure(
                checkout = checkout,
                order = order,
                cart = cart,
                transactionId = transactionId,
            )

        // メール送信
        try {
            mailer.sendPurchasedEmail(
                modifyRecordsFor3DSecureOutput.updatedOrder,
                modifyRecordsFor3DSecureOutput.purchasedItems,
                transaction,
            )
        } catch (e: Exception) {
            logger.error("Failed to send email to purchaser", e)
        }
        try {
            val showSales =
                !restrictedAccountController.isRestricted(
                    RestrictedAccountType.HIDE_SALES,
                    modifyRecordsFor3DSecureOutput.updatedOrder.shop.creatorUid,
                )
            if (showSales) {
                mailer.sendPurchasedSellerEmail(
                    modifyRecordsFor3DSecureOutput.updatedOrder,
                    modifyRecordsFor3DSecureOutput.purchasedItems,
                    transaction,
                )
            }
            mailer.sendPurchasedSellerEmailForManager(
                modifyRecordsFor3DSecureOutput.updatedOrder,
                modifyRecordsFor3DSecureOutput.purchasedItems,
                transaction,
            )
        } catch (e: Exception) {
            logger.error("Failed to send email to seller", e)
        }

        addYellLog(
            order = modifyRecordsFor3DSecureOutput.updatedOrder,
            checkout = checkout,
            status = Const.CheckoutStatus.PAYSUCCESS.value,
        )
    }

    // finalizeCreditCard3DSecure関数内のみで使用
    // @Transactionalのためにスコープがpublicだが、外部から呼び出してはいけない
    @Transactional
    fun modifyRecordsFor3DSecure(
        checkout: Checkout,
        order: Order,
        cart: Cart,
        transactionId: Long?,
    ): ModifyRecordsFor3DSecureOutput {
        val updatedOrder =
            updateOrder
                .execute(
                    UpdateOrder.Input(
                        transactionId = transactionId,
                        checkout = checkout,
                        Const.CheckoutStatus.PAYSUCCESS.value,
                    )
                )
                .getOrElse { throw it }

        // 必要なリソースの計算・取得
        val amounts =
            getAmounts
                .execute(
                    GetAmounts.Input(
                        cartId = cart.id!!,
                        cartItemIds = cart.items.map { it.id!! },
                        tip = checkout.tip?.amount ?: 0,
                    )
                )
                .getOrElse { throw it }

        val purchasedItems =
            createPurchasedItems
                .execute(
                    CreatePurchasedItems.Input(
                        purchaserUserId = checkout.purchaserUserId!!,
                        orderId = order.id!!,
                        shopId = order.shop.id!!,
                        amounts = amounts,
                        status = PurchasedItemStatus.PAYSUCCESS,
                    )
                )
                .getOrElse { throw it }
                .purchasedItems

        deleteCartItems.execute(checkout.id!!)

        return ModifyRecordsFor3DSecureOutput(updatedOrder, purchasedItems)
    }

    data class ModifyRecordsFor3DSecureOutput(
        val updatedOrder: Order,
        val purchasedItems: List<PurchasedItem>,
    )

    // updateOrder関数内で使用
    // @Transactionalのためにスコープがpublicだが、外部から呼び出してはいけない
    @Transactional
    fun updateOrderRelatedRecords(checkout: Checkout, status: String, transactionId: Long?): Order {
        val order =
            updateOrder
                .execute(
                    UpdateOrder.Input(transactionId = transactionId, checkout = checkout, status)
                )
                .getOrElse { throw it }

        updatePurchasedItemStatus.execute(
            UpdatePurchasedItemStatus.Input(orderId = order.id!!, status = status)
        )

        return order
    }

    fun getTipLimit(creatorUid: String, userUid: String): GetTipUpperLimit.TipUpperLimit {
        return getTipUpperLimit
            .execute(GetTipUpperLimit.Input(creatorUid = creatorUid, userUid = userUid))
            .getOrElse { throw it }
    }

    fun getConvenienceFees(): List<GetConvenienceFees.ConvenienceFee> {
        return getConvenienceFees.execute().getOrElse { throw it }
    }

    // createOrder関数内で使用
    // @Transactionalのためにスコープがpublicだが、外部から呼び出してはいけない
    @Transactional
    fun createOrderRecord(request: OrderRequest.CreateOrder, checkout: Checkout): Order {
        return createOrder
            .execute(CreateOrder.Input(cartId = request.cartId, checkoutId = checkout.id!!))
            .getOrElse { throw it }
    }

    data class ExecuteOrderResult(
        val transaction: Transaction? = null,
        val convenienceCheckout: ExecTransaction.Output? = null,
        val redirectUrl: String? = null,
        val status: Const.CheckoutStatus,
    )

    // createOrder関数内で使用
    // @Transactionalのためにスコープがpublicだが、外部から呼び出してはいけない
    @Transactional
    fun executePostOrderProcess(
        request: OrderRequest.CreateOrder,
        shop: Shop,
        checkout: Checkout,
        amounts: OrderAmounts,
        resultExecuteOrder: ExecuteOrderResult,
    ): PostOrderProcessor.Output {
        return postOrderProcessor
            .execute(
                PostOrderProcessor.Input(
                    userId = request.userId,
                    shop = shop,
                    transaction = resultExecuteOrder.transaction,
                    checkoutId = checkout.id!!,
                    cartId = request.cartId,
                    cartItemIds = request.cartItemIds,
                    amounts = amounts,
                    purchasedItemStatus =
                        PurchasedItemStatus.fromValue(resultExecuteOrder.status.value)
                            ?: PurchasedItemStatus.PAYFAILED,
                    paymentMethod = request.paymentMethod,
                )
            )
            .getOrElse { throw it }
    }

    @Transactional
    fun updateCartForPayPayStatus(status: String, order: Order, checkout: Checkout) {
        if (checkout.type == PaymentConst.PaymentType.PAY_PAY.value) {
            val purchasedItem = PurchasedItem.findByOrderId(order.id!!).firstOrNull()
            // デジタルガチャ（単一商品）の場合はカートを使用しないので削除しない
            if (purchasedItem!!.item.itemType != ItemType.DIGITAL_GACHA) {
                when (status) {
                    Const.CheckoutStatus.PAYSUCCESS.value -> {
                        deleteCartItems.execute(checkout.id!!)
                        unlockCart.execute(checkout.id!!)
                    }
                    Const.CheckoutStatus.PAYFAILED.value,
                    Const.CheckoutStatus.CANCEL.value,
                    Const.CheckoutStatus.EXPIRED.value -> {
                        unlockCart.execute(checkout.id!!)
                    }
                }
            }
        }
    }

    data class ReturnUrls(val successUrl: String, val errorUrl: String)
}
