package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.batch.usecases.MonthlySellerSalesBatch
import org.jboss.logging.Logger

class MonthlySellerSalesBatchMain : QuarkusApplication {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var monthlySellerSalesBatch: MonthlySellerSalesBatch

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("MonthlySellerSalesBatchMain start")

        val yearMonth =
            if (args.size > 1 && args[1] != null) {
                args[1]
            } else {
                val previousDate: LocalDateTime =
                    ZonedDateTime.now(ZoneId.of("Asia/Tokyo"))
                        .minusDays(1) // 前日を取得
                        .toLocalDateTime() // LocalDateTime に変換

                DateTimeFormatter.ofPattern("yyyyMM").format(previousDate)
            }

        try {
            monthlySellerSalesBatch.execute(yearMonth!!)
            logger.info("MonthlySellerSalesBatchMain success yearMonth: $yearMonth")
            return 0
        } catch (e: Exception) {
            logger.error("MonthlySellerSalesBatchMain error yearMonth: $yearMonth", e)
            return 1
        }
    }
}
