package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.time.ZoneOffset
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.MonthlyTenantSale
import jp.co.torihada.fanme.modules.payment.models.SellerGmoTransfer
import jp.co.torihada.fanme.modules.payment.models.Transaction
import org.jboss.logging.Logger

@ApplicationScoped
class MonthlyTenantSalesBatch {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var monthlyTotalBase: MonthlyTotalBase
    @Inject private lateinit var config: Config

    fun execute(yearMonth: String) {
        logger.info("MonthlyTenantSalesBatch start yearMonth: $yearMonth")

        val startEndDateTimes = monthlyTotalBase.toUtcMonthPeriod(yearMonth)

        // テナントの月次売上情報取得
        val monthlyTenantAmount =
            Transaction.findMonthlyTenantAmounts(
                tenant = config.tenant(),
                startDateTime = startEndDateTimes.startDateTime.toInstant(ZoneOffset.UTC),
                endDateTime = startEndDateTimes.endDateTime.toInstant(ZoneOffset.UTC),
                status = Const.TransactionStatus.Success.value,
            )

        // テナントの月次送金手数料取得
        val transferFeeValues =
            SellerGmoTransfer.findTransferFee(
                tenant = config.tenant(),
                startDateTime = startEndDateTimes.startDateTime.toInstant(ZoneOffset.UTC),
                endDateTime = startEndDateTimes.endDateTime.toInstant(ZoneOffset.UTC),
            )

        val monthlyTenantSale =
            MonthlyTenantSale.findByTenantAndYearMonth(config.tenant(), yearMonth)
        if (monthlyTenantSale == null) {
            MonthlyTenantSale()
                .apply {
                    this.tenant = config.tenant()
                    this.yearMonth = yearMonth
                    this.transactionAmount = monthlyTenantAmount.transactionAmount ?: 0
                    this.miniappSalesAmount = monthlyTenantAmount.tenantSaleAmount ?: 0
                    this.transferSalesAmount = transferFeeValues?.totalFee ?: 0
                    this.gmoTransferFeeAmount = transferFeeValues?.totalGmoFee ?: 0
                    this.sellerSalesAmount = monthlyTenantAmount.sellerSaleAmount ?: 0
                }
                .persist()
        } else {
            monthlyTenantSale.tenant = config.tenant()
            monthlyTenantSale.yearMonth = yearMonth
            monthlyTenantSale.transactionAmount = monthlyTenantAmount.transactionAmount ?: 0
            monthlyTenantSale.miniappSalesAmount = monthlyTenantAmount.tenantSaleAmount ?: 0
            monthlyTenantSale.transferSalesAmount = transferFeeValues?.totalFee ?: 0
            monthlyTenantSale.gmoTransferFeeAmount = transferFeeValues?.totalGmoFee ?: 0
            monthlyTenantSale.sellerSalesAmount = monthlyTenantAmount.sellerSaleAmount ?: 0
        }

        logger.info("MonthlyTenantSalesBatch end yearMonth: $yearMonth")
    }
}
