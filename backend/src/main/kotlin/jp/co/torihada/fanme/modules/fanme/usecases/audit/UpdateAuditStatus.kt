package jp.co.torihada.fanme.modules.fanme.usecases.audit

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupService

@ApplicationScoped
class UpdateAuditStatus @Inject constructor(private val auditGroupService: AuditGroupService) {
    fun execute(
        auditGroupId: Long,
        status: AuditStatus,
        comment: String? = null,
        auditedUserUid: String? = null,
    ) {
        auditGroupService.updateAuditStatus(
            auditGroupId = auditGroupId,
            status = status,
            comment = comment,
            auditedUserUid = auditedUserUid,
        )
    }
}
