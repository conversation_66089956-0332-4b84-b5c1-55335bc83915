package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrThrow
import com.github.michaelbull.result.onFailure
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUserRole
import jp.co.torihada.fanme.modules.console.usecases.GetAgencies
import jp.co.torihada.fanme.modules.console.usecases.GetAgencySales
import jp.co.torihada.fanme.modules.console.usecases.GetAgencyUsers
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class AgencyController {

    @Inject private lateinit var getAgency: GetAgencies
    @Inject private lateinit var getAgencySales: GetAgencySales
    @Inject private lateinit var getAgencyUsers: GetAgencyUsers

    fun getAgencies(
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        odata: OData?,
    ): List<Agency> {
        return getAgency
            .execute(GetAgencies.Input(odata, currentUserUid, currentUserRole))
            .getOrThrow()
    }

    fun getAgencyUsers(
        agencyId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
    ): List<User> {
        val input =
            GetAgencyUsers.Input(
                agencyId = agencyId,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
            )
        return getAgencyUsers.execute(input).getOrThrow()
    }

    fun getAgencySales(
        agencyId: Long,
        currentUserUid: String,
        currentUserRole: ConsoleUserRole,
        from: String?,
        to: String?,
    ): List<GetAgencySales.CreatorSales> {
        val input =
            GetAgencySales.Input(
                agencyId = agencyId,
                fromYearMonth = from,
                toYearMonth = to,
                currentUserUid = currentUserUid,
                currentUserRole = currentUserRole,
            )

        return getAgencySales.execute(input).onFailure { throw it }.getOrThrow().creatorSales
    }
}
