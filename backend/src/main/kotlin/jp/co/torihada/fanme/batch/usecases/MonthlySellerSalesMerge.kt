package jp.co.torihada.fanme.batch.usecases

import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.payment.Const
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale
import jp.co.torihada.fanme.modules.payment.models.SellerAccountActivity
import jp.co.torihada.fanme.modules.payment.models.SellerAccountBalance
import org.jboss.logging.Logger

@ApplicationScoped
class MonthlySellerSalesMerge {
    @Inject private lateinit var logger: Logger

    @Inject private lateinit var config: Config

    fun execute(yearMonth: String) {
        val monthlySellerSales =
            MonthlySellerSale.findByYearMonth(yearMonth, approved = true, merged = false)

        if (monthlySellerSales.isNullOrEmpty()) {
            logger.info("yearMonth=$yearMonth, no sales MonthlySellerSalesMerge")
            return
        }

        monthlySellerSales.forEach {

            // TODO ログ追加

            val sellerAccountBalance = SellerAccountBalance.findBySellerUserId(it.sellerUserId!!)
            val currentBalanceAmount = sellerAccountBalance?.amount ?: 0
            val currentAccumulatedSales = sellerAccountBalance?.accumulatedSales ?: 0
            // 前月分の売上を追加
            val nextBalance = currentBalanceAmount + (it.remainingAmount ?: 0)
            val nextAccumulatedSales = currentAccumulatedSales + (it.remainingAmount ?: 0)

            SellerAccountActivity()
                .apply {
                    this.tenant = config.tenant()
                    this.sellerUserId = it.sellerUserId
                    this.activityType = Const.AccountActivityType.Payment.value
                    this.activityCode = Const.AccountActivityCode.AppSales.value
                    this.yearMonth = yearMonth
                    this.amount = it.remainingAmount ?: 0
                    this.balance = nextBalance
                }
                .persist()

            // 残高テーブルに金額を追加
            if (sellerAccountBalance == null) {
                SellerAccountBalance()
                    .apply {
                        this.tenant = config.tenant()
                        this.sellerUserId = it.sellerUserId
                        this.amount = nextBalance
                        this.accumulatedSales = nextAccumulatedSales
                    }
                    .persist()
            } else {
                sellerAccountBalance.amount = nextBalance
                sellerAccountBalance.accumulatedSales = nextAccumulatedSales
            }

            // 残高への取り込みフラグをtrueにする
            it.merged = true

            logger.info(
                "sellerUserId=${it.sellerUserId}, " +
                    "yearMonth=$yearMonth, " +
                    "sale amount=${it.sellerSalesAmount}, " +
                    "balance=$currentBalanceAmount->$nextBalance, " +
                    "accumulated sales=$currentAccumulatedSales->$nextAccumulatedSales"
            )
        }
    }
}
