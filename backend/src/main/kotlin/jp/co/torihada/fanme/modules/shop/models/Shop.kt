package jp.co.torihada.fanme.modules.shop.models

import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_MARGIN_RATE
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_SHOP_MESSAGE
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_TENANT
import jp.co.torihada.fanme.modules.shop.Const.DEFAULT_TIP_MARGIN_RATE
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH

@PersistenceUnit(name = "shop")
@Entity
@Table(name = "shops")
class Shop : BaseModel() {
    @Size(max = 50) @NotNull @Column(nullable = false, length = 50) var name: String = ""

    @NotNull
    @Column(nullable = false, updatable = false, length = 10)
    var tenant: String = DEFAULT_TENANT

    @Size(max = USER_UID_MAX_LENGTH)
    @NotNull
    @Column(name = "creator_uid", nullable = false, length = USER_UID_MAX_LENGTH)
    var creatorUid: String? = null

    @Size(max = 500) @Column(length = 500) var description: String? = null

    @Column(name = "header_image_uri", columnDefinition = "text") var headerImageUri: String? = null

    @NotBlank
    @Size(max = 100)
    @Column(nullable = false, length = 100)
    var message: String = DEFAULT_SHOP_MESSAGE

    @NotNull
    @Min(0)
    @Column(name = "margin_rate", nullable = false)
    var marginRate: Float = DEFAULT_MARGIN_RATE

    @NotNull
    @Min(0)
    @Column(name = "tip_margin_rate", nullable = false)
    var tipMarginRate: Float = DEFAULT_TIP_MARGIN_RATE

    @NotNull @Column(name = "is_open", nullable = false) var isOpen: Boolean = true

    @OneToOne(mappedBy = "shop", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    val limitation: ShopLimitation? = null

    companion object : PanacheCompanion<Shop> {

        fun findByCreatorUid(creatorUid: String): Shop? {
            return find("creatorUid = ?1 and isOpen = true", creatorUid).firstResult()
        }

        fun create(
            creatorUid: String,
            name: String,
            description: String?,
            headerImageUri: String?,
            message: String?,
        ): Shop {
            val shop =
                Shop().apply {
                    this.creatorUid = creatorUid
                    this.tenant = DEFAULT_TENANT
                    this.name = name
                    this.description = description
                    this.headerImageUri = headerImageUri
                    this.message = if (!message.isNullOrBlank()) message else DEFAULT_SHOP_MESSAGE
                    this.marginRate = DEFAULT_MARGIN_RATE
                    this.tipMarginRate = DEFAULT_TIP_MARGIN_RATE
                    this.isOpen = true
                }
            shop.persist()
            return shop
        }

        fun update(
            creatorUid: String,
            name: String,
            description: String?,
            headerImageUri: String?,
            message: String?,
        ): Shop {
            val shop = findByCreatorUid(creatorUid) ?: throw ResourceNotFoundException("shop")
            shop.name = name
            shop.description = description
            shop.headerImageUri = headerImageUri
            shop.message = if (!message.isNullOrBlank()) message else DEFAULT_SHOP_MESSAGE
            shop.persist()
            return shop
        }
    }
}
