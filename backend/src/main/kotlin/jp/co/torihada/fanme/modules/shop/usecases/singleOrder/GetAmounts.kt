package jp.co.torihada.fanme.modules.shop.usecases.singleOrder

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Util.Companion.getCurrentPrice
import jp.co.torihada.fanme.modules.shop.Util.OrderAmounts
import jp.co.torihada.fanme.modules.shop.Util.UnitPrice
import jp.co.torihada.fanme.modules.shop.models.Item
import jp.co.torihada.fanme.modules.shop.services.DeliveryService

@ApplicationScoped
class GetAmounts {

    @Inject private lateinit var deliveryService: DeliveryService

    data class Input(val itemId: Long, val quantity: Int, val tip: Int)

    fun execute(params: Input): Result<OrderAmounts, FanmeException> {
        val item = Item.findById(params.itemId) ?: return Err(ResourceNotFoundException("Item"))
        val isDigital = item.isDigital

        val sale = item.onSale
        var price = getCurrentPrice(item)
        if (sale != null) {
            val now = Instant.now()
            if (
                !((sale.startAt != null && sale.startAt!!.isAfter(now)) ||
                    (sale.endAt != null && sale.endAt!!.isBefore(now)))
            ) {
                val discountRate = BigDecimal(sale.discountRate.toString())
                price =
                    BigDecimal(price)
                        .multiply(BigDecimal.ONE.subtract(discountRate))
                        .setScale(0, RoundingMode.FLOOR)
                        .toInt()
            }
        }

        val deliveryFee = deliveryService.getDeliveryFee(listOf(item))
        val itemAmount = price * params.quantity
        val fee =
            BigDecimal(itemAmount)
                .multiply(BigDecimal(item.marginRate.toString()))
                .setScale(0, RoundingMode.CEILING)
                .toInt() +
                BigDecimal(params.tip)
                    .multiply(BigDecimal(item.shop.tipMarginRate.toString()))
                    .setScale(0, RoundingMode.CEILING)
                    .toInt()
        val total = itemAmount + params.tip
        val profit = itemAmount - fee

        return Ok(
            OrderAmounts(
                total = total,
                itemAmount = itemAmount,
                profit = profit,
                fee = fee,
                tip = params.tip,
                unitPrices =
                    listOf(UnitPrice(item = item, price = price, quantity = params.quantity)),
                deliveryFee = deliveryFee,
                isDigital = isDigital,
            )
        )
    }
}
