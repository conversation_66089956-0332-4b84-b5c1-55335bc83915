package jp.co.torihada.fanme.modules.shop.usecases.benefitLog

import jakarta.enterprise.context.ApplicationScoped
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.shop.models.Benefit
import jp.co.torihada.fanme.modules.shop.models.BenefitCompletionLog
import jp.co.torihada.fanme.modules.shop.models.BenefitCondition
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem

@ApplicationScoped
class CreateBenefitLog {

    data class Input(val itemId: Long, val purchaserUid: String)

    data class BenefitWithStatus(val benefit: Benefit, val isCompleted: Boolean)

    @Transactional
    fun execute(params: Input) {
        val benefits = Benefit.findByItemId(params.itemId)

        // すでに取得済みかチェック
        val benefitStatuses: List<BenefitWithStatus> =
            benefits.map { benefit ->
                val isCompleted =
                    BenefitCompletionLog.findByUserUidAndBenefitId(
                            userUid = params.purchaserUid,
                            benefitId = benefit.id!!,
                        )
                        ?.let { true } ?: false

                BenefitWithStatus(benefit = benefit, isCompleted = isCompleted)
            }

        if (benefitStatuses.all { it.isCompleted }) {
            return
        }

        val uncompletedBenefits = benefitStatuses.filter { !it.isCompleted }.map { it.benefit }

        if (uncompletedBenefits.isNotEmpty()) {
            uncompletedBenefits.forEach {
                if (it.conditionType == BenefitCondition.PURCHASED) {
                    val purchasedItem =
                        PurchasedItem.findByItemIdAndPurchaserUid(
                            params.itemId,
                            params.purchaserUid,
                        )
                    if (purchasedItem.isNotEmpty()) {
                        BenefitCompletionLog.create(params.purchaserUid, it.id!!)
                    }
                }

                if (
                    it.conditionType == BenefitCondition.PULLED_DIGITAL_GACHA_10_TIMES ||
                        it.conditionType == BenefitCondition.PULLED_DIGITAL_GACHA_20_TIMES ||
                        it.conditionType == BenefitCondition.PULLED_DIGITAL_GACHA_30_TIMES
                ) {
                    val purchasedItems =
                        PurchasedItem.findByItemIdAndPurchaserUid(
                            itemId = params.itemId,
                            purchaserUid = params.purchaserUid,
                        )

                    val pulledCount = purchasedItems.sumOf { it.quantity }

                    createPullCountConditionTypeBenefitLog(
                        pulledCount,
                        it.id!!,
                        it.conditionType,
                        params.purchaserUid,
                    )
                }
            }
        }
    }

    // 引いた回数特典
    private fun createPullCountConditionTypeBenefitLog(
        pulledCount: Int,
        id: Long,
        conditionType: BenefitCondition,
        purchaserUid: String,
    ) {
        val requiredCount =
            when (conditionType) {
                BenefitCondition.PULLED_DIGITAL_GACHA_10_TIMES -> 10
                BenefitCondition.PULLED_DIGITAL_GACHA_20_TIMES -> 20
                BenefitCondition.PULLED_DIGITAL_GACHA_30_TIMES -> 30
                else -> null
            }

        if (requiredCount != null && pulledCount >= requiredCount) {
            BenefitCompletionLog.create(userUid = purchaserUid, benefitId = id)
        }
    }
}
