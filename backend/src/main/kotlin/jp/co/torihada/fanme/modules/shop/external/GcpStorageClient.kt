package jp.co.torihada.fanme.modules.shop.external

import jakarta.ws.rs.Consumes
import jakarta.ws.rs.POST
import jakarta.ws.rs.Path
import jakarta.ws.rs.QueryParam
import jakarta.ws.rs.core.MediaType
import java.io.InputStream
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient

@Path("/")
@RegisterRestClient(configKey = "gcp-storage-api")
interface GcpStorageClient {

    @POST
    @Path("/upload/storage/v1/b/{bucket}/o")
    @Consumes(MediaType.TEXT_PLAIN)
    fun uploadObject(
        @QueryParam("bucket") bucket: String,
        @QueryParam("name") name: String,
        @QueryParam("uploadType") uploadType: String,
        content: InputStream,
    ): Map<String, Any>
}
