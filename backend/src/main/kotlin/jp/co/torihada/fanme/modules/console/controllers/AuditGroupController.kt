package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jp.co.torihada.fanme.modules.console.usecases.GetAuditGroups
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class AuditGroupController {

    @Inject private lateinit var getAuditGroups: GetAuditGroups

    fun getAuditGroups(odata: OData?): List<AuditGroup> {
        return getAuditGroups.execute(GetAuditGroups.Input(odata)).getOrElse { error ->
            throw error
        }
    }
}
