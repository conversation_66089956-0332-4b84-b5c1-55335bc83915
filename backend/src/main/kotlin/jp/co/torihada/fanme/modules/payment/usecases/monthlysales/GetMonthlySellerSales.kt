package jp.co.torihada.fanme.modules.payment.usecases.monthlysales

import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.modules.payment.dto.MonthlySalesDto
import jp.co.torihada.fanme.modules.payment.models.MonthlySellerSale

@ApplicationScoped
class GetMonthlySellerSales {

    data class Input(
        val sellerUserIds: List<String>,
        val fromYearMonth: String? = null,
        val toYearMonth: String? = null,
    )

    data class Output(val monthlySales: List<MonthlySalesDto>)

    fun execute(input: Input): Result<Output, FanmeException> {

        val allMonthlySales =
            MonthlySellerSale.findBySellerUserIds(
                input.sellerUserIds,
                input.fromYearMonth,
                input.toYearMonth,
            )

        val monthlySalesData =
            allMonthlySales.map { sale ->
                MonthlySalesDto(
                    sellerUserId = sale.sellerUserId,
                    yearMonth = sale.yearMonth ?: "",
                    sellerSalesAmount = sale.sellerSalesAmount,
                    merged = sale.merged,
                    expirationDate = sale.expirationDate,
                )
            }

        return Ok(Output(monthlySalesData))
    }
}
