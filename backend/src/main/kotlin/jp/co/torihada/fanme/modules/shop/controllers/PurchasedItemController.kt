package jp.co.torihada.fanme.modules.shop.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Size
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.ITEM_ID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.ITEM_ID_MUST_BE_POSITIVE
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.USER_ID_IS_REQUIRED
import jp.co.torihada.fanme.modules.shop.Const.ValidationErrorMessages.USER_ID_TOO_MANY_LENGTH
import jp.co.torihada.fanme.modules.shop.usecases.purchasedItem.GetPurchasedItem
import jp.co.torihada.fanme.modules.shop.usecases.purchasedItem.GetPurchasedItems
import jp.co.torihada.fanme.odata.OData

@ApplicationScoped
class PurchasedItemController {
    @Inject private lateinit var getPurchasedItems: GetPurchasedItems
    @Inject private lateinit var getPurchasedItem: GetPurchasedItem

    fun getPurchasedItems(
        userUid: String,
        includeTip: Boolean,
        odata: OData?,
        applyMasking: Boolean,
    ): List<GetPurchasedItems.PurchasedItem> {
        return getPurchasedItems
            .execute(
                GetPurchasedItems.Input(
                    userUid = userUid,
                    includeTip = includeTip,
                    odata = odata,
                    applyMasking = applyMasking,
                )
            )
            .getOrElse { throw it }
    }

    fun getPurchasedItem(
        @NotNull(message = USER_ID_IS_REQUIRED)
        @Size(max = USER_UID_MAX_LENGTH, message = USER_ID_TOO_MANY_LENGTH)
        userUid: String,
        @NotNull(message = ITEM_ID_IS_REQUIRED)
        @Min(1, message = ITEM_ID_MUST_BE_POSITIVE)
        purchasedItemId: Long,
        applyMasking: Boolean,
    ): GetPurchasedItem.PurchasedItemDetail {
        return getPurchasedItem
            .execute(
                GetPurchasedItem.Input(
                    userUid = userUid,
                    purchasedItemId = purchasedItemId,
                    applyMasking = applyMasking,
                )
            )
            .getOrElse { throw it }
    }
}
