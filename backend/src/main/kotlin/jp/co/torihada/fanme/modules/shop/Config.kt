package jp.co.torihada.fanme.modules.shop

import io.quarkus.runtime.annotations.StaticInitSafe
import io.smallrye.config.ConfigMapping

@ConfigMapping(prefix = "config.shop")
@StaticInitSafe
interface Config {
    fun shopFrontUrl(): String

    fun shopPaymentUrl(): String

    fun s3Endpoint(): String

    fun s3BucketName(): String

    fun s3BucketNameForSmapo(): String

    //    fun smapoGcpBucketName(): String
}
