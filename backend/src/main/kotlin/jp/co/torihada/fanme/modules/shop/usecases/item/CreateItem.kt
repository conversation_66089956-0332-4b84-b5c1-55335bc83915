package jp.co.torihada.fanme.modules.shop.usecases.item

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import java.time.Instant
import jp.co.torihada.fanme.exception.FanmeException
import jp.co.torihada.fanme.exception.ResourceNotFoundException
import jp.co.torihada.fanme.modules.shop.Const
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.*
import jp.co.torihada.fanme.modules.shop.models.BenefitFile as BenefitFileModel
import jp.co.torihada.fanme.modules.shop.services.audit.ShopAuditService

@ApplicationScoped
class CreateItem {

    @Inject private lateinit var shopAuditService: ShopAuditService

    data class Input(
        val creatorUid: String,
        val name: String,
        val description: String?,
        val thumbnailUri: String,
        val thumbnailFrom: Int,
        val thumbnailBlurLevel: Int,
        val thumbnailWatermarkLevel: Int,
        val price: Int,
        val available: Boolean,
        val files: List<File>,
        val samples: List<SampleFile>?,
        val benefits: List<BenefitParam>?,
        val tags: List<String>?,
        val itemOption: ItemOptionParam,
        val itemType: ItemType = ItemType.DIGITAL_BUNDLE,
    )

    data class File(
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val price: Int?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val itemThumbnailSelected: Boolean = false,
        val sortOrder: Int = 0,
    )

    data class SampleFile(
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
    )

    data class BenefitFile(
        val name: String,
        val objectUri: String,
        val thumbnailUri: String?,
        val fileType: String,
        val size: Float,
        val duration: Int?,
        val itemThumbnailSelected: Boolean? = null,
        val sortOrder: Int? = null,
    )

    data class BenefitParam(
        val description: String?,
        val conditionType: Int,
        val files: List<BenefitFile>,
    )

    data class ItemOptionParam(
        val isSingleSales: Boolean,
        val qtyTotal: Int?,
        val qtyPerUser: Int?,
        val forSale: ForSale?,
        val password: String?,
        val onSale: OnSale?,
    )

    data class ForSale(val startAt: Instant?, val endAt: Instant?)

    data class OnSale(val discountRate: Float, val startAt: Instant?, val endAt: Instant?)

    @Transactional
    fun execute(params: Input): Result<Item, FanmeException> {
        val shop =
            Shop.findByCreatorUid(params.creatorUid)
                ?: return Err(ResourceNotFoundException("Shop"))

        // チェキのみ一時的に料率を固定で設定
        val marginRate =
            if (params.itemType == ItemType.CHEKI) {
                Const.CHEKI_MARGIN_RATE
            } else {
                shop.marginRate
            }

        val item =
            Item.create(
                shop.id!!,
                params.name,
                params.description,
                params.thumbnailUri,
                params.thumbnailFrom,
                params.thumbnailBlurLevel,
                params.thumbnailWatermarkLevel,
                params.price,
                0,
                params.available,
                params.itemType,
                marginRate,
            )

        val files =
            params.files.map {
                ItemFile.create(
                    item.id!!,
                    it.name,
                    it.objectUri,
                    it.thumbnailUri,
                    null, // maskedThumbnailUriはデジタルガチャ用のため、ここではnull
                    it.price,
                    it.fileType,
                    it.size,
                    it.duration,
                    it.itemThumbnailSelected,
                    it.sortOrder,
                )
            }

        val itemFileType = Util.getItemFileType(files)
        item.fileType = itemFileType
        item.persist()

        ItemOption.create(
            item.id!!,
            params.itemOption.isSingleSales,
            params.itemOption.qtyTotal,
            params.itemOption.qtyPerUser,
            params.itemOption.forSale?.startAt,
            params.itemOption.forSale?.endAt,
            params.itemOption.password,
        )
        params.itemOption.onSale?.let {
            ItemOnSale.create(item.id!!, it.discountRate, it.startAt, it.endAt)
        }

        val samples =
            params.samples?.map {
                Sample.create(
                    item.id!!,
                    it.name,
                    it.objectUri,
                    it.thumbnailUri,
                    it.fileType,
                    it.size,
                    it.duration,
                )
            }

        var benefitFiles: List<BenefitFileModel> = mutableListOf()
        if (params.benefits != null) {
            params.benefits.let {
                it.forEach { benefitParam ->
                    val benefit =
                        Benefit.create(
                            item.id!!,
                            benefitParam.description,
                            BenefitCondition.entries.find { it.value == benefitParam.conditionType }
                                ?: BenefitCondition.PURCHASED,
                        )
                    benefitFiles +=
                        benefitParam.files.map { file ->
                            BenefitFileModel.create(
                                benefit.id!!,
                                file.name,
                                file.objectUri,
                                file.thumbnailUri,
                                file.fileType,
                                file.size,
                                file.duration ?: 0,
                            )
                        }
                }
            }
        }

        if (params.tags != null) {
            val existTags = Tag.findByShopId(shop.id!!)
            params.tags.forEach {
                if (existTags.find { t -> t.tag == it } == null) {
                    Tag.create(shop.id!!, it)
                }
                ItemTag.create(item.id!!, it)
            }
        }

        // 監査データの作成
        shopAuditService.createAuditDataForShopItem(
            params.creatorUid,
            ShopAuditService.OperationType.INSERT,
            item,
            files,
            samples,
            benefitFiles.ifEmpty { null },
        )

        return Ok(item)
    }
}
