package jp.co.torihada.fanme.modules.fanme.controllers

import BaseController
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditStatus
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup.AuditType
import jp.co.torihada.fanme.modules.fanme.models.AuditGroupMetadata
import jp.co.torihada.fanme.modules.fanme.models.AuditObject.AssetType
import jp.co.torihada.fanme.modules.fanme.services.audit.AuditGroupServiceAuditObject
import jp.co.torihada.fanme.modules.fanme.usecases.audit.CreateAuditGroup
import jp.co.torihada.fanme.modules.fanme.usecases.audit.GetAuditGroups
import jp.co.torihada.fanme.modules.fanme.usecases.audit.UpdateAuditStatus
import jp.co.torihada.fanme.odata.OData

data class AuditGroupControllerAuditObject(
    val bucket: String,
    val filePath: String,
    val assetType: AssetType,
)

data class AuditGroupControllerCreateInput(
    val auditType: String,
    val userUid: String,
    val operationType: String,
    val metadata: AuditGroupMetadata,
    val auditObjects: List<AuditGroupControllerAuditObject>,
)

data class AuditGroupControllerUpdateStatusInput(
    val auditGroupId: Long,
    val status: AuditStatus,
    val comment: String? = null,
    val auditedUserUid: String? = null,
)

@ApplicationScoped
class AuditGroupController : BaseController() {

    @Inject private lateinit var createAuditGroup: CreateAuditGroup
    @Inject private lateinit var updateAuditStatus: UpdateAuditStatus
    @Inject private lateinit var getAuditGroups: GetAuditGroups

    /** ショップ監査情報の作成 */
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun createAuditGroup(input: AuditGroupControllerCreateInput): Long {
        return createAuditGroup.execute(
            userUid = input.userUid,
            auditType = exchangeAuditType(input.auditType),
            operationType = AuditGroup.OperationType.fromValue(input.operationType),
            metadata = input.metadata,
            auditObjects =
                input.auditObjects.map {
                    AuditGroupServiceAuditObject(
                        bucket = it.bucket,
                        filePath = it.filePath,
                        assetType = it.assetType,
                    )
                },
        )
    }

    private fun exchangeAuditType(auditType: String): AuditType {
        return when (auditType) {
            "shop" -> AuditType.SHOP
            "shop_item" -> AuditType.SHOP_ITEM
            "fanme_profile" -> AuditType.FANME_PROFILE
            "fanme_content" -> AuditType.FANME_CONTENT
            else -> throw IllegalArgumentException("Invalid audit type: $auditType")
        }
    }

    /** 監査ステータスの更新 */
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    fun updateAuditStatus(input: AuditGroupControllerUpdateStatusInput) {
        updateAuditStatus.execute(
            auditGroupId = input.auditGroupId,
            status = input.status,
            comment = input.comment,
            auditedUserUid = input.auditedUserUid,
        )
    }

    fun getAuditGroups(odata: OData?): List<AuditGroup> {
        return getAuditGroups.execute(GetAuditGroups.Input(odata)).getOrElse { throw it }
    }
}
