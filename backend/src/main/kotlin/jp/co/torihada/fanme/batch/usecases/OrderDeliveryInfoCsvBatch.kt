package jp.co.torihada.fanme.batch.usecases

import com.github.michaelbull.result.Err
import com.github.michaelbull.result.Ok
import com.github.michaelbull.result.Result
import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import java.nio.file.Files
import java.nio.file.Path
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import jp.co.torihada.fanme.Config
import jp.co.torihada.fanme.modules.fanme.controllers.FanmeCustomerController
import jp.co.torihada.fanme.modules.shop.Config as ShopConfig
import jp.co.torihada.fanme.modules.shop.Util
import jp.co.torihada.fanme.modules.shop.models.PurchasedItem
import jp.co.torihada.fanme.modules.shop.services.aws.S3
import org.jboss.logging.Logger

@ApplicationScoped
class OrderDeliveryInfoCsvBatch {

    @Inject private lateinit var logger: Logger
    @Inject private lateinit var config: Config
    @Inject private lateinit var shopConfig: ShopConfig
    @Inject private lateinit var s3: S3
    @Inject private lateinit var fanmeCustomerController: FanmeCustomerController

    //    @Inject @RestClient private lateinit var gcpStorageClient: GcpStorageClient

    fun execute() {
        val jstZone = ZoneId.of("Asia/Tokyo")

        val todayJst = LocalDate.now(jstZone)
        val yesterdayJst = todayJst.minusDays(1)

        // DBにはUTCで保存されているため、JSTの日時をUTCに変換して検索
        val startOfYesterday = yesterdayJst.atStartOfDay(jstZone).toInstant()
        val endOfYesterday = todayJst.atStartOfDay(jstZone).toInstant()

        logger.info(
            "Search purchasedItems with successful payment status between $startOfYesterday and $endOfYesterday"
        )

        // 購入日時が昨日の0時から23時59分59秒までのチェキのPurchasedItemを取得
        val purchasedItems =
            PurchasedItem.findOrderedChekiItemByTerms(startOfYesterday, endOfYesterday)

        logger.info("Found ${purchasedItems.size} purchasedItems with successful payment status")

        // CSVに入れるデータを作成
        val csvContent = buildCsvContent(purchasedItems)

        val tempFile = Files.createTempFile("cheki_purchase_data", ".csv")
        Files.write(tempFile, csvContent.toByteArray())

        val dateStr = DateTimeFormatter.ofPattern("yyyyMMdd").format(yesterdayJst)
        val distDir =
            "${config.envKind()}/${yesterdayJst.year}/${yesterdayJst.monthValue}/${yesterdayJst.dayOfMonth}/"
        val remotePath = "${distDir}cheki_purchase_data$dateStr.csv"

        try {
            // S3へのアップロード
            uploadFileToS3(remotePath, tempFile).getOrElse { e ->
                logger.error("Failed to upload file to S3: $remotePath", e)
            }
            logger.info("Finish uploading CSV to S3")

            //            // smapo GCPへアップロード
            //            uploadFileToSmapo(fileName, tempFile).getOrElse { e ->
            //                logger.error("Failed to upload file to GCP Storage: $fileName", e)
            //            }
            //            logger.info("Successfully uploaded CSV to GCP Storage")
        } finally {
            // 一時ファイルの削除
            try {
                Files.delete(tempFile)
            } catch (e: Exception) {
                logger.warn("Failed to delete temporary file", e)
            }
        }

        logger.info("OrderDeliveryInfoCsvBatch end")
    }

    private fun buildCsvContent(purchasedItems: List<PurchasedItem>): String {
        val header = "オーダーID,商品ID,商品タイプ,商品名,購入数,商品単価,合計,購入日時,名前（購入者）,郵便番号（購入者）,住所（購入者）,電話番号（購入者）"
        val customers =
            fanmeCustomerController.listFanmeCustomers(
                purchasedItems.map { it.purchaserUid }.distinct()
            )

        return purchasedItems
            .mapNotNull { item ->
                customers
                    .firstOrNull { it.creatorUid == item.purchaserUid }
                    ?.let { customer ->
                        listOf(
                                item.order.id,
                                item.item.id,
                                "チェキ",
                                item.item.name,
                                item.quantity,
                                item.item.price,
                                item.item.price * item.quantity,
                                item.purchasedAt?.let { Util.toJSTLocalDateTimeString(it) } ?: "",
                                "${customer.lastName} ${customer.firstName}",
                                customer.postalCode,
                                "${customer.prefecture}${customer.city}${customer.street}${customer.building ?: ""}",
                                // 先頭の0が消えないように
                                "'${customer.phoneNumber}",
                            )
                            .joinToString(",")
                    }
                    ?: run {
                        logger.warn(
                            "Customer information not found for purchaserUid: ${item.purchaserUid}"
                        )
                        null
                    }
            }
            .let { rows -> listOf(header, rows.joinToString("\n")).joinToString("\n") }
    }

    private fun uploadFileToS3(remotePath: String, filePath: Path): Result<String, Exception> {
        return try {
            s3.putObject(shopConfig.s3BucketNameForSmapo(), remotePath, "text/csv", filePath)
            Ok(remotePath)
        } catch (e: Exception) {
            Err(e)
        }
    }

    //    fun uploadFileToSmapo(fileName: String, filePath: Path): Result<String, Exception> {
    //        try {
    //            Files.newInputStream(filePath).use { inputStream ->
    //                gcpStorageClient.uploadObject(
    //                    bucket = shopConfig.smapoGcpBucketName(),
    //                    name = "fanme/$fileName",
    //                    uploadType = "media",
    //                    content = inputStream
    //                )
    //                return Ok(fileName)
    //            }
    //        } catch (e: Exception) {
    //            return Err(e)
    //        }
    //    }
}
