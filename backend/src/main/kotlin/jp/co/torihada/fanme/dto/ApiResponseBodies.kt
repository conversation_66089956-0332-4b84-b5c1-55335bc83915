package jp.co.torihada.fanme.dto

import jp.co.torihada.fanme.exception.ErrorObject
import jp.co.torihada.fanme.modules.console.dto.ConsoleUserDetail
import jp.co.torihada.fanme.modules.console.models.Agency
import jp.co.torihada.fanme.modules.console.models.ConsoleUser
import jp.co.torihada.fanme.modules.console.usecases.GetAgencySales
import jp.co.torihada.fanme.modules.fanme.controllers.RankingEventController.RankingEventWithBoost
import jp.co.torihada.fanme.modules.fanme.models.AuditGroup
import jp.co.torihada.fanme.modules.fanme.models.User
import jp.co.torihada.fanme.modules.fanme.services.address.SuggestedAddressEntity
import jp.co.torihada.fanme.modules.fanme.services.fanmeCustomer.FanmeCustomerEntity
import jp.co.torihada.fanme.modules.shop.controllers.DigitalGachaController.GetDigitalGachaCompleteBadgeRankingResponse
import jp.co.torihada.fanme.modules.shop.controllers.OrderController
import jp.co.torihada.fanme.modules.shop.usecases.badge.GetBadge
import jp.co.torihada.fanme.modules.shop.usecases.cartItem.GetCartItems
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.GetPullableGachaCount
import jp.co.torihada.fanme.modules.shop.usecases.digitalGacha.PullDigitalGachaItems
import jp.co.torihada.fanme.modules.shop.usecases.file.GetDownloadUrls
import jp.co.torihada.fanme.modules.shop.usecases.item.GetItem
import jp.co.torihada.fanme.modules.shop.usecases.order.GetTipUpperLimit
import jp.co.torihada.fanme.modules.shop.usecases.purchasedItem.GetPurchasedItem
import jp.co.torihada.fanme.modules.shop.usecases.shop.GetShop

data class ItemResponseBody(
    override val data: ItemData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class ItemData(val item: GetItem.Item)
}

data class ShopResponseBody(
    override val data: ShopData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class ShopData(val shop: GetShop.Shop)
}

data class RankingEventInfoResponseBody(
    override val data: EventData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class EventData(val event: RankingEventWithBoost)
}

data class TipLimitResponseBody(
    override val data: TipLimitData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class TipLimitData(val tip_limit: GetTipUpperLimit.TipUpperLimit)
}

data class PurchaseItemResponseBody(
    override val data: PurchaseItemData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class PurchaseItemData(val purchasedItem: GetPurchasedItem.PurchasedItemDetail)
}

data class DownloadUrlResponseBody(
    override val data: DownloadUrlData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class DownloadUrlData(val downloadUrls: List<GetDownloadUrls.DownloadUrl>)
}

data class FanmeCustomerResponseBody(
    override val data: FanmeCustomerData?,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class FanmeCustomerData(val fanmeCustomer: FanmeCustomerEntity)
}

data class SuggestAddressResponseBody(
    override val data: SuggestAddressData?,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class SuggestAddressData(val suggestedAddress: SuggestedAddressEntity)
}

data class CartItemsResponseBody(
    val cart: GetCartItems.CartItemData,
    val errors: List<ErrorObject>? = null,
)

data class SingleOrderResponseBody(
    override val data: SingleOrderData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class SingleOrderData(val order: OrderController.OrderResult)
}

data class AgenciesResponseBody(
    override val data: AgencyData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class AgencyData(val agencies: List<Agency>)
}

data class CompleteBadgeResponseBody(
    override val data: CompleteBadgeData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class CompleteBadgeData(val badge: GetBadge.Output)
}

data class BadgeRankingResponseBody(
    override val data: BadgeRankingData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class BadgeRankingData(val ranking: List<GetDigitalGachaCompleteBadgeRankingResponse>)
}

data class DigitalGachaPullResponseBody(
    override val data: DigitalGachaPullData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class DigitalGachaPullData(
        val files: List<PullDigitalGachaItems.FileForPullDigitalGachaItems>
    )
}

data class DigitalGachaPullableCountResponseBody(
    override val data: DigitalGachaPullableCountData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class DigitalGachaPullableCountData(val item: GetPullableGachaCount.Output)
}

data class ConsoleUsersResponseBody(
    override val data: ConsoleUsersData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class ConsoleUsersData(val consoleUsers: List<ConsoleUser>)
}

data class ConsoleUserResponseBody(
    override val data: ConsoleUserData?,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class ConsoleUserData(val user: ConsoleUserDetail)
}

data class UsersResponseBody(
    override val data: UsersData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class UsersData(val users: List<User>)
}

data class AgencySalesResponseBody(
    override val data: AgencySales,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class AgencySales(val agencySales: List<GetAgencySales.CreatorSales>)
}

data class AuditGroupsResponseBody(
    override val data: AuditGroupsData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class AuditGroupsData(val auditGroups: List<AuditGroup>)
}

data class AuditStatusResponseBody(
    override val data: AuditStatusData,
    override val errors: List<ErrorObject>? = null,
) : BaseResponseBody(data, errors) {
    data class AuditStatusData(val result: Boolean)
}
