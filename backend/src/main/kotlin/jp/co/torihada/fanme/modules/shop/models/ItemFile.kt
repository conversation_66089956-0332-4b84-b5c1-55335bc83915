package jp.co.torihada.fanme.modules.shop.models

import com.fasterxml.jackson.annotation.JsonIgnore
import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import jakarta.persistence.*
import jakarta.validation.constraints.*
import jp.co.torihada.fanme.modules.shop.Const.FILE_NAME_MAX_LENGTH
import jp.co.torihada.fanme.modules.shop.Const.MAX_PRICE

@PersistenceUnit(name = "item_file")
@Entity
@Table(name = "item_files")
class ItemFile : BaseModel() {

    @NotNull
    @ManyToOne
    @JoinColumn(name = "item_id", nullable = false, updatable = false)
    var item: Item = Item()

    @NotBlank
    @Size(max = FILE_NAME_MAX_LENGTH)
    @Column(nullable = false, length = FILE_NAME_MAX_LENGTH)
    var name: String = ""

    @NotBlank @Column(name = "object_uri", columnDefinition = "text") var objectUri: String? = null

    @Column(name = "thumbnail_uri", columnDefinition = "text") var thumbnailUri: String? = null

    @Column(name = "masked_thumbnail_uri", columnDefinition = "text")
    var maskedThumbnailUri: String? = null

    @Max(MAX_PRICE) @Min(0) @Column var price: Int? = 0

    @NotBlank
    @Column(name = "file_type", nullable = false, length = 10)
    var fileType: String = "any" // image, audio, video, any

    @Column var size: Float = 0.0f

    @Min(0)
    @Column(name = "duration", nullable = false)
    var duration: Int = 0 // audio or video duration in seconds. 0 means not audio or video.

    @NotNull
    @Column(name = "item_thumbnail_selected", nullable = false)
    var itemThumbnailSelected: Boolean = false

    @NotNull @Column(name = "sort_order", nullable = false) var sortOrder: Int = 0

    @OneToOne(mappedBy = "itemFile", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var gachaItemFile: GachaItemFile? = null

    @OneToMany(mappedBy = "itemFile", cascade = [CascadeType.ALL], orphanRemoval = true)
    @JsonIgnore
    var gachaReceivedFile: MutableSet<GachaReceivedFile> = mutableSetOf()

    companion object : PanacheCompanion<ItemFile> {
        fun create(
            itemId: Long,
            name: String,
            objectUri: String,
            thumbnailUri: String?,
            maskedThumbnailUri: String?,
            price: Int?,
            fileType: String,
            size: Float,
            duration: Int?,
            itemThumbnailSelected: Boolean,
            sortOrder: Int,
        ): ItemFile {
            val itemFiles = ItemFile()
            itemFiles.item = Item.findById(itemId)!!
            itemFiles.name = name
            itemFiles.objectUri = objectUri
            itemFiles.thumbnailUri = thumbnailUri
            itemFiles.maskedThumbnailUri = maskedThumbnailUri
            itemFiles.price = price
            itemFiles.fileType = fileType
            itemFiles.size = size
            itemFiles.duration = duration ?: 0
            itemFiles.itemThumbnailSelected = itemThumbnailSelected
            itemFiles.sortOrder = sortOrder
            itemFiles.persist()
            return itemFiles
        }

        fun update(
            id: Long,
            name: String,
            objectUri: String,
            thumbnailUri: String?,
            maskedThumbnailUri: String?,
            price: Int?,
            fileType: String,
            size: Float,
            duration: Int?,
            itemThumbnailSelected: Boolean,
            sortOrder: Int,
        ): ItemFile {
            val itemFiles = findById(id)!!
            itemFiles.name = name
            itemFiles.objectUri = objectUri
            itemFiles.thumbnailUri = thumbnailUri
            itemFiles.maskedThumbnailUri = maskedThumbnailUri
            itemFiles.price = price
            itemFiles.fileType = fileType
            itemFiles.size = size
            itemFiles.duration = duration ?: 0
            itemFiles.itemThumbnailSelected = itemThumbnailSelected
            itemFiles.sortOrder = sortOrder
            itemFiles.persist()
            return itemFiles
        }

        fun findByIds(ids: List<Long>): List<ItemFile> {
            return find("id in ?1", ids).list()
        }

        fun findByItemId(itemId: Long): List<ItemFile> {
            return find("item.id", itemId).list()
        }
    }
}
