package jp.co.torihada.fanme.endpoints.shop

import io.quarkus.security.identity.SecurityIdentity
import jakarta.inject.Inject
import jakarta.ws.rs.*
import jakarta.ws.rs.container.ContainerRequestContext
import jakarta.ws.rs.core.MediaType
import jakarta.ws.rs.core.Response
import jp.co.torihada.fanme.dto.ItemResponseBody
import jp.co.torihada.fanme.endpoints.BaseCreatorAccountIdentityEndpoint
import jp.co.torihada.fanme.endpoints.ResponseEntity
import jp.co.torihada.fanme.endpoints.Util
import jp.co.torihada.fanme.modules.shop.controllers.ItemController
import jp.co.torihada.fanme.odata.OData
import org.eclipse.microprofile.openapi.annotations.responses.APIResponse
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema

@Path("/shops/{creator_account_identity}/items")
class ItemEndpoint : BaseCreatorAccountIdentityEndpoint() {

    @Inject lateinit var securityIdentity: SecurityIdentity
    @Inject lateinit var requestContext: ContainerRequestContext
    @Inject lateinit var handler: ItemController
    @Inject lateinit var util: Util

    @GET
    fun getItems(
        @QueryParam("available") available: Boolean?,
        @QueryParam("tag") tag: String?,
    ): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity)
            val odata = requestContext.getProperty("odata") as OData?
            val result = handler.getItems(creatorUid, userUid, available, tag, odata, true)
            val entity = ResponseEntity(result, "items")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }

    @GET
    @Path("/{item_id}")
    @Produces(MediaType.APPLICATION_JSON)
    @APIResponse(responseCode = "200")
    @APIResponseSchema(ItemResponseBody::class)
    @APIResponse(responseCode = "500", description = "Internal Server Error")
    fun getItem(@PathParam("item_id") id: Long): Response {
        return try {
            val creatorUid = util.getCreatorUid(requestContext)
            val userUid = util.getCurrentUserUid(securityIdentity)
            val result = handler.getItem(id, creatorUid, userUid)
            val entity = ResponseEntity(result, "item")
            Response.ok(entity).build()
        } catch (e: Exception) {
            Response.serverError().entity(e).build()
        }
    }
}
