package jp.co.torihada.fanme.modules.shop.models

import io.quarkus.hibernate.orm.panache.kotlin.PanacheCompanion
import io.quarkus.panache.common.Parameters
import jakarta.persistence.*
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotNull
import java.time.Instant
import jp.co.torihada.fanme.modules.shop.Const.USER_UID_MAX_LENGTH

enum class BadgeType(val value: Int) {
    DIGITAL_GACHA_COMPLETE(1)
}

@Converter(autoApply = true)
class BadgeTypeConverter : AttributeConverter<BadgeType, Int> {
    override fun convertToDatabaseColumn(attribute: BadgeType?): Int? {
        return attribute?.value
    }

    override fun convertToEntityAttribute(dbData: Int?): BadgeType? {
        return dbData?.let { value -> BadgeType.entries.find { it.value == value } }
    }
}

@PersistenceUnit(name = "user_badge")
@Entity
@Table(name = "user_badges")
class UserBadge : BaseModel() {
    @NotNull @ManyToOne @JoinColumn(name = "item_id") var item: Item = Item()

    @NotBlank
    @Column(name = "user_uid", nullable = false, updatable = false, length = USER_UID_MAX_LENGTH)
    var userUid: String = ""

    @NotNull
    @Column(name = "badge_type", nullable = false)
    @Convert(converter = BadgeTypeConverter::class)
    var badgeType: BadgeType = BadgeType.DIGITAL_GACHA_COMPLETE

    companion object : PanacheCompanion<UserBadge> {
        fun create(itemId: Long, userUid: String, badgeType: BadgeType): UserBadge {
            val userBadge = UserBadge()
            userBadge.item = Item.findById(itemId)!!
            userBadge.userUid = userUid
            userBadge.badgeType = badgeType
            userBadge.persist()
            return userBadge
        }

        fun findByItemIdAndUserUidAndBadgeType(
            itemId: Long,
            userUid: String,
            badgeType: BadgeType,
        ): UserBadge? {
            return find(
                    "item.id = ?1 and userUid = ?2 and badgeType = ?3",
                    itemId,
                    userUid,
                    badgeType,
                )
                .firstResult()
        }

        data class UserBadgeWithRank(val userUid: String, val createdAt: Instant)

        fun findByCreatedAtLimit(
            itemId: Long,
            badgeType: BadgeType,
            limit: Int,
        ): List<UserBadgeWithRank> {
            val query =
                """
            SELECT 
                ub.userUid AS userUid,
                ub.createdAt AS createdAt
            FROM UserBadge ub
            WHERE ub.badgeType = :badgeType AND ub.item.id = :itemId
            ORDER BY ub.createdAt ASC
            LIMIT :limit
        """
            val result =
                find(
                        query,
                        Parameters.with("badgeType", badgeType)
                            .and("itemId", itemId)
                            .and("limit", limit),
                    )
                    .project(Tuple::class.java)
                    .list()

            return result.map {
                UserBadgeWithRank(
                    userUid = it.get("userUid", String::class.java),
                    createdAt = it.get("createdAt", Instant::class.java),
                )
            }
        }
    }
}
