package jp.co.torihada.fanme.modules.console.utils

import jakarta.enterprise.context.ApplicationScoped
import jp.co.torihada.fanme.exception.ForbiddenAccessException
import jp.co.torihada.fanme.modules.console.const.UserRole

@ApplicationScoped
class SecurityUtils {

    fun validateAgentAccess(agencyId: Long, currentUserRole: String?, currentUserAgencyId: Long?) {
        if (currentUserRole == UserRole.AGENT_VALUE) {
            if (currentUserAgencyId != agencyId) {
                throw ForbiddenAccessException()
            }
        }
    }
}
