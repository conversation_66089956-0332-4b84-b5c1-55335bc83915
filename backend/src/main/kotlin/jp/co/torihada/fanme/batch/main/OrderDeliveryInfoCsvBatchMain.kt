package jp.co.torihada.fanme.batch.main

import io.quarkus.runtime.QuarkusApplication
import jakarta.inject.Inject
import jakarta.transaction.Transactional
import jp.co.torihada.fanme.batch.usecases.OrderDeliveryInfoCsvBatch
import org.jboss.logging.Logger

class OrderDeliveryInfoCsvBatchMain : QuarkusApplication {

    @Inject private lateinit var logger: Logger
    @Inject private lateinit var orderDeliveryInfoCsvBatch: OrderDeliveryInfoCsvBatch

    @Transactional
    override fun run(vararg args: String?): Int {
        logger.info("OrderDeliveryInfoCsvBatchMain start")

        try {
            orderDeliveryInfoCsvBatch.execute()
            logger.info("OrderDeliveryInfoCsvBatchMain end")
            return 0
        } catch (e: Exception) {
            logger.error("OrderDeliveryInfoCsvBatchMain error", e)
            return 1
        }
    }
}
