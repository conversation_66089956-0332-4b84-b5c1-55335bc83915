package jp.co.torihada.fanme.modules.console.controllers

import com.github.michaelbull.result.getOrElse
import jakarta.enterprise.context.ApplicationScoped
import jakarta.inject.Inject
import jakarta.validation.constraints.Min
import jp.co.torihada.fanme.modules.console.dto.ConsoleUserDetail
import jp.co.torihada.fanme.modules.console.usecases.GetUserUseCaseInput
import jp.co.torihada.fanme.modules.console.usecases.IGetUser

interface IUserController {
    fun getUser(@Min(1) id: Long): ConsoleUserDetail
}

@ApplicationScoped
class UserController @Inject constructor(private val getUser: IGetUser) : IUserController {

    override fun getUser(@Min(1) id: Long): ConsoleUserDetail {
        return getUser.execute(GetUserUseCaseInput(id)).getOrElse { error -> throw error }
    }
}
