---
openapi: 3.1.0
paths:
  /address/suggestion/postal-code/{postal-code}:
    get:
      operationId: suggestAddress
      tags:
      - FANME
      parameters:
      - name: postal-code
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuggestAddressResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Suggest Address
      security:
      - SecurityScheme:
        - LoginUser
  /auth/fanme:
    get:
      summary: Fanme OAuth認証開始
      description: OAuth認証を開始し、認証プロバイダへのリダイレクト先URLを取得するエンドポイント
      operationId: fanme
      tags:
      - FANME
      parameters:
      - description: 処理種別
        name: proc
        in: query
        schema:
          type:
          - string
          - "null"
      - description: 戻り先URL
        name: return_url
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: 認証プロバイダへのリダイレクトURLを返す
  /auth/fanme/callback:
    get:
      summary: OAuthコールバック処理
      description: OAuthプロバイダからのコールバックを受け取り、認証処理を実行する
      operationId: callback
      tags:
      - FANME
      parameters:
      - description: 認証コード
        name: code
        in: query
        schema:
          type: string
        required: true
      - description: 戻り先URL
        name: return_url
        in: query
        schema:
          type: string
        required: true
      - description: 状態値
        name: state
        in: query
        schema:
          type: string
        required: true
      - description: ログイン失敗回数
        name: login_failure_count
        in: cookie
        schema:
          type:
          - string
          - "null"
      - description: Cookie内のNonce
        name: nonce
        in: cookie
        schema:
          type:
          - string
          - "null"
      - description: Cookie内の状態値
        name: state
        in: cookie
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: 認証成功時のレスポンスを返す
        "500":
          description: 内部サーバエラーが発生した場合のレスポンスを返す
  /cards:
    post:
      operationId: registerCard
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RegisterCardRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Register Card
      tags:
      - Card Endpoints
    get:
      operationId: fetchCard
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Fetch Card
      tags:
      - Card Endpoints
      security:
      - SecurityScheme:
        - LoginUser
  /cards/update:
    put:
      operationId: updateCard
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCardRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Update Card
      tags:
      - Card Endpoints
  /cards/{card_sequence}:
    delete:
      operationId: deleteCard
      parameters:
      - name: card_sequence
        in: path
        required: true
        schema:
          type: integer
          format: int32
      responses:
        "200":
          description: OK
      summary: Delete Card
      tags:
      - Card Endpoints
  /console/agencies:
    get:
      operationId: getAgencies
      tags:
      - CONSOLE
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AgenciesResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Agencies
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/agencies/{agency_id}/sales:
    get:
      operationId: getAgencySales
      tags:
      - CONSOLE
      parameters:
      - name: agency_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: from
        in: query
        schema:
          type:
          - string
          - "null"
      - name: to
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: 成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AgencySalesResponseBody"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      summary: Get Agency Sales
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/agencies/{agency_id}/users:
    get:
      operationId: getAgencyUsers
      tags:
      - CONSOLE
      parameters:
      - name: agency_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: 成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UsersResponseBody"
        "400":
          description: Bad Request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
        "404":
          description: Not Found
        "500":
          description: Internal Server Error
      summary: Get Agency Users
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
        - AGENT
  /console/audit-groups:
    get:
      operationId: getAuditGroups
      tags:
      - CONSOLE
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuditGroupsResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Audit Groups
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/audit-groups/{audit_group_id}/status:
    put:
      operationId: updateAuditStatus
      tags:
      - CONSOLE
      parameters:
      - name: audit_group_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateStatusRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuditStatusResponseBody"
        "400":
          description: Bad Request
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Update Audit Status
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/users:
    get:
      operationId: getConsoleUsers
      tags:
      - CONSOLE
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConsoleUsersResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Console Users
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /console/users/{id}:
    get:
      operationId: getById
      tags:
      - CONSOLE
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: クリエイター情報の取得成功
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConsoleUserResponseBody"
        "400":
          description: 不正なリクエスト
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBody"
        "403":
          description: アクセス権限がありません
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBody"
        "404":
          description: 指定されたIDのクリエイターが見つかりません
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBody"
        "500":
          description: サーバー内部エラー
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BaseResponseBody"
        "401":
          description: Not Authorized
      summary: Get By Id
      security:
      - SecurityScheme:
        - SUPER
        - BIZ
  /email/payment:
    post:
      operationId: sendPaymentEmail
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SendEmailRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Send Payment Email
      tags:
      - Email Endpoint
  /fanme/content_blocks/create_with_detail:
    post:
      operationId: createWithDetail
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateContentWithDetailRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create With Detail
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/fanme-customers:
    post:
      operationId: saveFanmeCustomer
      tags:
      - FANME
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveFanmeCustomerRequest"
        required: true
      responses:
        "200":
          description: OK
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Save Fanme Customer
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getFanmeCustomer
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/FanmeCustomerResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Fanme Customer
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/users/current:
    get:
      operationId: getCurrentUser
      tags:
      - FANME
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema: {}
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Current User
      security:
      - SecurityScheme:
        - LoginUser
  /fanme/users/id_token:
    get:
      operationId: getIdToken
      tags:
      - FANME
      parameters:
      - name: creator_login_state
        in: cookie
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema: {}
      summary: Get Id Token
  /fanme/users/{user_uuid}:
    get:
      operationId: getUser
      tags:
      - FANME
      parameters:
      - name: user_uuid
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema: {}
      summary: Get User
  /hc:
    get:
      operationId: healthCheck
      responses:
        "200":
          description: OK
      summary: Health Check
      tags:
      - Health Check Endpoints
  /orders:
    put:
      operationId: updateOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateOrderRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Update Order
      tags:
      - Order Endpoint
    get:
      operationId: getOrders
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Orders
      tags:
      - Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrderRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Order
      tags:
      - Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /orders/convenience-fees:
    get:
      operationId: getConvenienceFees
      responses:
        "200":
          description: OK
      summary: Get Convenience Fees
      tags:
      - Order Endpoint
  /orders/finalize-credit-card-3d-secure:
    post:
      operationId: finalizeCreditCard3DSecure
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FinalizeCreditCard3DSecureRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Finalize Credit Card 3 D Secure
      tags:
      - Order Endpoint
  /orders/tip-upper-limit/{creator_account_identity}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getTipLimit
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TipLimitResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Tip Limit
      tags:
      - Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current:
    put:
      operationId: updateShop
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateShopRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Shop
      tags:
      - Current User Shop Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getCurrentUserShop
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Shop
      tags:
      - Current User Shop Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createShop
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateShopRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Shop
      tags:
      - Current User Shop Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/digital-gacha:
    post:
      operationId: createDigitalGachaItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateDigitalGachaItemRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Digital Gacha Item
      tags:
      - Current User Digital Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/digital-gacha/{item_id}:
    put:
      operationId: updateDigitalGachaItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateDigitalGachaItemRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Digital Gacha Item
      tags:
      - Current User Digital Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/files/download-url:
    post:
      operationId: getDownloadUrl
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetDownloadUrlRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DownloadUrlResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Get Download Url
      tags:
      - File Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/files/presigned-url:
    post:
      operationId: getPreSignedUrl
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetPreSignedUrlRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Get Pre Signed Url
      tags:
      - File Endpoint
  /shops/current/files/upload-url:
    post:
      operationId: getUploadUrl
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetUploadUrlRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Get Upload Url
      tags:
      - File Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/items:
    get:
      operationId: getCurrentUserItems
      parameters:
      - name: available
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: tag
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Items
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrUpdateItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Item
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/items/sort:
    put:
      operationId: sortItems
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SortItemsRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Sort Items
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/items/{item_id}:
    put:
      operationId: updateItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrUpdateItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Item
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    get:
      operationId: getCurrentUserItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      - name: include_deleted
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Item
      tags:
      - Current User Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/purchased-items:
    get:
      operationId: getPurchasedItems
      parameters:
      - name: include_tip
        in: query
        schema:
          type:
          - boolean
          - "null"
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Purchased Items
      tags:
      - Purchased Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/purchased-items/{purchased_item_id}:
    get:
      operationId: getPurchasedItem
      parameters:
      - name: purchased_item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/PurchaseItemResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Purchased Item
      tags:
      - Purchased Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/current/sales-history:
    get:
      operationId: getSalesHistories
      responses:
        "200":
          description: OK
      summary: Get Sales Histories
      tags:
      - Sales History Endpoint
  /shops/digital-gacha/pull:
    post:
      operationId: pull
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/PullDigitalGachaRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DigitalGachaPullResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Pull
      tags:
      - Digital Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/digital-gacha/{item_id}/complete-badge:
    get:
      operationId: getCompleteBadge
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompleteBadgeResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Complete Badge
      tags:
      - Digital Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/digital-gacha/{item_id}/complete-badge-ranking:
    get:
      operationId: getCompleteBadgeRanking
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BadgeRankingResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Complete Badge Ranking
      tags:
      - Digital Gacha Endpoint
  /shops/digital-gacha/{item_id}/pullable-count:
    get:
      operationId: getPullableGachaCount
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DigitalGachaPullableCountResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Pullable Gacha Count
      tags:
      - Digital Gacha Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/ranking_event_info/creator/{creator_account_identity}/active:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getCreatorActive
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RankingEventInfoResponseBody"
        "204":
          description: No active event found
        "500":
          description: Internal Server Error
      summary: Get Creator Active
      tags:
      - Ranking Event Info Endpoint
  /shops/{creator_account_identity}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getShop
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ShopResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Shop
      tags:
      - Shop Endpoint
  /shops/{creator_account_identity}/cart-items:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getCartItems
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CartItemsResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Cart Items
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createCartItem
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateCartItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Cart Item
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/cart-items/check-price:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    post:
      operationId: checkCartItemPrice
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckCartItemPriceRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Check Cart Item Price
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/cart-items/invalid-items:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    delete:
      operationId: getInvalidCartItems
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Invalid Cart Items
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/cart-items/{cart_item_id}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    put:
      operationId: updateCartItem
      parameters:
      - name: cart_item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateCartItemRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Update Cart Item
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    delete:
      operationId: deleteCartItem
      parameters:
      - name: cart_item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Delete Cart Item
      tags:
      - Cart Item Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/items:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getItems
      parameters:
      - name: available
        in: query
        schema:
          type:
          - boolean
          - "null"
      - name: tag
        in: query
        schema:
          type:
          - string
          - "null"
      responses:
        "200":
          description: OK
      summary: Get Items
      tags:
      - Item Endpoint
  /shops/{creator_account_identity}/items/{item_id}:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getItem
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ItemResponseBody"
        "500":
          description: Internal Server Error
      summary: Get Item
      tags:
      - Item Endpoint
  /shops/{creator_account_identity}/items/{item_id}/password-unlock:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getItemPasswordUnlockCache
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
      summary: Get Item Password Unlock Cache
      tags:
      - Item Password Unlock Cache Endpoint
      security:
      - SecurityScheme:
        - LoginUser
    post:
      operationId: createItemPasswordUnlockCache
      parameters:
      - name: item_id
        in: path
        required: true
        schema:
          type: integer
          format: int64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateItemPasswordUnlockCacheRequest"
        required: true
      responses:
        "200":
          description: OK
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Item Password Unlock Cache
      tags:
      - Item Password Unlock Cache Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /shops/{creator_account_identity}/seller-apps/digital-fan-letter-link:
    parameters:
    - name: creator_account_identity
      in: path
      required: true
      schema:
        type: string
    get:
      operationId: getDigitalFanLetterLink
      responses:
        "200":
          description: OK
      summary: Get Digital Fan Letter Link
      tags:
      - Seller Apps Endpoint
  /single-order:
    post:
      operationId: createOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateSingleOrderRequest"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SingleOrderResponseBody"
        "500":
          description: Internal Server Error
        "401":
          description: Not Authorized
        "403":
          description: Not Allowed
        "400":
          description: Bad Request
      summary: Create Order
      tags:
      - Single Order Endpoint
      security:
      - SecurityScheme:
        - LoginUser
  /single-order/finalize-credit-card-3d-secure:
    post:
      operationId: finalizeCreditCard3DSecureSingleOrder
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/FinalizeCreditCard3DSecureRequest"
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Finalize Credit Card 3 D Secure Single Order
      tags:
      - Single Order Endpoint
  /webhook/gmo/payment/notice:
    post:
      operationId: gmoWebhook
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              type: object
              required:
              - ShopID
              - ShopPass
              - AccessID
              - AccessPass
              - OrderID
              - Status
              - Amount
              - Tax
              - PayType
              properties:
                ShopID:
                  type: string
                ShopPass:
                  type: string
                AccessID:
                  type: string
                AccessPass:
                  type: string
                OrderID:
                  type: string
                Status:
                  type: string
                Amount:
                  type: string
                Tax:
                  type: string
                PayType:
                  type: string
        required: true
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
      summary: Gmo Webhook
      tags:
      - Webhook Endpoints
tags:
- name: CONSOLE
  description: CONSOLE APIサーバー
- name: FANME
  description: FANME APIサーバー
components:
  schemas:
    AgenciesResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/AgencyData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    Agency:
      type: object
      required:
      - created_at
      - updated_at
      - name
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        name:
          type: string
        deleted_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    AgencyData:
      type: object
      required:
      - agencies
      properties:
        agencies:
          type: array
          items:
            $ref: "#/components/schemas/Agency"
    AgencySales:
      type: object
      required:
      - agencySales
      properties:
        agencySales:
          type: array
          items:
            $ref: "#/components/schemas/CreatorSales"
    AgencySalesResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/AgencySales"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ApplePayParam:
      type: object
      required:
      - token
      properties:
        token:
          type: string
    AssetType:
      type: string
      enum:
      - IMAGE
      - VOICE
      - MOVIE
      - ANY
    AuditGroup:
      type: object
      required:
      - created_at
      - updated_at
      - auditObjects
      - userUid
      - auditType
      - operationType
      - status
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        auditObjects:
          type: array
          items:
            $ref: "#/components/schemas/AuditObject"
        userUid:
          type: string
          maxLength: 50
        user:
          anyOf:
          - $ref: "#/components/schemas/User"
          - type: "null"
        auditType:
          $ref: "#/components/schemas/AuditType"
        operationType:
          $ref: "#/components/schemas/OperationType"
        metadata:
          type:
          - string
          - "null"
        status:
          $ref: "#/components/schemas/AuditStatus"
        comment:
          type:
          - string
          - "null"
        auditedAt:
          anyOf:
          - $ref: "#/components/schemas/LocalDateTime"
          - type: "null"
        auditedUserUid:
          type:
          - string
          - "null"
          maxLength: 50
        metadata_object:
          anyOf:
          - $ref: "#/components/schemas/AuditGroupMetadata"
          - type: "null"
    AuditGroupMetadata:
      type: object
      properties:
        shop_id:
          type:
          - integer
          - "null"
          format: int64
          description: ショップID
          example: 123
        item_id:
          type:
          - string
          - "null"
          description: 商品ID
          example: "456"
        title:
          type:
          - string
          - "null"
          description: タイトル
          example: 商品名
        description:
          type:
          - string
          - "null"
          description: 説明
          example: 商品の説明文
    AuditGroupsData:
      type: object
      required:
      - auditGroups
      properties:
        auditGroups:
          type: array
          items:
            $ref: "#/components/schemas/AuditGroup"
    AuditGroupsResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/AuditGroupsData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    AuditObject:
      type: object
      required:
      - created_at
      - updated_at
      - auditGroupId
      - bucket
      - filePath
      - assetType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        auditGroup:
          anyOf:
          - $ref: "#/components/schemas/AuditGroup"
          - type: "null"
        auditGroupId:
          type: integer
          format: int64
        bucket:
          type: string
          maxLength: 50
        filePath:
          type: string
          maxLength: 255
        assetType:
          $ref: "#/components/schemas/AssetType"
    AuditStatus:
      type: string
      enum:
      - UNAUDITED
      - REJECTED
      - PENDING
      - RESEND
      - APPROVED
    AuditStatusData:
      type: object
      properties:
        result:
          type: boolean
    AuditStatusResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/AuditStatusData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    AuditType:
      type: string
      enum:
      - SHOP
      - SHOP_ITEM
      - FANME_PROFILE
      - FANME_CONTENT
    AwardProbability:
      type: object
      properties:
        award_type:
          type: integer
          format: int32
        probability:
          type: integer
          format: int32
    AwardProbability1:
      type: object
      properties:
        awardType:
          type: integer
          format: int32
        probability:
          type: integer
          format: int32
    BadgeRankingData:
      type: object
      required:
      - ranking
      properties:
        ranking:
          type: array
          items:
            $ref: "#/components/schemas/GetDigitalGachaCompleteBadgeRankingResponse"
    BadgeRankingResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/BadgeRankingData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    BaseResponseBody:
      type: object
      properties:
        data: {}
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    Benefit:
      type: object
      required:
      - files
      properties:
        id:
          type: integer
          format: int64
        description:
          type:
          - string
          - "null"
        conditionType:
          type: integer
          format: int32
        files:
          type: array
          items:
            $ref: "#/components/schemas/BenefitFile1"
    BenefitFile:
      type: object
      required:
      - name
      - file_type
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        object_uri:
          type:
          - string
          - "null"
        thumbnail_uri:
          type:
          - string
          - "null"
        file_type:
          type: string
        size:
          type: number
          format: float
        duration:
          type:
          - integer
          - "null"
          format: int32
        item_thumbnail_selected:
          type:
          - boolean
          - "null"
        sort_order:
          type:
          - integer
          - "null"
          format: int32
    BenefitFile1:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type:
          - integer
          - "null"
          format: int32
        conditionType:
          type: integer
          format: int32
    BenefitParam:
      type: object
      properties:
        id:
          type: integer
          format: int64
        description:
          type:
          - string
          - "null"
        condition_type:
          type: integer
          format: int32
        files:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/BenefitFile"
    CardParam:
      type: object
      properties:
        cardSequence:
          type: integer
          format: int32
    CartItem:
      type: object
      required:
      - name
      - thumbnail_uri
      properties:
        cart_item_id:
          type: integer
          format: int64
        cart_id:
          type: integer
          format: int64
        item_type:
          type: integer
          format: int32
        item_id:
          type: integer
          format: int64
        file_id:
          type:
          - integer
          - "null"
          format: int64
        quantity:
          type: integer
          format: int32
        name:
          type: string
        thumbnail_uri:
          type: string
        price:
          type: integer
          format: int32
        margin_rate:
          type: number
          format: float
        current_price:
          type: integer
          format: int32
        discount_rate:
          type: number
          format: float
        file_type:
          type:
          - string
          - "null"
        file_quantities:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/FileQuantity"
        for_sale:
          type: boolean
        sold_out:
          type: boolean
        purchasable_quantity:
          type:
          - integer
          - "null"
          format: int32
        purchaser_comment:
          type:
          - string
          - "null"
    CartItemData:
      type: object
      required:
      - cart_items
      properties:
        cart_items:
          type: array
          items:
            $ref: "#/components/schemas/CartItem"
        delivery_fee:
          type:
          - integer
          - "null"
          format: int32
        is_locked:
          type: boolean
    CartItemsResponseBody:
      type: object
      required:
      - cart
      properties:
        cart:
          $ref: "#/components/schemas/CartItemData"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    CheckCartItemPriceRequest:
      type: object
      required:
      - itemPrices
      properties:
        itemPrices:
          type: array
          items:
            $ref: "#/components/schemas/ItemPriceSet"
    CheckoutStatus:
      type: string
      enum:
      - UNPROCESSED
      - REQSUCCESS
      - PAYSUCCESS
      - EXPIRED
      - CANCEL
      - PAYFAILED
    CompleteBadgeData:
      type: object
      required:
      - badge
      properties:
        badge:
          $ref: "#/components/schemas/Output1"
    CompleteBadgeResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/CompleteBadgeData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ConsoleUser:
      type: object
      required:
      - created_at
      - updated_at
      - user
      - role
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        user:
          $ref: "#/components/schemas/User"
        agency_id:
          type:
          - integer
          - "null"
          format: int64
        agency:
          anyOf:
          - $ref: "#/components/schemas/Agency"
          - type: "null"
        role:
          type: string
        deleted_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    ConsoleUserData:
      type: object
      required:
      - user
      properties:
        user:
          $ref: "#/components/schemas/ConsoleUserDetail"
    ConsoleUserDetail:
      type: object
      required:
      - name
      - accountIdentity
      properties:
        id:
          type: integer
          format: int64
        uid:
          type:
          - string
          - "null"
        name:
          type: string
        accountIdentity:
          type: string
        isPublic:
          type:
          - boolean
          - "null"
        birthday:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        birthdayConfirmed:
          type:
          - boolean
          - "null"
        isBirthdayWeek:
          type:
          - integer
          - "null"
          format: int32
        gender:
          type:
          - string
          - "null"
        icon:
          type:
          - string
          - "null"
        filledProfile:
          type:
          - boolean
          - "null"
        allowPublicSharing:
          type:
          - boolean
          - "null"
        purpose:
          type:
          - integer
          - "null"
          format: int32
        role:
          type:
          - string
          - "null"
        agencyId:
          type:
          - integer
          - "null"
          format: int64
        livecommerceSalesAmount:
          type:
          - integer
          - "null"
          format: int64
        totalGiftAmount:
          type:
          - integer
          - "null"
          format: int64
        totalTipAmount:
          type:
          - integer
          - "null"
          format: int64
    ConsoleUserResponseBody:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ConsoleUserData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ConsoleUsersData:
      type: object
      required:
      - consoleUsers
      properties:
        consoleUsers:
          type: array
          items:
            $ref: "#/components/schemas/ConsoleUser"
    ConsoleUsersResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ConsoleUsersData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ConvenienceParam:
      type: object
      required:
      - convenience
      - customerName
      - customerKana
      - telNo
      properties:
        convenience:
          type: string
        customerName:
          type: string
        customerKana:
          type: string
        telNo:
          type: string
    CreateCartItemRequest:
      type: object
      properties:
        itemId:
          type: integer
          format: int64
        singleFile:
          type:
          - integer
          - "null"
          format: int64
        quantity:
          type: integer
          format: int32
    CreateContentWithDetailRequest:
      type: object
      required:
      - title
      - url
      properties:
        contentBlockType:
          type: integer
          format: int64
        title:
          type: string
        description:
          type:
          - string
          - "null"
        appDescription:
          type:
          - string
          - "null"
        url:
          type: string
        iconUrl:
          type:
          - string
          - "null"
    CreateDigitalGachaItemRequest:
      type: object
      required:
      - name
      - thumbnailUri
      - itemFiles
      - itemOption
      - awardProbabilities
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnailUri:
          type: string
        thumbnailFrom:
          type: integer
          format: int32
        thumbnailBlurLevel:
          type: integer
          format: int32
        thumbnailWatermarkLevel:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        available:
          type: boolean
        itemFiles:
          type: array
          items:
            $ref: "#/components/schemas/DigitalGachaFile"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/DigitalGachaSampleFile"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/Benefit"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        itemOption:
          $ref: "#/components/schemas/ItemOption1"
        isDuplicated:
          type: boolean
        awardProbabilities:
          type: array
          items:
            $ref: "#/components/schemas/AwardProbability1"
    CreateItemPasswordUnlockCacheRequest:
      type: object
      required:
      - userInputPassword
      properties:
        userInputPassword:
          type: string
    CreateOrUpdateItemRequest:
      type: object
      required:
      - name
      - thumbnailUri
      - itemFiles
      - itemOption
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnailUri:
          type: string
        thumbnailFrom:
          type: integer
          format: int32
        thumbnailBlurLevel:
          type: integer
          format: int32
        thumbnailWatermarkLevel:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        available:
          type: boolean
        itemFiles:
          type: array
          items:
            $ref: "#/components/schemas/File1"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/SampleFile"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/Benefit"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        itemOption:
          $ref: "#/components/schemas/ItemOption1"
        itemType:
          type:
          - integer
          - "null"
          format: int32
    CreateOrderRequest:
      type: object
      required:
      - cartItemIds
      - paymentMethod
      properties:
        cartId:
          type: integer
          format: int64
        cartItemIds:
          type: array
          items:
            type: integer
            format: int64
        tip:
          type: integer
          format: int32
        paymentMethod:
          type: string
        cardParam:
          anyOf:
          - $ref: "#/components/schemas/CardParam"
          - type: "null"
        convenienceParam:
          anyOf:
          - $ref: "#/components/schemas/ConvenienceParam"
          - type: "null"
        googlePayParam:
          anyOf:
          - $ref: "#/components/schemas/GooglePayParam"
          - type: "null"
        applePayParam:
          anyOf:
          - $ref: "#/components/schemas/ApplePayParam"
          - type: "null"
    CreateShopRequest:
      type: object
      required:
      - name
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        headerImageUri:
          type:
          - string
          - "null"
        message:
          type:
          - string
          - "null"
    CreateSingleOrderRequest:
      type: object
      required:
      - paymentMethod
      properties:
        itemId:
          type: integer
          format: int64
        quantity:
          type: integer
          format: int32
        tip:
          type: integer
          format: int32
        paymentMethod:
          type: string
        cardParam:
          anyOf:
          - $ref: "#/components/schemas/CardParam"
          - type: "null"
        convenienceParam:
          anyOf:
          - $ref: "#/components/schemas/ConvenienceParam"
          - type: "null"
        googlePayParam:
          anyOf:
          - $ref: "#/components/schemas/GooglePayParam"
          - type: "null"
        applePayParam:
          anyOf:
          - $ref: "#/components/schemas/ApplePayParam"
          - type: "null"
    CreatorSales:
      type: object
      required:
      - creatorUid
      - creatorName
      - monthlySalesList
      properties:
        creatorUid:
          type: string
        creatorName:
          type: string
        accumulatedSales:
          type: integer
          format: int32
        withdrawableAmount:
          type: integer
          format: int32
        monthlySalesList:
          type: array
          items:
            $ref: "#/components/schemas/MonthlySales"
    DigitalGachaBenefitFileForUpdate:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type:
          - integer
          - "null"
          format: int32
    DigitalGachaBenefitForUpdate:
      type: object
      required:
      - files
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        description:
          type:
          - string
          - "null"
        files:
          type: array
          items:
            $ref: "#/components/schemas/DigitalGachaBenefitFileForUpdate"
    DigitalGachaFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        maskedThumbnailUri:
          type:
          - string
          - "null"
        price:
          type:
          - integer
          - "null"
          format: int32
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type:
          - integer
          - "null"
          format: int32
        awardType:
          type: integer
          format: int32
        isSecret:
          type:
          - boolean
          - "null"
    DigitalGachaFileForUpdate:
      type: object
      required:
      - name
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        isSecret:
          type: boolean
    DigitalGachaPullData:
      type: object
      required:
      - files
      properties:
        files:
          type: array
          items:
            $ref: "#/components/schemas/FileForPullDigitalGachaItems"
    DigitalGachaPullResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/DigitalGachaPullData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    DigitalGachaPullableCountData:
      type: object
      required:
      - item
      properties:
        item:
          $ref: "#/components/schemas/Output"
    DigitalGachaPullableCountResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/DigitalGachaPullableCountData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    DigitalGachaSampleFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
    DigitalGachaSampleFileForUpdate:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
    DownloadUrl:
      type: object
      required:
      - key
      - url
      properties:
        key:
          type: string
        url:
          type: string
    DownloadUrlData:
      type: object
      required:
      - downloadUrls
      properties:
        downloadUrls:
          type: array
          items:
            $ref: "#/components/schemas/DownloadUrl"
    DownloadUrlResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/DownloadUrlData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ErrorObject:
      type: object
      required:
      - code
      - message
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
    EventData:
      type: object
      required:
      - event
      properties:
        event:
          $ref: "#/components/schemas/RankingEventWithBoost"
    FanmeCustomerData:
      type: object
      required:
      - fanmeCustomer
      properties:
        fanmeCustomer:
          $ref: "#/components/schemas/FanmeCustomerEntity"
    FanmeCustomerEntity:
      type: object
      required:
      - first_name
      - last_name
      - first_name_kana
      - last_name_kana
      - postal_code
      - prefecture
      - city
      - street
      - phone_number
      properties:
        creator_uid:
          type:
          - string
          - "null"
        first_name:
          type: string
        last_name:
          type: string
        first_name_kana:
          type: string
        last_name_kana:
          type: string
        postal_code:
          type: string
        prefecture:
          type: string
        city:
          type: string
        street:
          type: string
        building:
          type:
          - string
          - "null"
        phone_number:
          type: string
    FanmeCustomerResponseBody:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/FanmeCustomerData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    File:
      type: object
      required:
      - name
      - file_type
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        name:
          type: string
        object_uri:
          type:
          - string
          - "null"
        thumbnail_uri:
          type:
          - string
          - "null"
        masked_thumbnail_uri:
          type:
          - string
          - "null"
        price:
          type:
          - integer
          - "null"
          format: int32
        current_price:
          type:
          - integer
          - "null"
          format: int32
        file_type:
          type: string
        size:
          type: number
          format: float
        duration:
          type:
          - integer
          - "null"
          format: int32
        is_purchased:
          type: boolean
        is_checkout:
          type: boolean
        item_thumbnail_selected:
          type: boolean
        award_type:
          type:
          - integer
          - "null"
          format: int32
        is_secret:
          type: boolean
        condition_type:
          type: integer
          format: int32
        received_file_count:
          type:
          - integer
          - "null"
          format: int32
    File1:
      type: object
      required:
      - name
      - objectUri
      - thumbnailUri
      - fileType
      properties:
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type: string
        price:
          type:
          - integer
          - "null"
          format: int32
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
        itemThumbnailSelected:
          type:
          - boolean
          - "null"
        sortOrder:
          type: integer
          format: int32
    FileForPullDigitalGachaItems:
      type: object
      required:
      - name
      - file_type
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        object_uri:
          type:
          - string
          - "null"
        thumbnail_uri:
          type:
          - string
          - "null"
        file_type:
          type: string
        size:
          type: number
          format: float
        duration:
          type:
          - integer
          - "null"
          format: int32
        award_type:
          type: integer
          format: int32
        is_secret:
          type:
          - boolean
          - "null"
    FileQuantity:
      type: object
      required:
      - file_type
      properties:
        file_type:
          type: string
        quantity:
          type: integer
          format: int32
    FinalizeCreditCard3DSecureRequest:
      type: object
      properties:
        transactionId:
          type: integer
          format: int64
        checkoutId:
          type: integer
          format: int64
    ForSale:
      type: object
      properties:
        startAt:
          type:
          - string
          - "null"
        endAt:
          type:
          - string
          - "null"
    ForSaleData:
      type: object
      properties:
        start_at:
          type:
          - string
          - "null"
        end_at:
          type:
          - string
          - "null"
    GetDigitalGachaCompleteBadgeRankingResponse:
      type: object
      required:
      - userUid
      - userAccountIdentity
      - userName
      - userIcon
      - getBadgeAt
      properties:
        userUid:
          type: string
        userAccountIdentity:
          type: string
        userName:
          type: string
        userIcon:
          type: string
        getBadgeAt:
          type: string
    GetDownloadUrlRequest:
      type: object
      required:
      - metadataList
      properties:
        metadataList:
          type: array
          items:
            $ref: "#/components/schemas/MetadataForGetDownloadUrlRequest"
        itemId:
          type: integer
          format: int64
    GetPreSignedUrlRequest:
      type: object
      required:
      - metadataList
      - creatorAccountIdentity
      properties:
        metadataList:
          type: array
          items:
            $ref: "#/components/schemas/MetadataForGetPreSignedUrlRequest"
        creatorAccountIdentity:
          type: string
    GetUploadUrlRequest:
      type: object
      required:
      - metadataList
      properties:
        metadataList:
          type: array
          items:
            $ref: "#/components/schemas/MetadataForGetUploadUrlRequest"
    GooglePayParam:
      type: object
      required:
      - token
      properties:
        token:
          type: string
    Instant:
      type: string
      format: date-time
      examples:
      - 2022-03-10T16:15:50Z
    Item:
      type: object
      required:
      - creator_account_identity
      - name
      - thumbnail_uri
      - file_type
      - item_option
      properties:
        id:
          type: integer
          format: int64
        creator_account_identity:
          type: string
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnail_uri:
          type: string
        thumbnail_from:
          type: integer
          format: int32
        thumbnail_blur_level:
          type: integer
          format: int32
        thumbnail_watermark_level:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        current_price:
          type: integer
          format: int32
        file_type:
          type: string
        available:
          type: boolean
        award_probabilities:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/AwardProbability"
        is_duplicated_digital_gacha_items:
          type:
          - boolean
          - "null"
        item_type:
          type: integer
          format: int32
        files:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/File"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/File"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/BenefitParam"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        item_option:
          $ref: "#/components/schemas/OptionData"
        is_purchased:
          type: boolean
        is_checkout:
          type: boolean
        purchased_count:
          type: integer
          format: int32
        collected_unique_items_count:
          type: integer
          format: int32
        is_completed:
          type: boolean
        remaining_unique_pull_count:
          type:
          - integer
          - "null"
          format: int32
    ItemData:
      type: object
      required:
      - item
      properties:
        item:
          $ref: "#/components/schemas/Item"
    ItemOption:
      type: object
      properties:
        password:
          type:
          - string
          - "null"
        onSale:
          anyOf:
          - $ref: "#/components/schemas/OnSale"
          - type: "null"
        forSale:
          anyOf:
          - $ref: "#/components/schemas/ForSale"
          - type: "null"
    ItemOption1:
      type: object
      properties:
        isSingleSales:
          type: boolean
        qtyTotal:
          type:
          - integer
          - "null"
          format: int32
        qtyPerUser:
          type:
          - integer
          - "null"
          format: int32
        forSale:
          anyOf:
          - $ref: "#/components/schemas/ForSale"
          - type: "null"
        password:
          type:
          - string
          - "null"
        onSale:
          anyOf:
          - $ref: "#/components/schemas/OnSale"
          - type: "null"
    ItemPriceSet:
      type: object
      properties:
        cartItemId:
          type: integer
          format: int64
        displayedPrice:
          type: integer
          format: int32
    ItemResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ItemData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    ItemType:
      type: string
      enum:
      - DIGITAL_BUNDLE
      - DIGITAL_GACHA
      - CHEKI
    LocalDateTime:
      type: string
      format: date-time
      examples:
      - 2022-03-10T12:15:50
    MetadataForGetDownloadUrlRequest:
      type: object
      required:
      - key
      properties:
        key:
          type: string
        name:
          type:
          - string
          - "null"
    MetadataForGetPreSignedUrlRequest:
      type: object
      required:
      - id
      - key
      properties:
        id:
          type: string
        key:
          type: string
    MetadataForGetUploadUrlRequest:
      type: object
      required:
      - id
      properties:
        id:
          type: string
        name:
          type:
          - string
          - "null"
    MonthlySales:
      type: object
      required:
      - yearMonth
      properties:
        yearMonth:
          type: string
        sellerSalesAmount:
          type: integer
          format: int32
        merged:
          type: boolean
        expirationDate:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    OnSale:
      type: object
      properties:
        discountRate:
          type: number
          format: float
        startAt:
          type:
          - string
          - "null"
        endAt:
          type:
          - string
          - "null"
    OnSaleData:
      type: object
      properties:
        discount_rate:
          type: number
          format: float
        start_at:
          type:
          - string
          - "null"
        end_at:
          type:
          - string
          - "null"
    OperationType:
      type: string
      enum:
      - INSERT
      - UPDATE
    OptionData:
      type: object
      properties:
        is_single_sales:
          type: boolean
        qty_total:
          type:
          - integer
          - "null"
          format: int32
        qty_per_user:
          type:
          - integer
          - "null"
          format: int32
        remaining_amount:
          type:
          - integer
          - "null"
          format: int32
        for_sale:
          anyOf:
          - $ref: "#/components/schemas/ForSaleData"
          - type: "null"
        password:
          type:
          - string
          - "null"
        on_sale:
          anyOf:
          - $ref: "#/components/schemas/OnSaleData"
          - type: "null"
    Order:
      type: object
      required:
      - created_at
      - updated_at
      - purchaserUid
      - shop
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        purchaserUid:
          type: string
          pattern: \S
        shop:
          $ref: "#/components/schemas/Shop1"
        transactionId:
          type:
          - integer
          - "null"
          format: int64
        checkoutId:
          type:
          - integer
          - "null"
          format: int64
    OrderResult:
      type: object
      required:
      - order
      properties:
        order:
          $ref: "#/components/schemas/Order"
        purchased_items:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/PurchasedItem"
        convenience_checkout:
          anyOf:
          - $ref: "#/components/schemas/Output2"
          - type: "null"
        redirect_url:
          type:
          - string
          - "null"
    OrderedItem:
      type: object
      required:
      - name
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        itemType:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        marginRate:
          type: number
          format: float
        quantity:
          type: integer
          format: int32
    Output:
      type: object
      properties:
        item_id:
          type: integer
          format: int64
        remaining_pull_count:
          type: integer
          format: int32
    Output1:
      type: object
      properties:
        item_id:
          type: integer
          format: int64
        is_acquired:
          type: boolean
        rank:
          type:
          - integer
          - "null"
          format: int32
    Output2:
      type: object
      required:
      - convenience
      - confNo
      - receiptNo
      - paymentTerm
      - status
      properties:
        checkoutId:
          type: integer
          format: int64
        convenience:
          type: string
        confNo:
          type: string
        receiptNo:
          type: string
        paymentTerm:
          type: string
        receiptUrl:
          type:
          - string
          - "null"
        status:
          $ref: "#/components/schemas/CheckoutStatus"
    PullDigitalGachaRequest:
      type: object
      properties:
        itemId:
          type: integer
          format: int64
    PurchaseItemData:
      type: object
      required:
      - purchasedItem
      properties:
        purchasedItem:
          $ref: "#/components/schemas/PurchasedItemDetail"
    PurchaseItemResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/PurchaseItemData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    PurchasedItem:
      type: object
      required:
      - created_at
      - updated_at
      - order
      - purchaserUid
      - item
      - status
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        order:
          $ref: "#/components/schemas/Order"
        purchaserUid:
          type: string
          pattern: \S
        item:
          type: object
          required:
          - created_at
          - updated_at
          - name
          - thumbnailUri
          - thumbnailFrom
          - thumbnailBlurLevel
          - thumbnailWatermarkLevel
          - price
          - fileType
          - available
          - marginRate
          - sortOrder
          - itemType
          properties:
            id:
              type:
              - integer
              - "null"
              format: int64
            created_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            updated_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            name:
              type: string
              minLength: 1
              maxLength: 100
              pattern: \S
            description:
              type:
              - string
              - "null"
              maxLength: 800
            thumbnailUri:
              type: string
              pattern: \S
            thumbnailFrom:
              type: integer
              format: int32
              maximum: 1
              minimum: 0
            thumbnailBlurLevel:
              type: integer
              format: int32
              maximum: 2
              minimum: 0
            thumbnailWatermarkLevel:
              type: integer
              format: int32
              maximum: 2
              minimum: 0
            price:
              type: integer
              format: int32
              maximum: 1000000
              minimum: 100
            fileType:
              type: integer
              format: int32
            available:
              type: boolean
            marginRate:
              type: number
              format: float
            sortOrder:
              type: integer
              format: int32
            itemType:
              $ref: "#/components/schemas/ItemType"
            digital:
              type: boolean
        itemFile:
          type:
          - object
          - "null"
          required:
          - created_at
          - updated_at
          - name
          - objectUri
          - fileType
          - itemThumbnailSelected
          - sortOrder
          properties:
            id:
              type:
              - integer
              - "null"
              format: int64
            created_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            updated_at:
              anyOf:
              - $ref: "#/components/schemas/Instant"
              - type: "null"
            name:
              type: string
              maxLength: 30
              pattern: \S
            objectUri:
              type:
              - string
              - "null"
              pattern: \S
            thumbnailUri:
              type:
              - string
              - "null"
            maskedThumbnailUri:
              type:
              - string
              - "null"
            price:
              type:
              - integer
              - "null"
              format: int32
              maximum: 1000000
              minimum: 0
            fileType:
              type: string
              pattern: \S
            size:
              type: number
              format: float
            duration:
              type: integer
              format: int32
              minimum: 0
            itemThumbnailSelected:
              type: boolean
            sortOrder:
              type: integer
              format: int32
        price:
          type: integer
          format: int32
          maximum: 1000000
          minimum: 100
        quantity:
          type: integer
          format: int32
          minimum: 1
        purchaserComment:
          type:
          - string
          - "null"
        status:
          type: string
          pattern: \S
        purchasedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    PurchasedItemCheckout:
      type: object
      properties:
        payment_type:
          type:
          - string
          - "null"
        convenience:
          type:
          - string
          - "null"
        conf_no:
          type:
          - string
          - "null"
        receipt_no:
          type:
          - string
          - "null"
        payment_term:
          type:
          - string
          - "null"
        receipt_url:
          type:
          - string
          - "null"
        status:
          type:
          - string
          - "null"
        total:
          type:
          - integer
          - "null"
          format: int32
        tip_amount:
          type:
          - integer
          - "null"
          format: int32
        cvs_fee:
          type:
          - integer
          - "null"
          format: int32
        delivery_fee:
          type:
          - integer
          - "null"
          format: int32
    PurchasedItemDetail:
      type: object
      required:
      - purchased_at
      - order
      properties:
        id:
          type: integer
          format: int64
        item_id:
          type: integer
          format: int64
        purchased_at:
          type: string
        order:
          $ref: "#/components/schemas/PurchasedItemOrder"
        checkout:
          anyOf:
          - $ref: "#/components/schemas/PurchasedItemCheckout"
          - type: "null"
        purchaser_comment:
          type:
          - string
          - "null"
    PurchasedItemOrder:
      type: object
      required:
      - order_number
      - items
      - ordered_at
      properties:
        id:
          type: integer
          format: int64
        order_number:
          type: string
        items:
          type: array
          items:
            $ref: "#/components/schemas/OrderedItem"
        ordered_at:
          type: string
    RankingEvent:
      type: object
      required:
      - created_at
      - updated_at
      - eventIdentity
      - name
      - description
      - imageUrl
      - applyStartAt
      - applyEndAt
      - startAt
      - endAt
      - calculatedAt
      - archivedAt
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        eventIdentity:
          type:
          - string
          - "null"
          maxLength: 255
        name:
          type:
          - string
          - "null"
          maxLength: 255
        description:
          type:
          - string
          - "null"
        imageUrl:
          type:
          - string
          - "null"
          maxLength: 255
        baseColor:
          type:
          - string
          - "null"
          maxLength: 255
        addInfos:
          type:
          - string
          - "null"
          maxLength: 255
        judgeX:
          type:
          - string
          - "null"
          maxLength: 255
        judgeInstagram:
          type:
          - string
          - "null"
          maxLength: 255
        shareHashTags:
          type:
          - string
          - "null"
          maxLength: 255
        results:
          type:
          - string
          - "null"
          maxLength: 255
        applyStartAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        applyEndAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        startAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        endAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        calculatedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        archivedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
    RankingEventInfoResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/EventData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    RankingEventWithBoost:
      type: object
      properties:
        rankingEvent:
          anyOf:
          - $ref: "#/components/schemas/RankingEvent"
          - type: "null"
        boost:
          anyOf:
          - $ref: "#/components/schemas/RankingYellBoost"
          - type: "null"
    RankingYellBoost:
      type: object
      required:
      - created_at
      - updated_at
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        startAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        endAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        boostRatio:
          type:
          - number
          - "null"
    RegisterCardRequest:
      type: object
      required:
      - cardName
      - token
      properties:
        cardName:
          type: string
        token:
          type: string
    SampleFile:
      type: object
      required:
      - name
      - objectUri
      - fileType
      properties:
        name:
          type: string
        objectUri:
          type: string
        thumbnailUri:
          type:
          - string
          - "null"
        fileType:
          type: string
        size:
          type: number
          format: float
        duration:
          type: integer
          format: int32
    SaveFanmeCustomerRequest:
      type: object
      required:
      - firstName
      - lastName
      - firstNameKana
      - lastNameKana
      - postalCode
      - prefecture
      - city
      - street
      - phoneNumber
      properties:
        firstName:
          type: string
        lastName:
          type: string
        firstNameKana:
          type: string
        lastNameKana:
          type: string
        postalCode:
          type: string
        prefecture:
          type: string
        city:
          type: string
        street:
          type: string
        building:
          type:
          - string
          - "null"
        phoneNumber:
          type: string
    SendEmailRequest:
      type: object
      properties:
        transactionId:
          type: integer
          format: int64
    Shop:
      type: object
      required:
      - tenant
      - creator_name
      - creator_icon_uri
      - creator_uid
      - creator_account_identity
      - name
      - limitation
      properties:
        id:
          type: integer
          format: int64
        tenant:
          type: string
        creator_name:
          type: string
        creator_icon_uri:
          type: string
        creator_uid:
          type: string
        creator_account_identity:
          type: string
        name:
          type: string
        description:
          type:
          - string
          - "null"
        header_image_uri:
          type:
          - string
          - "null"
        message:
          type:
          - string
          - "null"
        margin_rate:
          type: number
          format: float
        is_open:
          type: boolean
        limitation:
          $ref: "#/components/schemas/ShopLimitation"
        open:
          type: boolean
    Shop1:
      type: object
      required:
      - created_at
      - updated_at
      - name
      - tenant
      - creatorUid
      - message
      - marginRate
      - tipMarginRate
      - isOpen
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        name:
          type: string
          maxLength: 50
        tenant:
          type: string
        creatorUid:
          type:
          - string
          - "null"
          maxLength: 50
        description:
          type:
          - string
          - "null"
          maxLength: 500
        headerImageUri:
          type:
          - string
          - "null"
        message:
          type: string
          maxLength: 100
          pattern: \S
        marginRate:
          type: number
          format: float
          minimum: 0
        tipMarginRate:
          type: number
          format: float
          minimum: 0
        isOpen:
          type: boolean
        open:
          type: boolean
    ShopData:
      type: object
      required:
      - shop
      properties:
        shop:
          $ref: "#/components/schemas/Shop"
    ShopLimitation:
      type: object
      required:
      - created_at
      - updated_at
      - file_capacity
      - file_quantity
      - is_cheki_exhibitable
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        file_capacity:
          type: integer
          format: int32
        file_quantity:
          type: integer
          format: int32
        is_cheki_exhibitable:
          type: boolean
        cheki_exhibitable:
          type: boolean
    ShopResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/ShopData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    SingleOrderData:
      type: object
      required:
      - order
      properties:
        order:
          $ref: "#/components/schemas/OrderResult"
    SingleOrderResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/SingleOrderData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    SortItem:
      type: object
      properties:
        id:
          type: integer
          format: int64
        sortOrder:
          type: integer
          format: int32
    SortItemsRequest:
      type: object
      required:
      - items
      properties:
        items:
          type: array
          items:
            $ref: "#/components/schemas/SortItem"
    SuggestAddressData:
      type: object
      required:
      - suggestedAddress
      properties:
        suggestedAddress:
          $ref: "#/components/schemas/SuggestedAddressEntity"
    SuggestAddressResponseBody:
      type: object
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/SuggestAddressData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    SuggestedAddressEntity:
      type: object
      required:
      - prefecture
      - city
      - street
      properties:
        prefecture:
          type: string
        city:
          type: string
        street:
          type: string
    TipLimitData:
      type: object
      required:
      - tip_limit
      properties:
        tip_limit:
          $ref: "#/components/schemas/TipUpperLimit"
    TipLimitResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/TipLimitData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
    TipUpperLimit:
      type: object
      properties:
        amount:
          type: integer
          format: int32
    UpdateCardRequest:
      type: object
      required:
      - cardName
      - cardHolderName
      - expire
      properties:
        cardSequence:
          type: integer
          format: int32
        cardName:
          type: string
        cardHolderName:
          type: string
        expire:
          type: string
    UpdateCartItemRequest:
      type: object
      properties:
        quantity:
          type:
          - integer
          - "null"
          format: int32
        purchaserComment:
          type:
          - string
          - "null"
    UpdateDigitalGachaItemRequest:
      type: object
      required:
      - name
      - thumbnailUri
      - itemFiles
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        thumbnailUri:
          type: string
        thumbnailFrom:
          type: integer
          format: int32
        thumbnailBlurLevel:
          type: integer
          format: int32
        thumbnailWatermarkLevel:
          type: integer
          format: int32
        price:
          type: integer
          format: int32
        available:
          type: boolean
        itemFiles:
          type: array
          items:
            $ref: "#/components/schemas/DigitalGachaFileForUpdate"
        samples:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/DigitalGachaSampleFileForUpdate"
        benefits:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/DigitalGachaBenefitForUpdate"
        tags:
          type:
          - array
          - "null"
          items:
            type: string
        itemOption:
          anyOf:
          - $ref: "#/components/schemas/ItemOption"
          - type: "null"
    UpdateOrderRequest:
      type: object
      required:
      - status
      properties:
        transactionId:
          type:
          - integer
          - "null"
          format: int64
        checkoutId:
          type: integer
          format: int64
        status:
          type: string
    UpdateShopRequest:
      type: object
      required:
      - name
      properties:
        name:
          type: string
        description:
          type:
          - string
          - "null"
        headerImageUri:
          type:
          - string
          - "null"
        message:
          type:
          - string
          - "null"
    UpdateStatusRequest:
      type: object
      properties:
        status:
          type: integer
          format: int32
        comment:
          type:
          - string
          - "null"
        auditedUserUid:
          type:
          - string
          - "null"
    User:
      type: object
      required:
      - created_at
      - updated_at
      - name
      - gender
      - birthday
      - birthdayConfirmed
      - accountIdentity
      - public
      - filledProfile
      - purpose
      - iconUrl
      properties:
        id:
          type:
          - integer
          - "null"
          format: int64
        created_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        updated_at:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        icon:
          type:
          - string
          - "null"
          maxLength: 255
        name:
          type:
          - string
          - "null"
          maxLength: 255
        gender:
          type:
          - string
          - "null"
          maxLength: 255
        birthday:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        birthdayConfirmed:
          type:
          - boolean
          - "null"
        accountIdentity:
          type:
          - string
          - "null"
          maxLength: 255
        public:
          type:
          - boolean
          - "null"
        allowPublicSharing:
          type:
          - boolean
          - "null"
        uid:
          type:
          - string
          - "null"
          maxLength: 255
        deletedAt:
          anyOf:
          - $ref: "#/components/schemas/Instant"
          - type: "null"
        filledProfile:
          type:
          - boolean
          - "null"
        purpose:
          type:
          - integer
          - "null"
          format: int32
        isBirthdayWeek:
          type:
          - integer
          - "null"
          format: int32
        birthdayWeek:
          type: integer
          format: int32
        iconUrl:
          type: string
    UsersData:
      type: object
      required:
      - users
      properties:
        users:
          type: array
          items:
            $ref: "#/components/schemas/User"
    UsersResponseBody:
      type: object
      required:
      - data
      properties:
        data:
          anyOf:
          - $ref: "#/components/schemas/UsersData"
          - type: "null"
        errors:
          type:
          - array
          - "null"
          items:
            $ref: "#/components/schemas/ErrorObject"
  securitySchemes:
    SecurityScheme:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: Authentication
info:
  title: backend API
  version: 1.0.0-SNAPSHOT
