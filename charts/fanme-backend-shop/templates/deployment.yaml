apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "fanme-backend-shop.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ include "fanme-backend-shop.name" . }}
    helm.sh/chart: {{ include "fanme-backend-shop.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    tags.datadoghq.com/env: {{ .Values.env | quote }}
    tags.datadoghq.com/service: {{ .Chart.Name | quote }}
    tags.datadoghq.com/version: {{ index .Values.image "fanme-backend-shop" "tag" | quote }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "fanme-backend-shop.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "fanme-backend-shop.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        tags.datadoghq.com/env: {{ .Values.env | quote }}
        tags.datadoghq.com/service: {{ .Chart.Name | quote }}
        tags.datadoghq.com/version: {{ index .Values.image "fanme-backend-shop" "tag" | quote }}
        admission.datadoghq.com/enabled: "true"
        admission.datadoghq.com/config.mode: "socket"
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
        cluster-autoscaler.kubernetes.io/safe-to-evict: {{ index .Values "auto-scale" | quote }}
        admission.datadoghq.com/java-lib.version: "v1.47.3"
        ad.datadoghq.com/{{ .Chart.Name }}.logs: |
          [
            {
              "source": "quarkus"
            }
          ]
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ index .Values "image" "fanme-backend-shop" "repository" }}:{{ index .Values "image" "fanme-backend-shop" "tag" }}"
          imagePullPolicy: {{ index .Values "image" "fanme-backend-shop" "pullPolicy" }}
          command:
            - /opt/jboss/container/java/run/run-java.sh
          resources:
{{- toYaml (index .Values "resources") | nindent 12 }}
          env:
            - name: QUARKUS_PROFILE
              value: {{ index .Values "fanme-backend-shop" "quarkus_profile" | quote }}
            - name: DD_LOGS_INJECTION
              value: {{ default true .Values.datadog_DD_LOGS_INJECTION | quote }}
            - name: DD_PROFILING_ENABLED
              value: {{ default true .Values.datadog_DD_PROFILING_ENABLED | quote }}
            - name: DD_PROFILING_DIRECTALLOCATION_ENABLED
              value: {{ default true .Values.datadog_DD_PROFILING_DIRECTALLOCATION_ENABLED | quote }}
            - name: DD_GIT_REPOSITORY_URL
              value: "https://github.com/torihada-inc/fanme-shop.git"
            - name: DD_GIT_COMMIT_SHA
              value: {{  index .Values "image" "fanme-backend-shop" "tag" | quote }}
          lifecycle:
            preStop:
              exec:
                command:
                  - sleep
                  - "40"
          livenessProbe:
            httpGet:
              path: {{ .Values.livenessProbe.path }}
              port: {{ .Values.livenessProbe.port }}
            initialDelaySeconds: {{ .Values.livenessProbe.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.livenessProbe.timeoutSeconds }}
            periodSeconds: {{ .Values.livenessProbe.periodSeconds }}
            failureThreshold: {{ .Values.livenessProbe.failureThreshold }}
          readinessProbe:
            httpGet:
              path: {{ .Values.readinessProbe.path }}
              port: {{ .Values.readinessProbe.port }}
            initialDelaySeconds: {{ .Values.readinessProbe.initialDelaySeconds }}
            timeoutSeconds: {{ .Values.readinessProbe.timeoutSeconds }}
            periodSeconds: {{ .Values.readinessProbe.periodSeconds }}
            failureThreshold: {{ .Values.readinessProbe.failureThreshold }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      volumes:
        - name: config
          configMap:
            name: {{ include "fanme-backend-shop.fullname" . }}
