replicaCount: 4

fanme-frontend-shop:
  yarn_profile: prod
  next:
    app_env: production

env: prd

auto-scale: true

ingress:
  enabled: true
  className: alb
  annotations:
    alb.ingress.kubernetes.io/group.name: public-alb
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/certificate-arn: "arn:aws:acm:ap-northeast-1:638984414044:certificate/beb7368c-538a-408d-9d02-da1876ee0046"
    alb.ingress.kubernetes.io/backend-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-port: "3000"
    alb.ingress.kubernetes.io/healthcheck-interval-seconds: "10"
    alb.ingress.kubernetes.io/healthcheck-protocol: HTTP
    alb.ingress.kubernetes.io/healthcheck-path: /hc
    alb.ingress.kubernetes.io/success-codes: "200"
  path: /*
  hosts:
  - fanme.link
  servicePort: 80
  backend_front_shop: fanme-frontend-shop
  backend_front: fanme-front

services:
- name: fanme-frontend-shop-server-svc
  domains:
  - fanme-frontend-shop:80
  forward:
    address: fanme-frontend-shop.svc.cluster.local
    port: 3000
  http2: true
  internalService: true
  tls: true
  healthcheck: true

resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 100m
    memory: 128Mi
