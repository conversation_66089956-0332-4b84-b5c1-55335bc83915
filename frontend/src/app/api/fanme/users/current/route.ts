import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { User } from '@/app/api/types/backend/fanme/User';
import { apiAuth } from '@/app/api/utils/auth';
import { handleApiResponse } from '@/utils/api';

export async function GET(request: NextRequest): Promise<NextResponse<User | null>> {
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json(null);
  }

  const client = createAxiosClient(request);
  return await handleApiResponse(client.get('/fanme/users/current'));
}
