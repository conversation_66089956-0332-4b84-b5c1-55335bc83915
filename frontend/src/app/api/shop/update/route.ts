import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { Shop } from '@/app/api/types/backend/shop/Shop';
import { apiAuth } from '@/app/api/utils/auth';
import { camelToSnakeObject, snakeToCamelObject } from '@/app/api/utils/formatConvert';
import { validateFields } from '@/app/api/utils/validation';
import { createShopRules } from '@/app/api/utils/validationRules/createShop';
import { CreateShopRequest } from '@/types/api';

export async function PUT(request: NextRequest) {
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const body: CreateShopRequest = await request.json();
  const client = createAxiosClient(request);
  const path = `/shops/current`;

  try {
    const validation = validateFields(body, createShopRules);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status || 400 });
    }
    const shopData: Shop = camelToSnakeObject(body);

    const response = await client.put(path, shopData);
    if (response.data.errors.length > 0) {
      return NextResponse.json(response);
    }

    const resShopData = snakeToCamelObject(response.data.data.shop);
    const resData = response.data;
    resData.data.shop = resShopData;
    return NextResponse.json(resData, { status: 200 });
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return new NextResponse(error, { status: 500 });
  }
}
