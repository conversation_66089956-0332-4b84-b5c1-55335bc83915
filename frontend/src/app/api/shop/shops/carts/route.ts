import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { Shop } from '@/app/api/types/backend/shop/Shop';
import { ShopItemBk } from '@/app/api/types/backend/shop/ShopItem';

type ShopResponseData = Pick<Shop, 'id' | 'name' | 'description' | 'headerImageUri'>;

type CartItemsBody = {
  identitiIds: string[];
  userId: string;
};
export async function GET(request: NextRequest) {
  const client = createAxiosClient(request);

  const params: CartItemsBody = await request.json();
  // TODO
  // 今は店舗ごとに展示するため、クリエイターIDを固定
  // 今後カートに複数の店舗を持たせる場合は、クリエイターIDを繰り返してpromise.allで各店舗のかーとを取得する
  const shopPath = `/shops/${params.identitiIds[0]}`;
  const shopItemsPath = `/shops/${params.identitiIds[0]}/items`;

  try {
    const [shopResponse, itemsResponse] = await Promise.all([client.get(shopPath), client.get(shopItemsPath)]);

    const shopData: ShopResponseData = {
      id: shopResponse.data.shop.id,
      name: shopResponse.data.shop.name,
      description: shopResponse.data.shop.description,
      headerImageUri: shopResponse.data.shop.headerImageUrl,
    };
    const itemsData: ShopItemBk[] = itemsResponse.data.items.map((item: ShopItemBk) => ({
      id: item.id,
      name: item.name,
      price: item.price,
      thumbnailUri: item.thumbnail_uri,
      description: item.description,
      fileType: item.file_type,
      available: item.available,
    }));

    return NextResponse.json({
      shop: shopData,
      items: itemsData,
    });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}
