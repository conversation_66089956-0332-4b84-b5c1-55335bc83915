import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { Shop } from '@/app/api/types/backend/shop/Shop';

export async function GET(request: NextRequest, { params }: { params: { identityId: string } }) {
  const client = createAxiosClient(request);
  const shopPath = `/shops/${params.identityId}`;

  try {
    const response = await client.get(shopPath);
    const shop: Shop = {
      id: response.data.data.shop.id,
      name: response.data.data.shop.name,
      description: response.data.data.shop.description,
      headerImageUri: response.data.data.shop.header_image_uri,
      message: response.data.data.shop.message,
      creatorIconUri: response.data.data.shop.creator_icon_uri,
      creatorName: response.data.data.shop.creator_name,
      limitation: {
        fileCapacity: response.data.data.shop.limitation.file_capacity,
        fileQuantity: response.data.data.shop.limitation.file_quantity,
      },
    };

    return NextResponse.json({ data: { shop } });
  } catch (error) {
    if (error instanceof AxiosError) {
      return NextResponse.json({ error: 'Failed to fetch shop data' }, { status: error.response?.status });
    }
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: 'Failed to fetch shop data' }, { status: 500 });
  }
}
