import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { ShopItemBk } from '@/app/api/types/backend/shop/ShopItem';
import { ShopItemType } from '@/types/shopItem';

export async function GET(request: NextRequest, { params }: { params: { identityId: string } }) {
  const client = createAxiosClient(request);
  const itemsPath = `/shops/${params.identityId}/items`;

  try {
    const res = await client.get(itemsPath);

    const items: ShopItemType[] = res.data.data.items.map((item: ShopItemBk) => {
      return {
        id: item.id,
        title: item.name,
        available: item.available,
        // TODO 要修正
        isNew: false,
        price: item.price,
        minPrice: item.min_price || 0,
        currentPrice: item.current_price,
        thumbnail: item.thumbnail_uri,
        description: item.description,
        fileType: item.file_type,
        itemType: item.item_type,
        hasBenefit: item.has_benefit,
        hasPassword: item.item_option.password ? true : false,
        isPurchased: item.is_purchased,
        isCheckout: item.is_checkout,
        onSale: {
          startAt: item.item_option.on_sale ? item.item_option.on_sale.start_at : undefined,
          endAt: item.item_option.on_sale ? item.item_option.on_sale.end_at : undefined,
          discountRate: item.item_option.on_sale ? item.item_option.on_sale.discount_rate : undefined,
        },
        forSale: {
          startAt: item.item_option.for_sale ? item.item_option.for_sale.start_at : undefined,
          endAt: item.item_option.for_sale ? item.item_option.for_sale.end_at : undefined,
        },
        isSingleSales: item.item_option.is_single_sales,
        stockCount: item.item_option.remaining_amount || undefined,
        fileQuantities:
          item.file_quantities?.map((fileQuantity) => ({
            fileType: fileQuantity.file_type,
            quantity: fileQuantity.quantity,
          })) || [],
      };
    });
    return NextResponse.json({ data: { items } });
  } catch (error) {
    if (error instanceof AxiosError) {
      return NextResponse.json({ error: 'Failed to fetch shop data' }, { status: error.response?.status });
    }
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: 'Failed to fetch shop data' }, { status: 500 });
  }
}
