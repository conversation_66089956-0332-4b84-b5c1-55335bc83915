import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { User } from '@/app/api/types/backend/fanme/User';
import { Shop } from '@/app/api/types/backend/shop/Shop';

type ShopResponseData = Pick<Shop, 'id' | 'message' | 'name'>;
type CreatorResponseData = Pick<User, 'id' | 'name' | 'iconUrl'>;

export async function GET(request: NextRequest, { params }: { params: { creatorId: string; identityId: string } }) {
  const client = createAxiosClient(request);
  const shopPath = `/shops/${params.identityId}`;

  try {
    const shopResponse = await client.get(shopPath);
    const creatorUid = shopResponse.data.data.shop.creator_uid;

    const creatorPath = `/fanme/users/${creatorUid}`;
    const creatorResponse = await client.get(creatorPath);

    const shopData: ShopResponseData = {
      id: shopResponse.data.data.shop.id,
      name: shopResponse.data.data.shop.name,
      message: shopResponse.data.data.shop.message,
    };
    const creatorData: CreatorResponseData = {
      id: creatorResponse.data.data.user.id,
      name: creatorResponse.data.data.user.name,
      iconUrl: creatorResponse.data.data.user.icon_url,
    };

    return NextResponse.json({
      data: {
        shop: shopData,
        creator: creatorData,
      },
    });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}
