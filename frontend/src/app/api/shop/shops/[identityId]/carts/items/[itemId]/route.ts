import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';

type DeleteCartItemParams = {
  identityId: string;
  itemId: string;
};

export async function DELETE(request: NextRequest, { params }: { params: DeleteCartItemParams }) {
  const client = createAxiosClient(request);
  const path = `/shops/${params.identityId}/cart-items/${params.itemId}`;

  try {
    await client.delete(path);
    return NextResponse.json({ message: 'Item deleted successfully' }, { status: 200 });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ message: 'Failed to delete item', details: error }, { status: 500 });
  }
}
