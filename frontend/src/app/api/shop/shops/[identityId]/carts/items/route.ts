import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { CartItem } from '@/app/api/types/backend/shop/CartItem';
import { apiAuth } from '@/app/api/utils/auth';
import { snakeToCamelObject } from '@/app/api/utils/formatConvert';
import { validateFields } from '@/app/api/utils/validation';
import { addCartItemRules, CartItemRequest } from '@/app/api/utils/validationRules/carts';

export async function GET(request: NextRequest) {
  const client = createAxiosClient(request);
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json(null);
  }
  const { searchParams } = new URL(request.url);
  const creatorIdentityId = searchParams.get('creator_identity_id');
  const cartItemsPath = `/shops/${creatorIdentityId}/cart-items`;

  try {
    const cartResponse = await client.get(cartItemsPath);
    const data = cartResponse.data;
    const items = data.data.cart['cart_items'];
    const isLocked = data.data.cart['is_locked'];
    const deliveryFee = data.data.cart['delivery_fee'];

    const formattedItems = items.map((item: any) => {
      const { cart_id, name, thumbnail_uri, item_type, purchasable_quantity, purchaser_comment, ...rest } = item;
      return {
        ...rest,
        cartId: cart_id,
        title: name,
        itemType: item_type,
        thumbnail: thumbnail_uri,
        purchasableQuantity: purchasable_quantity,
        purchaserComment: purchaser_comment,
      };
    });

    const cartItems: CartItem[] = snakeToCamelObject(formattedItems);
    const totalQuantity = cartItems.reduce((total, item) => total + item.quantity, 0);

    return NextResponse.json({
      data: {
        items: cartItems,
        totalQuantity,
        deliveryFee,
        isLocked,
      },
    });
  } catch (error) {
    console.error(error);
    return NextResponse.json({ message: 'カートの情報を取得に失敗しました', details: error }, { status: 500 });
  }
}

interface AddCartItemsBody {
  item: CartItemRequest;
  identityId: string;
}

export async function POST(request: NextRequest, { params }: { params: { identityId: string } }) {
  const client = createAxiosClient(request);
  const requestBody: AddCartItemsBody = await request.json();
  const { item } = requestBody;
  const { identityId } = params;

  // 各アイテムに対してバリデーションを実行
  const itemValidation = validateFields(item, addCartItemRules);
  if (!itemValidation.isValid) {
    return NextResponse.json({ error: itemValidation.error }, { status: itemValidation.status || 400 });
  }

  const { itemId } = item;
  if (itemId === undefined) {
    return NextResponse.json({ message: '商品を選択してください' }, { status: 400 });
  }

  const itemRequestBody = { item_id: item.itemId, quantity: item.quantity, single_file_id: item.singleFileId };

  try {
    const response = await client.post(`/shops/${identityId}/cart-items`, itemRequestBody);
    const data = response.data;
    if (data.errors?.length > 0) {
      return NextResponse.json(
        {
          errors: data.errors,
        },
        { status: 400 },
      );
    }
    return NextResponse.json({
      data: data,
      errors: null,
    });
  } catch (error: any) {
    if (error.response?.data?.errors) {
      return NextResponse.json(
        {
          errors: error.response.data.errors,
        },
        { status: 400 },
      );
    }

    return NextResponse.json(
      {
        errors: [
          {
            code: 9999,
            message: 'Unexpected error occurred',
          },
        ],
      },
      { status: 500 },
    );
  }
}
