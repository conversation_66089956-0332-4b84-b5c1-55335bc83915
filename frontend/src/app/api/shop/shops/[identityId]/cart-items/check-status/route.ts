import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import type { ApiResponse, CartItemCheckResponseData } from '@/types/api';
import type { CartItemCheckResultData } from '@/types/cart';

interface BackendResponse {
  data: {
    result: CartItemCheckResultData;
  };
  errors: any[];
}

export async function POST(
  request: NextRequest,
  { params }: { params: { identityId: string } },
): Promise<NextResponse<ApiResponse<CartItemCheckResponseData>>> {
  const client = createAxiosClient(request);
  try {
    const body = await request.json();
    const response = await client.post<BackendResponse>(`/shops/${params.identityId}/cart-items/check-price`, body);

    const checkResult = response.data.data.result;

    const responseData = {
      data: {
        result: checkResult,
      },
      statusText: 'success',
    };

    return NextResponse.json(responseData, { status: 200 });
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: error.message, statusText: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { identityId: string } },
): Promise<NextResponse<ApiResponse<{ deleted_cart_items: number }>>> {
  const client = createAxiosClient(request);
  try {
    const response = await client.delete(`/shops/${params.identityId}/cart-items/invalid-items`);

    return NextResponse.json(
      {
        data: {
          deleted_cart_items: response.data.deleted_cart_items || 0,
        },
        statusText: 'success',
      },
      { status: 200 },
    );
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: error.message, statusText: 'Internal Server Error' }, { status: 500 });
  }
}
