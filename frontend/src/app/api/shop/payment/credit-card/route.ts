import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { CreditCard } from '@/app/api/types/backend/shop/CreditCard';
import { snakeToCamelObject } from '@/app/api/utils/formatConvert';
import { validateFields } from '@/app/api/utils/validation';
import {
  registerCreditCardRules,
  updateCreditCardRules,
  deleteCreditCardRules,
} from '@/app/api/utils/validationRules/creditCard';
import { RegisterCreditCardRequest, UpdateCreditCardRequest, DeleteCreditCardRequest } from '@/types/api/creditCard';

export async function GET(request: NextRequest) {
  const client = createAxiosClient(request);
  const path = `/cards`;

  try {
    const response = await client.get(path);
    const data = response.data;
    const creditCards: CreditCard[] = snakeToCamelObject(data.data.cards);

    return NextResponse.json({ data: creditCards });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    throw new Error('fetch failed');
  }
}

export async function POST(request: NextRequest) {
  const body: RegisterCreditCardRequest = await request.json();
  const client = createAxiosClient(request);
  const path = `/cards`;

  try {
    const validation = validateFields(body, registerCreditCardRules);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status || 400 });
    }

    const requestBody = {
      card_name: body.cardName,
      token: body.token,
    };

    const response = await client.post(path, requestBody);

    return NextResponse.json({ data: response.data, status: response.status });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return new NextResponse(null, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  const body: DeleteCreditCardRequest = await request.json();
  const client = createAxiosClient(request);
  const path = `/card/delete/${body.cardSeq}`;

  try {
    const validation = validateFields(body, deleteCreditCardRules);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status || 400 });
    }

    const response = await client.delete(path);

    if (response.data.errors.length > 0) {
      return NextResponse.json(response.data.errors, { status: response.status });
    }
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return new NextResponse(null, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  const body: UpdateCreditCardRequest = await request.json();
  const client = createAxiosClient(request);
  const path = `/card/update`;

  try {
    const validation = validateFields(body, updateCreditCardRules);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: validation.status || 400 });
    }

    const response = await client.put(path, body);

    if (response.data.errors.length > 0) {
      return NextResponse.json(response.data.errors, { status: response.status });
    }
    return new NextResponse(null, { status: 200 });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return new NextResponse(null, { status: 500 });
  }
}
