import fs from 'fs';
import https from 'https';
import axios from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { APPLE_MERCHANT_ID } from '@/consts/payment';

export async function GET(request: NextRequest) {
  const validationUrl = request.nextUrl.searchParams.get('validationUrl');
  if (!validationUrl) {
    return new NextResponse('validationUrl is required', { status: 400 });
  }

  const httpsAgent = new https.Agent({
    cert: fs.readFileSync('cert/cert.pem'),
    key: fs.readFileSync('cert/key.pem'),
  });

  try {
    const response = await axios.post(
      validationUrl,
      {
        merchantIdentifier: APPLE_MERCHANT_ID,
        displayName: 'FANME',
        initiative: 'web',
        initiativeContext: process.env.NEXT_PUBLIC_APPLE_MERCHANT_DOMAIN,
      },
      {
        httpsAgent,
      },
    );
    return NextResponse.json({
      data: response.data,
    });
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: 'fetch apple pay token failed' }, { status: 500 });
  }
}
