import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { ConvenienceFee } from '@/app/api/types/backend/shop/ConvenienceFee';
import { snakeToCamelObject } from '@/app/api/utils/formatConvert';

export async function GET(request: NextRequest) {
  const client = createAxiosClient(request);
  const path = `/orders/convenience-fees`;

  try {
    const response = await client.get(path);
    const data = response.data;
    const convenienceFees: ConvenienceFee[] = snakeToCamelObject(data.data['convenience_fees']);

    return NextResponse.json(
      {
        data: convenienceFees,
        statusText: response.statusText || 'OK',
      },
      {
        status: response.status,
      },
    );
  } catch (error) {
    console.error(JSON.stringify({ error }));
    throw new Error('fetch failed');
  }
}
