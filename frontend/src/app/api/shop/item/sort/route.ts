import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { apiAuth } from '@/app/api/utils/auth';

export async function PUT(request: NextRequest) {
  const client = createAxiosClient(request);
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const requestBody = await request.json();
    const backendRequest = {
      items: requestBody.items.map((item: { id: number; sort_order: number }) => ({
        id: item.id,
        sort_order: item.sort_order,
      })),
    };

    const response = await client.put('/shops/current/items/sort', backendRequest);
    return NextResponse.json(response.data);
  } catch (error: any) {
    if (error.response?.data) {
      return NextResponse.json(error.response.data, {
        status: error.response.status,
      });
    }

    return NextResponse.json(
      {
        data: {},
        errors: [
          {
            code: 500,
            message: 'Failed to process item sort',
          },
        ],
      },
      {
        status: 500,
      },
    );
  }
}
