import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { apiAuth } from '@/app/api/utils/auth';
import { validateFields, ValidationRule } from '@/app/api/utils/validation';

interface UploadUrlParams {
  metadataList: UploadFileMetadata[];
  isCover: boolean;
  isPublic: boolean;
}

interface UploadFileMetadata {
  id: string;
  name: string | null;
}

const validationRules: ValidationRule<UploadUrlParams>[] = [
  {
    field: 'metadataList',
    required: true,
    message: 'metadataList are required',
    status: 400,
  },
];

export async function POST(request: NextRequest) {
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const requestData = await request.json();
    const validation = validateFields(requestData, validationRules);
    if (!validation.isValid) {
      return Response.json({ error: validation.error }, { status: validation.status || 400 });
    }
    const { metadataList, isCover, isPublic } = requestData;
    const client = createAxiosClient(request);
    const response = await client.post(
      `/shops/current/files/upload-url?is_cover_image=${isCover}&is_public=${isPublic}`,
      {
        metadataList: metadataList,
      },
    );
    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json(
      { error: error.response?.data?.message || 'Failed to get upload URL' },
      { status: error.response?.status || 500 },
    );
  }
}
