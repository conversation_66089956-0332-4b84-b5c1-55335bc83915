import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { CreateOrderRequest } from '@/types/api/order';

export async function POST(request: NextRequest) {
  const body: CreateOrderRequest = await request.json();
  const client = createAxiosClient(request);
  const path = `/orders`;

  try {
    const response = await client.post(path, body);

    if (response.data.errors.length > 0) {
      return NextResponse.json(response.data.errors, { status: response.status });
    }

    const orderData = response.data?.data?.order;
    return NextResponse.json(
      {
        data: {
          orderId: orderData.order.id,
          convenienceCheckout: orderData.convenience_checkout,
          redirectUrl: orderData.redirect_url,
        },
      },
      { status: 200 },
    );
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return new NextResponse(null, { status: 500 });
  }
}
