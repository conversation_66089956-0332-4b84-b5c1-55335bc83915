import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { apiAuth } from '@/app/api/utils/auth';
import { validateFields, ValidationRule } from '@/app/api/utils/validation';

interface PreSignedUrlParams {
  creatorAccountIdentity: string;
  metadataList: PreSignedUrlMetadata[];
}

interface PreSignedUrlMetadata {
  id: string;
  key: string;
}

const validationRules: ValidationRule<PreSignedUrlParams>[] = [
  {
    field: 'metadataList',
    required: true,
    message: 'metadataList is required',
    status: 400,
  },
];

export async function POST(request: NextRequest) {
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const requestData = await request.json();
    const validation = validateFields(requestData, validationRules);
    if (!validation.isValid) {
      return Response.json({ error: validation.error }, { status: validation.status || 400 });
    }
    const { creatorAccountIdentity, metadataList } = requestData;
    const metadata = metadataList.map((data: any) => {
      return {
        id: data.id,
        key: data.key,
      };
    });
    const client = createAxiosClient(request);
    const response = await client.post(`/shops/current/files/presigned-url`, {
      creator_account_identity: creatorAccountIdentity,
      metadata_list: metadata,
    });
    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error('Error getting signed URL:', error);
    return NextResponse.json(
      { error: error.response?.data?.message || 'Failed to get signed URL' },
      { status: error.response?.status || 500 },
    );
  }
}
