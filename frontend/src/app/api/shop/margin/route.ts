import { AxiosError } from 'axios';
import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { apiAuth } from '@/app/api/utils/auth';

export async function GET(request: NextRequest) {
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  const client = createAxiosClient(request);
  const shopPath = '/shops/current';

  try {
    const response = await client.get(shopPath);

    const marginRate = response.data.data.shop.margin_rate;

    return NextResponse.json({ data: { marginRate } });
  } catch (error) {
    if (error instanceof AxiosError) {
      return NextResponse.json({ error: 'Failed to fetch shop data' }, { status: error.response?.status });
    }
    console.error(JSON.stringify({ error }));
    return NextResponse.json({ error: 'Failed to fetch shop data' }, { status: 500 });
  }
}
