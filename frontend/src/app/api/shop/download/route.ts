import { NextRequest, NextResponse } from 'next/server';
import { createAxiosClient } from '@/app/api/_client';
import { apiAuth } from '@/app/api/utils/auth';
import { validateFields, ValidationRule } from '@/app/api/utils/validation';

interface DownloadUrlParams {
  itemId: number;
  metadataList: DownloadFileMetadata[];
}

interface DownloadFileMetadata {
  key: string;
  name: string | null;
}

const validationRules: ValidationRule<DownloadUrlParams>[] = [
  {
    field: 'metadataList',
    required: true,
    message: 'metadataList are required',
    status: 400,
  },
];

export async function POST(request: NextRequest) {
  const isAuth = await apiAuth();
  if (!isAuth) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  try {
    const requestData = await request.json();
    const validation = validateFields(requestData, validationRules);
    if (!validation.isValid) {
      return Response.json({ error: validation.error }, { status: validation.status || 400 });
    }
    const { itemId, metadataList } = requestData;
    const metadata = metadataList.map((item: any) => {
      return {
        key: item.key,
        name: item.name,
      };
    });
    const client = createAxiosClient(request);
    const response = await client.post(`/shops/current/files/download-url`, {
      item_id: itemId,
      metadata_list: metadata,
    });
    return NextResponse.json(response.data, { status: 200 });
  } catch (error: any) {
    console.error(JSON.stringify({ error }));
    return NextResponse.json(
      { error: error.response?.data?.message || 'Failed to get download URL' },
      { status: error.response?.status || 500 },
    );
  }
}
