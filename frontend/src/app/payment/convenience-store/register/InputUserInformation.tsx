'use client';

import React from 'react';
import { Control, FieldErrors } from 'react-hook-form';
import TextInput from '@/components/atoms/inputs/text-input';
import { useOrderFormStore } from '@/store/useOrderFormStore';

type FormData = {
  name: string;
  kana: string;
  telNo: string;
};

type Props = {
  control: Control<FormData>;
  errors: FieldErrors<FormData>;
};

const InputUserInformation = ({ control, errors }: Props) => {
  const { convenienceParam } = useOrderFormStore();

  return (
    <form className="flex w-full flex-col items-start justify-center gap-2">
      <div className="flex w-full flex-col items-start justify-center gap-2">
        <div className="flex w-full flex-col">
          <TextInput
            inputName="name"
            control={control}
            labelTitle="お名前"
            placeholder="山田太郎"
            required={true}
            error={!!errors.name}
            errorMsg={errors.name?.message}
            className="w-full"
            inputClassName="placeholder:text-left"
            defaultValue={convenienceParam.customerName}
          />
          <div className="relative bottom-2 text-regular-11 text-gray-500">
            *機種依存文字を使用しないでください、
            <br />
            コンビニ決済ができない場合があります
          </div>
        </div>
        <TextInput
          inputName="kana"
          control={control}
          labelTitle="フリガナ"
          placeholder="ヤマダ　タロウ"
          required={true}
          error={!!errors.kana}
          errorMsg={errors.kana?.message}
          className="w-full"
          inputClassName="placeholder:text-left"
          defaultValue={convenienceParam.customerKana}
        />
        <TextInput
          inputName="telNo"
          control={control}
          labelTitle="電話番号"
          placeholder="09012345678"
          required={true}
          error={!!errors.telNo}
          errorMsg={errors.telNo?.message}
          className="w-37.5"
          inputClassName="placeholder:text-left"
          defaultValue={convenienceParam.telNo}
        />
      </div>
    </form>
  );
};

export default InputUserInformation;
