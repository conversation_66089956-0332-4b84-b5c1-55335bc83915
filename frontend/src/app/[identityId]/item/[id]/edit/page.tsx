import { notFound } from 'next/navigation';
import ExhibitItemForm from '@/components/containers/ExhibitItemForm';
import type { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { getShop } from '@/lib/server-api/shop-endpoint/shop-endpoint';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';
import { DigitalBundle, ExhibitChekiItem, ExhibitGachaItem } from '@/types/exhibitItem';
import { ITEM_TYPE } from '@/types/item';
import { ExhibitType } from '@/types/shopItem';

const EditItem = async ({
  params,
  searchParams,
}: {
  params: { id: string; identityId: string };
  searchParams: { item_type: ExhibitType };
}) => {
  const itemType = searchParams.item_type;
  const itemId = params.id;
  const identityId = getUserIdentityId(params.identityId);

  const [shopItem, shopResponseBody] = await Promise.all([
    getItem(itemId, identityId, false, true),
    getShop(identityId),
  ]);
  const pageData = await shopItem.json().then((res) => {
    if (res.error) {
      return notFound();
    }
    const { item } = res.data;
    switch (itemType) {
      case ITEM_TYPE.DIGITAL_BUNDLE.str:
        return {
          ...item,
          priceSet: item.price,
          itemFiles: item.itemFiles.map((file: any) => ({
            ...file,
            isSingleSale: (file.price || 0) > 0,
          })),
          singleSaleDefault: item.singleSale,
          singlePrice: 0,
          selectedIndex: null,
          thumbnailCustomImage: item.thumbnailType === 'custom' ? item.thumbnail : '',
          samples: item.samples,
          benefits: item.benefits,
        } as DigitalBundle;
      case ITEM_TYPE.DIGITAL_GACHA.str:
        return {
          ...item,
          priceSet: item.price,
          itemFiles: item.itemFiles.map((file: any) => ({
            ...file,
            isSingleSale: (file.price || 0) > 0,
          })),
          totalCapacity: item.totalCapacity,
          isDuplicated: item.isDuplicated,
          awardProbabilities: item.awardProbabilities,
          thumbnailCustomImage: item.thumbnailType === 'custom' ? item.thumbnail : '',
          samples: item.samples,
          benefits: item.benefits,
        } as ExhibitGachaItem;
      case ITEM_TYPE.CHEKI.str:
        return {
          ...item,
          priceSet: item.price,
          samples: item.samples,
          benefits: item.benefits,
          thumbnailCustomImage: item.thumbnail,
        } as ExhibitChekiItem;
      default:
        return notFound();
    }
  });

  // TODO(KUDO): 実際の使用場所と離れたところでundefinedの時の処理を書いているので読みにくくなっている
  // 実際のundefinedの時の処理の場所で記載したい
  const shopLimitation: ShopLimitation = shopResponseBody?.data?.shop?.limitation || {
    fileCapacity: 0,
    fileQuantity: 0,
    isChekiExhibitable: false,
    createdAt: null,
    updatedAt: null,
  };

  return (
    <ExhibitItemForm
      itemParams={{
        itemId,
        itemType,
      }}
      identityId={identityId}
      pageData={pageData}
      shopLimitation={shopLimitation}
      isEdit={true}
    />
  );
};

export default EditItem;
