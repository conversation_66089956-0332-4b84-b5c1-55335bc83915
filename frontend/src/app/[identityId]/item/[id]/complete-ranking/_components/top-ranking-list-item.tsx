import clsx from 'clsx';
import Link from 'next/link';
import Avatar from '@/components/atoms/avatar';
import { RankingCrownBadge } from '@/components/atoms/ranking-crown-badge';
import { RightArrowIcon } from '@/components/atoms/right-arrow-icon';
import ShopPublicImage from '@/components/ShopImage';
import type { GetDigitalGachaCompleteBadgeRankingResponse } from '@/lib/server-api/shop-api.schemas';
import { RankingBackground } from './ranking-background';
import { getRankStyle } from '@/utils/complete-ranking';
import { formatRankingDate } from '@/utils/time';
import type { CompleteTopRank } from '@/types/common';

type TopRankingListItemProps = {
  entry: GetDigitalGachaCompleteBadgeRankingResponse;
  rank: CompleteTopRank;
  itemId: string;
  identityId: string;
  isHighlighted?: boolean;
};

export const TopRankingListItem = ({ entry, rank, isHighlighted = false }: TopRankingListItemProps) => {
  const { userAccountIdentity, userName, userIcon, getBadgeAt } = entry;

  const decorationColor = getRankStyle.decorationColor(rank);
  const avatarBorderColor = getRankStyle.avatarBorder(rank);

  const borderClass = clsx(
    'border-2',
    rank === 1 && 'border-ranking-first-border',
    rank === 2 && 'border-ranking-second-border',
    rank === 3 && 'border-ranking-third-border',
    rank !== 1 && rank !== 2 && rank !== 3 && 'border-gray-200 border',
  );

  return (
    <div
      className={clsx('relative mb-3 h-26 overflow-hidden rounded-xl', borderClass, {
        'bg-ranking-highlight-bg': isHighlighted,
      })}
    >
      {decorationColor && (
        <div className="absolute inset-0 flex items-center justify-center">
          <RankingBackground
            color={decorationColor}
            opacity={getRankStyle.opacity(rank, isHighlighted)}
            className="h-22 w-20"
          />
        </div>
      )}

      <div className="relative z-10 flex h-full items-center gap-1 p-4">
        <div className="flex-shrink-0">
          <Avatar src={userIcon} alt={userName} size="large" borderColor={avatarBorderColor} />
        </div>

        <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center">
          <RankingCrownBadge rank={rank} color={decorationColor} />
        </div>

        <div className="min-w-0 flex-1">
          <div
            className={clsx('mb-1 truncate text-medium-14', {
              'text-black': isHighlighted,
              'text-white': !isHighlighted,
            })}
          >
            {userName}
          </div>
          <div
            className={clsx('text-regular-12 opacity-80', {
              'text-black': isHighlighted,
              'text-white': !isHighlighted,
            })}
          >
            {formatRankingDate(getBadgeAt)}
          </div>
        </div>

        <div className="relative flex-shrink-0">
          <Link
            href={userAccountIdentity ? `${process.env.NEXT_PUBLIC_FANME_LINK_URL}/@${userAccountIdentity}` : '#'}
            className={clsx('flex h-7 w-12 items-center justify-center gap-1 rounded-2xl border', {
              'border-black': isHighlighted,
              'border-white': !isHighlighted,
              'pointer-events-none cursor-not-allowed opacity-50': !userAccountIdentity,
            })}
          >
            <ShopPublicImage
              src="/images/gacha/icons/user.svg"
              alt="User"
              width={14}
              height={14}
              className={clsx({
                'invert filter': isHighlighted,
              })}
            />
            <RightArrowIcon color={isHighlighted ? 'black' : 'white'} width={14} height={14} />
          </Link>
          {isHighlighted && (
            <span className="absolute -bottom-5 left-1/2 -translate-x-1/2 whitespace-nowrap text-regular-9 text-black">
              あなたの順位
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
