import { notFound, redirect } from 'next/navigation';
import HeaderLayout from '@/components/layouts/Header';
import MainItemViewerSection from './_components/main-item-viewer-section';
import BenefitsSection from './BenefitsSection';
import NotesSection from './NotesSection';
import { getItem } from '@/app/actions/shopItem';
import { getUserIdentityId } from '@/utils/base';
import { ShopItemDetail } from '@/types/shopItem';

type ItemViewerProps = {
  identityId: string;
  itemId: string;
};

const ItemViewer = async ({ params }: { params: ItemViewerProps }) => {
  const identityId = getUserIdentityId(params.identityId);
  if (isNaN(Number(params.itemId))) {
    return notFound();
  }

  let redirectTo = '';

  try {
    const response = await getItem(params.itemId, identityId, true);
    const { data } = await response.json();
    const itemData = data as ShopItemDetail;
    const item = itemData.item;
    const hasPurchasedFile = item.itemFiles.some((file) => file.isPurchased === true);
    if (!item.isPurchased && !hasPurchasedFile) {
      redirectTo = `/${identityId}/item/${params.itemId}`;
      redirect(redirectTo);
      return null;
    }

    return (
      <div className="flex flex-col items-center justify-center bg-gray-100">
        <HeaderLayout title={item.title} />
        <MainItemViewerSection itemId={item.id} itemFiles={item.itemFiles} purchasedOnly={true} />
        {item.benefits && item.benefits.length > 0 && item.isPurchased && (
          <BenefitsSection itemId={item.id} benefits={item.benefits} />
        )}
        <NotesSection />
      </div>
    );
  } catch (e) {
    console.error(e);
    return notFound();
  }
};

export default ItemViewer;
