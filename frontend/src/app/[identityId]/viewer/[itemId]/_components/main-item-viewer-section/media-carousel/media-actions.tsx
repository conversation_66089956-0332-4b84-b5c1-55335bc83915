import React from 'react';
import Button from '@/components/atoms/button';
import ShopPublicImage from '@/components/ShopImage';
import { useViewerStore } from '@/store/useViewerStore';
import { useIsPCBrowser } from '@/hooks/useIsPCBrowser';
import { SingleItem } from '@/types/shopItem';

type MediaActionsProps = {
  file: SingleItem;
};

const MediaActions = ({ file }: MediaActionsProps) => {
  const isPCBrowser = useIsPCBrowser();
  const { setBookViewer } = useViewerStore();

  if (file.type !== 'image') {
    return (
      <div className="absolute inset-0 grid place-content-center">
        <Button buttonShape="circle" buttonSize="md" buttonType="light">
          <div className="triangle-right" />
        </Button>
      </div>
    );
  }
  return (
    <>
      {isPCBrowser && (
        <ShopPublicImage
          src={'/images/icons/Book.svg'}
          width={32}
          height={32}
          alt="zoom"
          className="absolute bottom-3 right-12 cursor-pointer"
          onClick={() => setBookViewer(true)}
        />
      )}
      <ShopPublicImage
        src={'/images/icons/Scaling.svg'}
        width={24}
        height={24}
        alt="zoom"
        className="absolute bottom-4 right-4 cursor-pointer"
        onClick={() => setBookViewer(false)}
      />
    </>
  );
};

export default MediaActions;
