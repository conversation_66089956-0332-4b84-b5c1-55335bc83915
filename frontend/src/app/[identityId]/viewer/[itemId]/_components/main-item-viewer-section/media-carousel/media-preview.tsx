import React from 'react';
import ItemIcon from '@/components/atoms/itemIcon';
import { SingleItem } from '@/types/shopItem';

type MediaPreviewProps = {
  file: SingleItem;
};

const MediaPreview = ({ file }: MediaPreviewProps) => {
  if (file.type === 'image') {
    const imgSrc = file.src;
    return <ItemIcon thumbnail={imgSrc} thumbnailRatio={1} title={file.title} size={352} />;
  }
  if (file.type === 'video') {
    if (!file.thumbnail) {
      return null;
    }
    return (
      <div className="aspect-square w-full overflow-hidden rounded-2xl bg-gray-700">
        <img src={file.thumbnail} alt="Video Thumbnail" width={352} height={352} className="size-full object-contain" />
      </div>
    );
  }
  if (file.type === 'audio') {
    return (
      <div
        className="flex aspect-square w-full items-center justify-center rounded-2xl p-4"
        style={{
          backgroundImage: 'url(/shop/images/VoicePlayBackGround.webp)',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      />
    );
  }
  return null;
};

export default MediaPreview;
