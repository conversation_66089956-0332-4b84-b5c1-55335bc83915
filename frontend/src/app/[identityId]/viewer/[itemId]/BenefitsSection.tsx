'use client';

import React from 'react';
import toast from 'react-hot-toast';
import CustomToast from '@/components/atoms/toast/custom-toast';
import Instruction from '@/components/atoms/typography/instruction';
import ShopPublicImage from '@/components/ShopImage';
import BenefitItemList from './_components/benefit-item-list';
import { fileService } from '@/services/file';
import type { GachaBenefitFile } from '@/types/gacha';
import type { Benefit, SingleItem } from '@/types/shopItem';

interface BenefitsSectionProps {
  itemId: string;
  benefits: Benefit[];
}

const BenefitsSection = ({ itemId, benefits }: BenefitsSectionProps) => {
  // Using useState and useEffect to handle hydration mismatch
  // Issue: isIOS() returns different values on server and client
  // - Server: Always returns false (no window object)
  // - Client: Returns true/false based on user agent
  // Solution: Initially hide buttons (false) and show them after client-side check

  const firstBenefit = benefits && benefits.length > 0 ? benefits[0] : null;

  const onSingleDownloadFile = async (file: SingleItem | GachaBenefitFile) => {
    await downloadFiles([file]);
  };

  const downloadFiles = async (files: (SingleItem | GachaBenefitFile)[]) => {
    try {
      const downloadUrls = await fileService.getDownloadUrl({
        itemId: parseInt(itemId),
        fileData: files
          .filter((file) => !!file.type)
          .map((item) => ({ item, name: fileService.getValidFileNameForDownload(item.title, item.type!) })),
      });
      downloadUrls.forEach((downloadItem, index) => {
        setTimeout(() => {
          fileService.downloadByLink(downloadItem.url);
        }, index * 200);
      });
    } catch (error) {
      console.error('Error downloading file:', error);
      toast.custom((t) => CustomToast(t, 'error', 'ダウンロードに失敗しました'), {
        id: 'download-failed',
      });
    }
  };

  return (
    <section className="flex w-full flex-col gap-4 bg-gray-100 px-2 py-4">
      <div className="flex w-full flex-col gap-3 rounded-2xl bg-yellow-50 p-4">
        <div className="flex items-center justify-between gap-1">
          <div className="flex items-center gap-1">
            <ShopPublicImage src="/images/icons/GiftOrange.svg" alt="セット購入特典" width={22} height={22} />
            <h2 className="text-medium-16 text-orange-200">セット購入特典</h2>
          </div>
          <Instruction className="text-red-500">* 各特典クリックで内容確認</Instruction>
        </div>
        {firstBenefit?.description && (
          <div className="whitespace-pre-wrap rounded-2xl bg-white p-2 text-regular-13">{firstBenefit.description}</div>
        )}
        <BenefitItemList items={firstBenefit?.benefitFiles || []} readOnly={false} onDownload={onSingleDownloadFile} />
      </div>
    </section>
  );
};

export default BenefitsSection;
