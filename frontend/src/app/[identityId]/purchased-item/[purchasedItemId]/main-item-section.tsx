import React from 'react';
import ReadMoreButton from '@/components/containers/ReadMoreButton';

interface MainItemSectionProps {
  name: string;
  thumbnail: string;
  description?: string;
}

const MainItemSection = ({ thumbnail, name, description }: MainItemSectionProps) => {
  return (
    <div className="flex w-full flex-col gap-4 px-4">
      <div className="flex flex-col items-center justify-center gap-2 rounded-b-2xl">
        <div className="relative w-full" style={{ aspectRatio: '1 / 1' }}>
          <img src={thumbnail} alt="thumbnail" className="size-full rounded-lg object-cover" sizes="100vw" />
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <p className="text-bold-16 text-gray-800">{name}</p>
        {!!description && (
          <div className="whitespace-pre-wrap text-regular-13">
            <ReadMoreButton description={description?.trim()} backgroundColor="#f2f2f2" />
          </div>
        )}
      </div>
    </div>
  );
};

export default MainItemSection;
