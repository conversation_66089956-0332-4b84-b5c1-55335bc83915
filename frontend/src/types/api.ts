import type { CartItem, CartItemCheckResultData } from '@/types/cart';
import type { Card } from '@/types/creditCard';
import type { Shop } from '@/types/shopinfo';
import type { Benefit, SingleItem } from '@/types/shopItem';

// Backend API response format
export interface BackendResponse<T = any> {
  data: T;
  errors: ApiError[];
}

// Frontend API response format
export interface ApiResponse<T = any> {
  statusText: string;
  data?: T;
  error?: string;
}

export interface ApiError {
  statusText: string;
  code: number;
  message: string;
  data?: any;
}

export interface CreateOrUpdateItemRequest {
  title: string;
  description?: string;
  thumbnail: string;
  thumbnailFrom: 0 | 1;
  thumbnailBlurLevel: number;
  thumbnailWatermarkLevel: number;
  priceSet: number;
  available: boolean;
  itemFiles: SingleItem[];
  itemType: number;
  samples?: SingleItem[];
  benefits?: Benefit[];
  tags?: string[];
  itemOption?: {
    singleSale?: boolean;
    limited?: number;
    limitedPerUser?: number;
    period?: {
      start?: string;
      end?: string;
    };
    password?: string;
    discount?: {
      percentage: number;
      start?: string;
      end?: string;
    };
  };
}

export interface SortItemsRequest {
  items: {
    id: string;
    sort_order: number;
  }[];
}

export interface CreateShopRequest {
  name: string;
  description?: string;
  headerImageUri?: string;
  message?: string;
}

export interface CreateShopResponse {
  shop: {
    shop: Shop;
    isContentBlockCreated: boolean;
  };
}

export interface CartResponse {
  data: {
    items: CartItem[];
    totalQuantity: number;
    tipLimit: number;
    deliveryFee?: number;
    yell: {
      count: number;
      boostRatio: number;
    };
    isLocked: boolean;
  };
}

export type CartItemRequest = {
  itemId: number;
  singleFileId?: string;
  quantity: number;
};

export interface CardResponse {
  data: Card[];
}

export type CartItemCheckResponseData = {
  result: CartItemCheckResultData;
};

export type CartItemCheckResponse = ApiResponse<CartItemCheckResponseData>;

export type CartItemResponse = ApiResponse<{
  items: CartItem[];
  totalQuantity: number;
}>;
