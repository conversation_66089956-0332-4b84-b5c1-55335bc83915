import { z } from 'zod';

export const convenienceFormSchema = z.object({
  name: z
    .string()
    .min(1, { message: 'お名前を入力してください' })
    .max(50, { message: 'お名前は50文字以内で入力してください' }),
  kana: z
    .string()
    .min(1, { message: 'フリガナを入力してください' })
    .max(50, { message: 'フリガナは50文字以内で入力してください' })
    .regex(/^[\u30A1-\u30FC\u0020\u3000]*$/, { message: 'カタカナとスペースのみで入力してください' }),
  telNo: z
    .string()
    .min(1, { message: '電話番号を入力してください' })
    .regex(/^(0[0-9]{9,10}|0[0-9]-[0-9]{4}-[0-9]{4})$/, {
      message: '正しい電話番号形式で入力してください',
    }),
});

export type ConvenienceFormData = z.infer<typeof convenienceFormSchema>;
