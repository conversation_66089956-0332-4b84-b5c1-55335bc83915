import type { Benefit, BenefitFile1 } from '@/lib/server-api/shop-api.schemas';
import type { BaseItemFile, MediaType, BaseShopItemDetail } from './shopItem';
import type { GachaFile } from '@/app/api/types/backend/shop/ShopItem';
export const AWARD_TYPE = {
  S: 4,
  A: 3,
  B: 2,
  C: 1,
} as const;
export type Award = (typeof AWARD_TYPE)[keyof typeof AWARD_TYPE];
export const MAX_AWARD_COUNT = Object.keys(AWARD_TYPE).length;
export type AwardProbability = {
  awardType: Award;
  probability: number;
};
export const CONDITION_TYPE = {
  COMPLETED: 100,
  TIMES_10: 101,
  TIMES_20: 102,
  TIMES_30: 103,
} as const;
export type ConditionType = (typeof CONDITION_TYPE)[keyof typeof CONDITION_TYPE];
// Gacha item type with specific fields for digital gacha
export interface GachaItemFile extends BaseItemFile {
  sortOrder: number | null;
  awardType: Award | null;
  isSecret: boolean | null;
  receivedFileCount?: number;
  isNew?: boolean;
  maskedThumbnailUri?: string;
}
export interface GachaBenefitFile
  extends Omit<BenefitFile1, 'itemThumbnailSelected' | 'thumbnailUri' | 'name' | 'conditionType' | 'fileType'> {
  title: string;
  id: string;
  conditionType: ConditionType;
  type: MediaType;
  thumbnail?: string;
  preSignedThumbnailUrl?: string;
}

// export interface GachaBenefit {
//   id?: number;
//   description?: BenefitDescription;
//   conditionType?: number;
//   itemFiles: BenefitFile1[];
// }
export interface GachaBenefit extends Omit<Benefit, 'files' | 'conditionType'> {
  benefitFiles: GachaBenefitFile[];
  conditionType: ConditionType;
}

// Gacha shop item detail

export type GachaItemContent = Omit<BaseShopItemDetail['item'], 'thumbnailType'> & {
  totalCapacity: number;
  collectedUniqueItemsCount: number;
  remainingUniquePullCount: number;
  isCompleted: boolean;
  duplicatedCount: number;
  itemFiles: GachaItemFile[];
  samples?: GachaItemFile[];
  benefits?: GachaBenefit[];
  isDuplicated: boolean;
  awardProbabilities: AwardProbability[];
  thumbnailType: 'custom';
};
export interface GachaItem extends BaseShopItemDetail {
  item: GachaItemContent;
}

export interface PullGachaResponse extends GachaItem {
  currentPulledFiles: GachaItemFile[];
  selectedAwardFileId?: string | null;
}

export type GachaFileWithCount = GachaFile & { count?: number };
