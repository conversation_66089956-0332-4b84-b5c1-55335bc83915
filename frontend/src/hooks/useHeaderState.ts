'use client';
import { useMemo } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { PATH_PATTERNS } from '@/consts/router';
import { PageType } from '@/types/common';

const blackBackgroundPageType = [PageType.ORDER, PageType.GACHA_ORDER, PageType.CREDIT_CARD, PageType.EDIT_ADDRESS];
const hideHeaderPageType = [PageType.ITEM_LIST, PageType.ORDER_SUCCESS, PageType.GACHA_RESULT, PageType.COMPS];

export const useHeaderState = () => {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const currentPath = useMemo(() => {
    if (PATH_PATTERNS.CREATE_SHOP.test(pathname) || PATH_PATTERNS.ROOT.test(pathname)) {
      return PageType.CREATE_SHOP;
    } else if (PATH_PATTERNS.TOKUSHOHO.test(pathname)) {
      return PageType.TOKUSHOHO;
    } else if (PATH_PATTERNS.EDIT_SHOP.test(pathname)) {
      return PageType.EDIT_SHOP;
    } else if (PATH_PATTERNS.CREATE_ITEM.test(pathname)) {
      return PageType.CREATE_ITEM;
    } else if (PATH_PATTERNS.CONFIRM_ITEM.test(pathname)) {
      return PageType.CONFIRM_ITEM;
    } else if (PATH_PATTERNS.PREVIEW_ITEM.test(pathname)) {
      return PageType.PREVIEW_ITEM;
    } else if (PATH_PATTERNS.EDIT_ITEM.test(pathname)) {
      return PageType.EDIT_ITEM;
    } else if (PATH_PATTERNS.ITEM_DETAIL.test(pathname)) {
      return PageType.ITEM_DETAIL;
    } else if (PATH_PATTERNS.ITEM_VIEWER.test(pathname)) {
      return PageType.ITEM_VIEWER;
    } else if (PATH_PATTERNS.PURCHASED_ITEM.test(pathname)) {
      return PageType.PURCHASED_ITEM;
    } else if (PATH_PATTERNS.CONVENIENCE_STORE.test(pathname)) {
      return PageType.CONVENIENCE_STORE;
    } else if (PATH_PATTERNS.CREDIT_CARD.test(pathname)) {
      return PageType.CREDIT_CARD;
    } else if (PATH_PATTERNS.ORDER_SUCCESS.test(pathname)) {
      return PageType.ORDER_SUCCESS;
    } else if (PATH_PATTERNS.ORDER.test(pathname)) {
      if (searchParams.get('isGacha') === 'true') {
        return PageType.GACHA_ORDER;
      }
      return PageType.ORDER;
    } else if (PATH_PATTERNS.CART.test(pathname)) {
      return PageType.CART;
    } else if (PATH_PATTERNS.EDIT_ADDRESS.test(pathname)) {
      return PageType.EDIT_ADDRESS;
    } else if (PATH_PATTERNS.GACHA_RESULT.test(pathname)) {
      return PageType.GACHA_RESULT;
    } else if (PATH_PATTERNS.COMPLETE_RANKING.test(pathname)) {
      return PageType.COMPLETE_RANKING;
    } else if (PATH_PATTERNS.COMPS.test(pathname)) {
      return PageType.COMPS;
    } else {
      return PageType.ITEM_LIST;
    }
  }, [pathname, searchParams]);

  const shouldHideHeader = useMemo(
    () => hideHeaderPageType.includes(currentPath),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [hideHeaderPageType, currentPath],
  );
  const shouldHaveBlackBackground = useMemo(
    () => blackBackgroundPageType.includes(currentPath),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [blackBackgroundPageType, currentPath],
  );

  return {
    currentPath,
    shouldHideHeader,
    shouldHaveBlackBackground,
  };
};
