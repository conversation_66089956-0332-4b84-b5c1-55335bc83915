import type { ProbabilityFormValues } from '@/components/containers/GachaUploadSection/probability-input-item';
import { Award, AWARD_TYPE, CONDITION_TYPE } from '@/types/gacha';

const GACHA_PATTERN_KEYS = {
  EMPTY: 'empty',
  PATTERN_1: 'sOnly',
  PATTERN_2: 'sa',
  PATTERN_3: 'sab',
  PATTERN_4: 'sabc',
} as const;

// 確率パターンの定義 (各パターンで全ての賞を含むようにし、不要なものは0%)
const PROBABILITY_PATTERNS = {
  [GACHA_PATTERN_KEYS.PATTERN_1]: { [AWARD_TYPE.S]: 100, [AWARD_TYPE.A]: 0, [AWARD_TYPE.B]: 0, [AWARD_TYPE.C]: 0 },
  [GACHA_PATTERN_KEYS.PATTERN_2]: { [AWARD_TYPE.S]: 20, [AWARD_TYPE.A]: 80, [AWARD_TYPE.B]: 0, [AWARD_TYPE.C]: 0 },
  [GACHA_PATTERN_KEYS.PATTERN_3]: { [AWARD_TYPE.S]: 10, [AWARD_TYPE.A]: 30, [AWARD_TYPE.B]: 60, [AWARD_TYPE.C]: 0 },
  [GACHA_PATTERN_KEYS.PATTERN_4]: { [AWARD_TYPE.S]: 5, [AWARD_TYPE.A]: 15, [AWARD_TYPE.B]: 30, [AWARD_TYPE.C]: 50 },
};
const EMPTY_PROBABILITY_PATTERNS = { [AWARD_TYPE.S]: 0, [AWARD_TYPE.A]: 0, [AWARD_TYPE.B]: 0, [AWARD_TYPE.C]: 0 };
type ProbabilityPatternKey = keyof typeof PROBABILITY_PATTERNS;

// Helper function to map Award-keyed probabilities to form values
const mapProbabilitiesToFormValues = (probs: Record<Award, number>): ProbabilityFormValues => ({
  probability4: probs[AWARD_TYPE.S] || 0,
  probability3: probs[AWARD_TYPE.A] || 0,
  probability2: probs[AWARD_TYPE.B] || 0,
  probability1: probs[AWARD_TYPE.C] || 0,
});

const GACHA_MIN_PROBABILITY = 5;

const GACHA_SINGLE_PULL_COUNT = 1;
const GACHA_MULTI_PULL_COUNT = 10;

const GACHA_BENEFIT_CONDITIONS = [
  { value: CONDITION_TYPE.TIMES_10, label: '10回抽選特典' },
  { value: CONDITION_TYPE.TIMES_20, label: '20回抽選特典' },
  { value: CONDITION_TYPE.TIMES_30, label: '30回抽選特典' },
  { value: CONDITION_TYPE.COMPLETED, label: 'コンプ特典' },
];

export {
  GACHA_PATTERN_KEYS,
  PROBABILITY_PATTERNS,
  EMPTY_PROBABILITY_PATTERNS,
  GACHA_SINGLE_PULL_COUNT,
  GACHA_MIN_PROBABILITY,
  GACHA_MULTI_PULL_COUNT,
  GACHA_BENEFIT_CONDITIONS,
  mapProbabilitiesToFormValues,
  type ProbabilityPatternKey,
};
