'use client';
import clsx from 'clsx';
import ShopCover from '@/components/containers/ShopInfo/ShopCover';
import ShopInfoClientForm from '@/components/containers/ShopInfo/ShopInfoForm';
import ShopPublicImage from '@/components/ShopImage';
import Button from '../atoms/button';
import InputLabel from '../atoms/typography/input-label';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { roboto } from '@/app/fonts';
import { Shop } from '@/types/shopinfo';

interface ICreateShopProps {
  coverImageProp?: string;
  shopInfoProp?: Shop;
  messageProp?: string;
  isEdit?: boolean;
}
const CreateShopComponent = ({ coverImageProp, shopInfoProp, messageProp, isEdit }: ICreateShopProps) => {
  const popupHeader = (
    <>
      SNSなどのシェア時に使われる
      <br />
      ヘッダー画像
    </>
  );
  const popupContent = (
    <div className="flex items-center justify-center bg-gray-50">
      <ShopPublicImage src={'/images/shopCreateCoverModal.webp'} width={228} height={168} alt="modal" />
    </div>
  );
  const popupFooter = (
    <>
      ヘッダー画像は、ショップの上部に表示されます。
      <br />
      また、SNSへの共有、URLをシェアする際の
      <br />
      見出し画像として表示されます。
      <br />
      見栄えの良い画像にしましょう。
    </>
  );
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };
  return (
    <>
      <div className="mb-2 flex items-center justify-between pr-4">
        <InputLabel label="ヘッダー画像" required={true} className="!mb-0" />
        <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
          <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
        </Button>
      </div>
      <ShopCover coverImageProp={coverImageProp} />
      <p className="mb-4 ml-4 mt-1.5 text-regular-11 text-gray-500">
        *[比率]<span className={clsx(roboto.className, 'text-regular-12')}>1:3</span>
      </p>
      <ShopInfoClientForm
        shopName={shopInfoProp?.name ?? ''}
        description={shopInfoProp?.description ?? ''}
        message={messageProp ?? ''}
        isEdit={!!isEdit}
      />
    </>
  );
};

export default CreateShopComponent;
