import React from 'react';
import clsx from 'clsx';
import ItemIcon from '@/components/atoms/itemIcon';
import { IconRadius } from '@/components/atoms/itemIcon';
import { roboto } from '@/app/fonts';
import { GachaBenefitFile } from '@/types/gacha';
import { SingleItem } from '@/types/shopItem';

interface IPreviewSquareItemProps {
  item: SingleItem | GachaBenefitFile;
  radius?: string;
}
const PreviewSquareItem = ({ item, radius = 'lg' }: IPreviewSquareItemProps) => {
  const thumbnail = 'preSignedThumbnailUrl' in item ? item.preSignedThumbnailUrl || '' : item.thumbnail || '';
  return (
    <div className="flex flex-col items-center justify-center">
      <ItemIcon
        thumbnail={thumbnail}
        thumbnailRatio={1}
        title={item.title}
        size={104}
        radius={radius as IconRadius}
        type={item.type}
      />
      <div className="w-26">
        <p className="line-clamp-2 h-10 break-words text-regular-13">{item.title}</p>
        {'price' in item && item.price && (
          <p className={clsx(roboto.className, 'text-right text-bold-18')}>
            ¥{item.price.toLocaleString()} <span className="text-regular-9">（税込）</span>
          </p>
        )}
      </div>
    </div>
  );
};

export default PreviewSquareItem;
