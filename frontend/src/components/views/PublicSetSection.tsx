'use client';

import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import RadioGroup from '@/components/atoms/radioGroup';
import RadioGroupItem from '@/components/atoms/radioGroup/radio-group-item';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';

type PublicSetSectionProps = {
  numbering: string;
};

const PublicSetSection = ({ numbering }: PublicSetSectionProps) => {
  const { exhibits, setAvailable } = useExhibitsStore();
  const params = useParams();
  const itemId = params.id as string;

  const available = exhibits.find((e) => e.itemId === itemId)?.available;

  const popupHeader = '公開設定';
  const popupContent = (
    <div className="flex items-center gap-1 bg-navy-50 py-5 pl-5">
      <ShopPublicImage src={'/images/public.webp'} width={111} height={123} alt="modal" />
    </div>
  );
  const popupFooter = (
    <div>
      公開すると、商品が出品されます。
      <br />
      一度出品した商品を取り下げたい場合は、非公開にしてください。
    </div>
  );
  const handleChange = (value: string) => {
    setAvailable(itemId, value === '1');
  };
  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };
  return (
    <section className="pb-7 pt-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithNumber title="公開設定" numbering={numbering} className="!mb-0" />
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>
        <RadioGroup name="available" defaultValue="1" direction="column" className="gap-y-7">
          <RadioGroupItem
            label="公開する"
            value="1"
            name="available"
            selectedValue={available ? '1' : '0'}
            onChange={handleChange}
          />
          <RadioGroupItem
            label="非公開にする"
            value="0"
            name="available"
            selectedValue={available ? '1' : '0'}
            onChange={handleChange}
          />
        </RadioGroup>
      </div>
    </section>
  );
};

export default PublicSetSection;
