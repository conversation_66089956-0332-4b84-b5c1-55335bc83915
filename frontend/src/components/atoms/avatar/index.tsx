'use client';

import React from 'react';
import clsx from 'clsx';
import Image from 'next/image';

type AvatarSize = number | 'small' | 'medium' | 'large';

type AvatarProps = {
  src: string;
  alt: string;
  size?: AvatarSize;
  className?: string;
  onClick?: () => void;
  borderColor?: string;
  borderWidth?: number;
};

const getSizeValue = (size: AvatarSize): number => {
  if (typeof size === 'number') return size;
  switch (size) {
    case 'small':
      return 32;
    case 'large':
      return 64;
    case 'medium':
    default:
      return 48;
  }
};

const Avatar = ({ src, alt, size = 40, className, onClick, borderColor, borderWidth = 2 }: AvatarProps) => {
  const sizeValue = getSizeValue(size);

  if (borderColor) {
    return (
      <div className={clsx('relative', className)} onClick={onClick}>
        <div
          className="rounded-full p-0.5"
          style={{
            width: sizeValue,
            height: sizeValue,
            borderColor: borderColor,
            backgroundColor: borderColor,
            border: `${borderWidth}px solid ${borderColor}`,
          }}
        >
          <div className="relative h-full w-full overflow-hidden rounded-full bg-gray-200">
            <Image src={src} alt={alt} fill className="object-cover" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={clsx('relative overflow-hidden rounded-full', className)}
      style={{ width: sizeValue, height: sizeValue }}
      onClick={onClick}
    >
      <Image src={src} alt={alt} fill className="object-cover" />
    </div>
  );
};

export default Avatar;
