'use client';
import clsx from 'clsx';
type StateBadgeProps = {
  type: 'round-filled' | 'square-lined' | 'half-round-filled' | 'square-radius-filled';
  size?: 'sm' | 'lg';
  color:
    | 'white'
    | 'orange'
    | 'green'
    | 'blue'
    | 'pink'
    | 'gray'
    | 'darkGray'
    | 'lined-green'
    | 'lined-orange'
    | 'lined-gray'
    | 'lined-blue'
    | 'lined-red';
  children: React.ReactNode;
  className?: string;
  position?: 'left' | 'right' | 'center';
  style?: React.CSSProperties;
};
type getSizeClass = (size: StateBadgeProps['size'], type: StateBadgeProps['type']) => string;

const colorClass = {
  white: 'bg-white text-black',
  orange: 'bg-orange-500 text-white',
  green: 'bg-green-300 text-white',
  blue: 'bg-blue-500 text-white',
  pink: 'bg-pink-100 text-white',
  gray: 'bg-gray-100 text-black',
  darkGray: 'bg-gray-400 text-white',
  'lined-green': 'bg-white border border-green-100 text-green-100',
  'lined-orange': 'bg-white border border-orange-500 text-orange-500',
  'lined-gray': 'bg-white border border-gray-500 text-gray-500',
  'lined-blue': 'bg-white border border-blue-500 text-blue-500',
  'lined-red': 'bg-white border border-red-500 text-red-500',
};

const getSizeClass: getSizeClass = (size: StateBadgeProps['size'], type: StateBadgeProps['type']) => {
  switch (size) {
    case 'sm':
      return type === 'round-filled'
        ? 'w-16'
        : type === 'square-lined'
          ? 'w-12'
          : type === 'square-radius-filled'
            ? 'w-20 h-6'
            : 'px-1';
    case 'lg':
      return type === 'round-filled' ? 'w-32.5' : 'px-1';
    default:
      return '';
  }
};
const StateBadge = ({ type, color, size, className, children, position, style }: StateBadgeProps) => {
  const sizeClass = getSizeClass(size, type);
  return (
    <div
      className={clsx(
        'flex h-5 items-center',
        colorClass[color],
        sizeClass,
        className,
        type === 'round-filled'
          ? 'rounded-full text-medium-9'
          : type === 'half-round-filled'
            ? 'h-8 w-24 rounded-r-full p-2 text-medium-13'
            : type === 'square-radius-filled'
              ? 'rounded text-bold-13'
              : 'justify-center text-medium-10',
        position === 'left' ? 'justify-start' : position === 'right' ? 'justify-end' : 'justify-center',
      )}
      style={style}
    >
      {children}
    </div>
  );
};

export default StateBadge;
