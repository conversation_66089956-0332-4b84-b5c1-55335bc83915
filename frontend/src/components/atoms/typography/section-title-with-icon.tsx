'use client';
import React from 'react';
import clsx from 'clsx';
import ShopPublicImage from '@/components/ShopImage';

type SectionTitleWithIconProps = {
  title: string;
  icon: string;
  className?: string;
};

const SectionTitleWithIcon = ({ title, icon, className }: SectionTitleWithIconProps) => {
  return (
    <h4 className={clsx('flex items-center justify-start gap-1.5 text-medium-15', className)}>
      <ShopPublicImage src={icon} alt="icon" width={20} height={20} />
      {title}
    </h4>
  );
};

export default SectionTitleWithIcon;
