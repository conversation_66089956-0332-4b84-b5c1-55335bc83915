'use client';
import React, { Suspense, useEffect, useMemo } from 'react';
import clsx from 'clsx';
import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import Tab from '@/components/atoms/tab';
import Tabs from '@/components/atoms/tab/tabs';
import ShopPublicImage from '@/components/ShopImage';
import { useCurrentUser } from '@/store/useCurrentUser';
import { useExhibitsStore } from '@/store/useExhibit';
import { useHeaderInfoStore } from '@/store/useHeaderInfo';
import { useIsEdit } from '@/store/useIsEdit';
import { useOrderFormStore } from '@/store/useOrderFormStore';
import { useSorting } from '@/store/useSorting';
import { PATH_PATTERNS } from '@/consts/router';
import { useGetUserCookieToken } from '@/hooks/swr/useGetUser';
import { useHeaderState } from '@/hooks/useHeaderState';
import { useRouteHistory } from '@/hooks/useRouteHistory';
import { userServices } from '@/services/user';
import { getUserIdentityId } from '@/utils/base';
import { PageType } from '@/types/common';
import { ITEM_TYPE } from '@/types/item';
import { ExhibitType } from '@/types/shopItem';

const Header: React.FC<{ title?: string }> = ({ title }) => {
  const { contents, rightContents, setHeaderInfo } = useHeaderInfoStore();
  const { currentPath, shouldHideHeader, shouldHaveBlackBackground } = useHeaderState();
  const { clearAllState } = useOrderFormStore();
  const { reset, isConfirmed } = useExhibitsStore();
  const { currentUser, setCurrentUser, isOwner, setIsOwner } = useCurrentUser();
  const pathname = usePathname();
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathIdentity = getUserIdentityId(pathname);
  const { setIsEdit, isEdit } = useIsEdit();
  const { sorting } = useSorting();
  const { getPreviousPath } = useRouteHistory();
  const itemType = searchParams.get('item_type') as ExhibitType;

  // TODO 通信内容が見えてしまうが一旦こうするしかない
  const { data } = useGetUserCookieToken();
  const token = data?.data;
  const params = useParams();
  const identityId = getUserIdentityId(params.identityId as string);
  const itemId = currentPath === PageType.GACHA_ORDER ? (searchParams.get('itemId') ?? '') : (params.id as string);

  const [ownerHeaderValue, setOwnerHeaderValue] = React.useState(0);

  useEffect(() => {
    (async () => {
      if (!token) return;
      const user = await userServices.getCurrentUser(token);
      if (user) setCurrentUser(user);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, token]);

  useEffect(() => {
    if (['/create', '/'].includes(pathname)) {
    }
  }, [pathname]);

  useEffect(() => {
    setIsOwner(pathIdentity);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathIdentity]);

  const handleOwnerHeaderTab = (value: number) => {
    const decodedId = decodeURIComponent(params.identityId as string);
    setOwnerHeaderValue(value);
    if (value === 0) {
      // editModeがあったら消す
      const path = window.location.search.replace(/(\??)editMode=true(&?)/gm, '$1');
      window.history.replaceState({}, '', `/shop/${decodedId}${path}`);
      setIsEdit(false);
    } else {
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.set('editMode', 'true');
      window.history.replaceState({}, '', `/shop/${decodedId}?${searchParams.toString()}`);
      setIsEdit(true);
    }
  };
  useEffect(() => {
    if (isEdit) {
      setOwnerHeaderValue(1);
    } else {
      setOwnerHeaderValue(0);
    }
  }, [isEdit]);

  const moveToFanmeLinkMyPage = () => {
    if (!currentUser?.accountIdentity) {
      console.error('No accountIdentity available:', currentUser);
      return;
    }
    window.location.href = `${process.env.NEXT_PUBLIC_FANME_LINK_URL}/@${currentUser.accountIdentity}`;
  };

  const BackWithHome = () => (
    <span onClick={moveToFanmeLinkMyPage} className="flex cursor-pointer items-center">
      <ShopPublicImage src="/images/icons/Arrow_Back.svg" width={16} height={16} alt="back" />
      <ShopPublicImage src="/images/icons/Home.svg" width={24} height={24} alt="home" />
    </span>
  );

  const leftContents = useMemo(() => {
    if (currentPath === PageType.CREATE_SHOP) {
      return <BackWithHome />;
    } else if (currentPath === PageType.CREATE_ITEM || currentPath === PageType.EDIT_ITEM) {
      const handleBack = () => {
        reset(itemId);
        router.back();
      };
      return (
        <span onClick={handleBack}>
          <ShopPublicImage src="/images/icons/Arrow_Back.svg" width={22} height={22} alt="back" />
        </span>
      );
    } else if (currentPath === PageType.ITEM_LIST || currentPath === PageType.TOKUSHOHO) {
      return <></>;
    } else if (currentPath === PageType.CART) {
      const handleOnClick = () => {
        router.push(`/@${identityId}`);
      };

      return (
        <span onClick={handleOnClick} className="flex cursor-pointer items-center">
          <ShopPublicImage src="/images/icons/Arrow_Back.svg" width={16} height={16} alt="back" />
          <div className="flex flex-col items-center">
            <ShopPublicImage src="/images/icons/Bag.svg" width={16} height={16} alt="bag" />
            <span className="text-regular-8 text-black">TOP</span>
          </div>
        </span>
      );
    } else if (currentPath === PageType.ITEM_DETAIL) {
      const handleOnClick = () => {
        router.push(`/@${identityId}`);
      };

      return (
        <span onClick={handleOnClick} className="flex cursor-pointer items-center">
          <ShopPublicImage src="/images/icons/Arrow_Back.svg" width={16} height={16} alt="back" />
        </span>
      );
    } else if (currentPath === PageType.ORDER) {
      const handleOnClick = () => {
        clearAllState();
        router.push(`/@${identityId}/cart`);
      };

      return (
        <Button
          onClick={() => handleOnClick()}
          buttonType="light"
          buttonSize="free"
          className="flex h-full items-center justify-center text-regular-12"
          buttonClassNames="w-22 h-6"
        >
          キャンセル
        </Button>
      );
    } else if (currentPath === PageType.GACHA_ORDER) {
      const handleOnClick = () => {
        clearAllState();
        router.push(`/@${identityId}/item/${itemId}`);
      };
      return (
        <Button
          onClick={() => handleOnClick()}
          buttonType="light"
          buttonSize="free"
          className="flex h-full items-center justify-center text-regular-12"
          buttonClassNames="w-22 h-6"
        >
          キャンセル
        </Button>
      );
    } else if ([PageType.CREDIT_CARD, PageType.EDIT_ADDRESS].includes(currentPath)) {
      return (
        <span onClick={() => router.back()}>
          <ShopPublicImage src="/images/icons/Arrow_Back_White.svg" width={22} height={22} alt="back" />
        </span>
      );
    } else if (currentPath === PageType.PREVIEW_ITEM) {
      return <></>;
    } else if (currentPath === PageType.ITEM_VIEWER) {
      const handleBack = () => {
        const previousPath = getPreviousPath();
        if (
          previousPath &&
          (PATH_PATTERNS.ITEM_LIST.test(previousPath) || PATH_PATTERNS.ITEM_DETAIL.test(previousPath))
        ) {
          router.back();
        } else {
          window.location.href = `${process.env.NEXT_PUBLIC_FANME_LINK_URL}/mylibrary`;
        }
      };

      return (
        <span onClick={() => handleBack()}>
          <ShopPublicImage src="/images/icons/Arrow_Back.svg" width={22} height={22} alt="back" />
        </span>
      );
    } else {
      return (
        <span onClick={() => router.back()}>
          <ShopPublicImage src="/images/icons/Arrow_Back.svg" width={22} height={22} alt="back" />
        </span>
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPath, currentUser]);

  const centerText = useMemo(() => {
    if (currentPath === PageType.CREATE_ITEM) return <span className="text-bold-17">出品</span>;
    if (currentPath === PageType.CONFIRM_ITEM) return <span className="text-bold-17">確認</span>;
    if (currentPath === PageType.PREVIEW_ITEM) return <span className="text-bold-17">プレビュー</span>;
    if (currentPath === PageType.EDIT_ITEM) return <span className="text-bold-17">商品編集</span>;
    if (currentPath === PageType.ITEM_DETAIL) return <span className="text-bold-17">商品詳細</span>;
    if (currentPath === PageType.ITEM_VIEWER) {
      const displayTitle = title && title.length > 10 ? `${title.substring(0, 10)}...` : title || '--';
      return <span className="text-bold-17">{displayTitle}</span>;
    }
    if (currentPath === PageType.PURCHASED_ITEM) return <span className="text-bold-17">購入詳細</span>;
    if (currentPath === PageType.CREATE_SHOP) return <span className="text-bold-17">ショップ開設</span>;
    if (currentPath === PageType.EDIT_SHOP) return <span className="text-bold-17">編集</span>;
    if (currentPath === PageType.CONVENIENCE_STORE) return <span className="text-bold-17">コンビニ決済</span>;
    if (currentPath === PageType.CREDIT_CARD) return <span className="text-bold-17">カード情報</span>;
    if (currentPath === PageType.CART) return <span className="text-bold-17">カート</span>;
    if (currentPath === PageType.COMPLETE_RANKING) return <span className="text-bold-17">ランキング</span>;
    if (currentPath === PageType.ORDER) return <span className="text-bold-17 text-white">注文する</span>;
    if (currentPath === PageType.GACHA_ORDER) return <span className="text-bold-17 text-white">注文する</span>;
    if (currentPath === PageType.EDIT_ADDRESS) return <span className="text-bold-17 text-white">配送先</span>;
    if (currentPath === PageType.ITEM_LIST && isOwner) {
      return (
        <Tabs value={ownerHeaderValue} onChange={handleOwnerHeaderTab} type="slide" disable={sorting}>
          <Tab isActive={ownerHeaderValue === 0}>
            <>
              <ShopPublicImage src="/images/icons/PassDisplay.svg" alt="confirm" width={20} height={20} />
              <span className="ml-1.25">確認</span>
            </>
          </Tab>
          <Tab isActive={ownerHeaderValue === 1}>
            <>
              <ShopPublicImage src="/images/icons/PageEdit.svg" alt="edit" width={14} height={14} />
              <span className="ml-1.25">作成</span>
            </>
          </Tab>
        </Tabs>
      );
    }
    if (currentPath === PageType.TOKUSHOHO) {
      return <span className="text-bold-17">特定商取引法に基づく表記</span>;
    }
    return '';
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPath, ownerHeaderValue, isOwner, title, isEdit, sorting]);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    if (currentPath === PageType.ITEM_LIST && isOwner) {
      const isEditMode = searchParams.get('editMode');
      if (isEditMode) {
        handleOwnerHeaderTab(1);
      } else {
        handleOwnerHeaderTab(0);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPath, isOwner]);

  useEffect(() => {
    (() => {
      if ([PageType.CREATE_ITEM, PageType.EDIT_ITEM].includes(currentPath)) {
        const goToPreview = () => {
          const basePath = `/${identityId}/item/${itemId}/preview`;

          if (!isConfirmed) return;
          if (itemType === ITEM_TYPE.DIGITAL_GACHA.str) {
            router.push(`${basePath}?item_type=${ITEM_TYPE.DIGITAL_GACHA.str}`);
          } else if (itemType === ITEM_TYPE.CHEKI.str) {
            router.push(`${basePath}?item_type=${ITEM_TYPE.CHEKI.str}`);
          } else {
            router.push(basePath);
          }
        };
        const reviewButton = (
          <OutlinedButton
            buttonColor={isConfirmed ? 'black' : 'gray'}
            buttonShape="oval"
            buttonType="preview"
            disabled={!isConfirmed}
            onClick={goToPreview}
          />
        );
        setHeaderInfo(leftContents, centerText, reviewButton);
      }
      if (
        [
          PageType.CONFIRM_ITEM,
          PageType.ITEM_DETAIL,
          PageType.ITEM_VIEWER,
          PageType.PURCHASED_ITEM,
          PageType.CONVENIENCE_STORE,
          PageType.CREDIT_CARD,
          PageType.CART,
          PageType.ORDER,
          PageType.GACHA_ORDER,
          PageType.CREATE_SHOP,
          PageType.EDIT_SHOP,
          PageType.EDIT_ADDRESS,
          PageType.COMPLETE_RANKING,
        ].includes(currentPath)
      ) {
        setHeaderInfo(leftContents, centerText, '');
        return;
      }
      if (currentPath === PageType.ORDER_SUCCESS) {
        clearAllState();
      }
      if (currentPath === PageType.PREVIEW_ITEM) {
        const closeButton = (
          <OutlinedButton
            buttonColor="black"
            buttonShape="oval"
            buttonType="close"
            onClick={() => {
              router.back();
            }}
          />
        );
        setHeaderInfo(leftContents, centerText, closeButton);
        return;
      }
      if (currentPath === PageType.TOKUSHOHO) {
        setHeaderInfo(leftContents, centerText, '');
      }
      if (currentPath === PageType.ITEM_LIST && isOwner) {
        setHeaderInfo(leftContents, centerText, '');
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPath, centerText, leftContents, isConfirmed]);

  if (shouldHideHeader && !(currentPath === PageType.ITEM_LIST && isOwner)) {
    return <></>;
  }

  return (
    <div
      className={clsx(
        'fixed top-0 z-50 mb-12 grid h-12 w-full max-w-120 grid-cols-10 items-center justify-between px-2 shadow-header-shadow',
        shouldHaveBlackBackground ? 'bg-black text-white' : 'bg-white',
      )}
    >
      <div className="col-span-2 flex items-center justify-start">{leftContents}</div>
      <div className="col-span-6 flex items-center justify-center">{contents}</div>
      <div className="col-span-2 flex items-center justify-end">{rightContents}</div>
    </div>
  );
};

const HeaderLayout: React.FC<{ title?: string }> = ({ title }) => {
  const { currentPath, shouldHideHeader } = useHeaderState();
  const { isOwner } = useCurrentUser();

  return (
    <Suspense fallback={<div>Loading user info...</div>}>
      <Header title={title} />
      {!(shouldHideHeader && !(currentPath === PageType.ITEM_LIST && isOwner)) && !title && <div className="h-12" />}
    </Suspense>
  );
};

export default HeaderLayout;
