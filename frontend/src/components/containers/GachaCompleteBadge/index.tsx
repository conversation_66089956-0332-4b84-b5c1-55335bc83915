import clsx from 'clsx';
import Image from 'next/image';
import ShopPublicImage from '@/components/ShopImage';

type GachaCompleteBadgeProps = {
  creatorAvatar?: string;
  completeRank: number;
};
const GachaCompleteBadge = ({ creatorAvatar, completeRank }: GachaCompleteBadgeProps) => {
  return (
    <div className="flex flex-col items-center justify-center gap-5">
      <div className="relative h-17 w-86">
        {creatorAvatar && (
          <div className="absolute left-1.5 top-[4.5px] h-[58.97px] overflow-hidden rounded-full">
            <Image
              src={creatorAvatar}
              alt="Creator Avatar"
              className="h-full object-cover"
              width={58.97}
              height={58.97}
            />
          </div>
        )}
        <ShopPublicImage src="/images/gacha/badge.svg" alt="Gacha badge" width={344} height={68} />
        {!!completeRank && (
          <div className={clsx('absolute text-white', completeRank > 100 ? 'right-6 top-5.5' : 'right-7 top-2')}>
            {completeRank > 100 ? (
              <span className="text-bold-16">ランク圏外</span>
            ) : (
              <>
                <span className="text-bold-32">{completeRank}</span>
                <span className="text-bold-16">位</span>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default GachaCompleteBadge;
