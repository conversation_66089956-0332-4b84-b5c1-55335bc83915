'use client';

import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'next/navigation';
import DateInput from '@/components/atoms/inputs/date-input';
import NumberInput from '@/components/atoms/inputs/number-input';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { ITEM_MAX_QUANTITY_PER_USER } from '@/consts/inputLength';
import { formatDateTime } from '@/utils/base';
import { isPhysicalItem } from '@/utils/itemTypes';

type PhysicalItemOptionProps = {
  numbering: string;
};

const PhysicalItemOptionSection = ({ numbering }: PhysicalItemOptionProps) => {
  const params = useParams();
  const itemId = params.id as string;
  const useExhibits = useExhibitsStore();
  const { exhibits, setPeriod, validatePeriodStart, validatePeriodEnd, setLimitedPerUser, validateLimitedPerUser } =
    useExhibits;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const limitedPerUser = !!exhibitItem && isPhysicalItem(exhibitItem) ? exhibitItem.limitedPerUser : undefined;
  const { period } = exhibitItem ?? {};
  const [minEndDate, setMinEndDate] = useState<string>('');

  const { control, watch, setValue } = useForm<{
    limitedPerUser: number;
    purchaserComment: string;
  }>({
    mode: 'onBlur',
    defaultValues: {
      limitedPerUser: limitedPerUser,
    },
  });

  // ページ読み込み時に、limitedPerUserの値をセット
  useEffect(() => {
    if (limitedPerUser !== undefined) {
      setValue('limitedPerUser', limitedPerUser);
    }
  }, [limitedPerUser, setValue]);

  useEffect(() => {
    const limitedPerUserValue = watch('limitedPerUser');
    if (limitedPerUserValue !== undefined) {
      setLimitedPerUser(itemId, Math.max(Number(limitedPerUserValue), 0));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [itemId, setLimitedPerUser, watch('limitedPerUser')]);

  const setStartDate = (date?: Date) => {
    setPeriod(itemId, { ...exhibitItem?.period, start: date });
  };

  const setEndDate = (date?: Date) => {
    setPeriod(itemId, { ...exhibitItem?.period, end: date });
  };

  const validateDate = (data?: Date) => {
    if (!data) return;
    setMinEndDate(formatDateTime(data));
  };

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <SectionTitleWithNumber title="購入設定" numbering={numbering} />
        <div className="flex flex-col gap-6 px-2">
          <div>
            {/*販売期間設定*/}
            <div className="flex items-center pb-4">
              <p className="flex items-center gap-x-2 text-regular-16">
                <ShopPublicImage src="/images/icons/Calendar.svg" alt="calendar" width={20} height={20} />
                販売期間
              </p>
              <span className="ml-0.5 text-regular-11 text-orange-200">*必須</span>
            </div>
            {/*TODO: ガチャ、チェキリリース後、ItemOptionSectionのものと共通化*/}
            <DateInput
              name="saleStartDate"
              label="販売開始"
              className="mb-4"
              min={formatDateTime()}
              max={undefined}
              errorMsg="販売開始日は現在より後の日付を指定してください"
              customError={!validatePeriodStart(itemId)}
              setTime={setStartDate}
              onChange={validateDate}
              errorClassName="col-start-1 col-end-4"
              defaultData={period?.start?.toLocaleString()}
            />
            <DateInput
              name="saleEndDate"
              label="販売終了"
              errorClassName="col-start-1 col-end-4"
              min={period?.start ? minEndDate : ''}
              max={undefined}
              errorMsg={
                period?.start
                  ? '販売終了日は販売開始日より後の日付を指定してください'
                  : '販売終了日は現在より後の日付を指定してください'
              }
              setTime={setEndDate}
              customError={!validatePeriodEnd(itemId)}
              defaultData={period?.end?.toLocaleString()}
            />
            <p className="pt-4 text-regular-11 text-gray-500">
              *販売開始日を未来の日付にする事で [予約販売] が出来ます
            </p>
          </div>

          {/*一人当たりの購入上限設定*/}
          <div className="flex flex-col">
            <div className="flex flex-row items-center">
              <p className="text-regular-16">最大購入数（1人当たり）</p>
              <span className="ml-0.5 text-regular-11 text-orange-200">*必須</span>
            </div>
            <div className="flex flex-col gap-2 pt-4">
              <NumberInput
                suffix="個"
                control={control}
                inputName="limitedPerUser"
                min={0}
                max={ITEM_MAX_QUANTITY_PER_USER}
                defaultValue={limitedPerUser}
                errorMsg={`最大購入数は${ITEM_MAX_QUANTITY_PER_USER.toLocaleString()}個以下で指定してください`}
                inputClassName="min-w-0 w-18 text-bold-22 bg-gray-50"
                className="!mb-0 mr-auto w-24 items-center"
                error={!validateLimitedPerUser(itemId)}
                coverValue={limitedPerUser}
                shortInput={true}
              />
              <p className="pt-2 text-regular-11 text-gray-500">
                *購入者は同じ商品を複数購入できます。制限を設定したい場合は、上限数を入力してください。
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PhysicalItemOptionSection;
