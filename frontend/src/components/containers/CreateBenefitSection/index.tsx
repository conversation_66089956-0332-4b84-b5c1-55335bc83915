import React, { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import TextAreaInput from '@/components/atoms/inputs/text-area-input';
import SectionInstruction from '@/components/atoms/sectionInstruction';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import UploadGroup from '../UploadGroup';
import type { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { BENEFITS_MESSAGE_MAX_LENGTH, MAX_UPLOAD_SET_ITEMS } from '@/consts/inputLength';
import type { DigitalBundle, ExhibitChekiItem } from '@/types/exhibitItem';
import type { Benefit, SingleItem } from '@/types/shopItem';

type BenefitSectionProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
};

const CreateBenefitSection = ({ numbering, shopLimitation }: BenefitSectionProps) => {
  const params = useParams();
  const itemId = params.id as string;
  const useExhibits = useExhibitsStore();
  const { exhibits, setBenefits } = useExhibits;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const benefits = useMemo(() => (exhibitItem as DigitalBundle | ExhibitChekiItem).benefits || [], [exhibitItem]);
  const firstBenefit = useMemo(
    () =>
      Array.isArray(benefits) && benefits.length > 0
        ? benefits[0]
        : { description: '', benefitFiles: [], conditionType: 0 },
    [benefits],
  );

  const [benefitsItems, setBenefitsItems] = React.useState<SingleItem[]>([]);
  const [benefitsMessageLen, setBenefitsMessageLen] = React.useState(firstBenefit?.description?.length || 0);

  const popupHeader = '購入特典について';
  const popupContent = (
    <div className="flex items-center justify-center gap-1 bg-navy-50 py-4">
      <ShopPublicImage src={'/images/tokuten.webp'} width={200} height={186} alt="modal" />
    </div>
  );
  const popupFooter = (
    <div>
      <p className="mb-4">購入特典は、セット商品を購入いただいた場合のみ受け取ることができる設定です。</p>
      <p>「単品」を購入しても特典は受け取れません。</p>
    </div>
  );

  const { control, reset, watch } = useForm<{ message: string }>({
    mode: 'onBlur',
    defaultValues: {
      message: firstBenefit?.description || '',
    },
  });

  const messageValue = watch('message');
  useEffect(() => {
    setBenefitsMessageLen(messageValue?.length || 0);
  }, [messageValue]);

  useEffect(() => {
    if (firstBenefit?.description !== undefined) {
      reset({ message: firstBenefit.description });
      setBenefitsMessageLen(firstBenefit.description.length || 0);
    }
  }, [firstBenefit, reset]);

  useEffect(() => {
    if (firstBenefit && firstBenefit.benefitFiles?.length) {
      setBenefitsItems(firstBenefit.benefitFiles);
    }
  }, [firstBenefit]);

  const handleSetBenefitsItems = (items: SingleItem[]) => {
    // Create a new benefit object with the updated files
    const newBenefit: Benefit = {
      description: firstBenefit?.description || '',
      benefitFiles: items,
      conditionType: 0,
    };
    // Create an array with the new benefit
    const newBenefits: Benefit[] = [newBenefit];
    setBenefits(itemId, newBenefits);
    setBenefitsItems(items);
  };

  const onBlurMessage = (value?: string) => {
    // Only update if we have benefit files
    if (benefitsItems.length > 0) {
      if (setBenefits) {
        const newBenefit: Benefit = {
          description: value || '',
          benefitFiles: benefitsItems,
          conditionType: 0,
        };
        // Create an array with the new benefit
        const newBenefits: Benefit[] = [newBenefit];
        setBenefits(itemId, newBenefits);
      }
    }
  };

  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(popupHeader, popupContent, popupFooter);
  };

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithNumber title="購入特典を設定" numbering={numbering} className="!mb-0" />
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>
        <SectionInstruction
          title={
            <>
              最大{MAX_UPLOAD_SET_ITEMS}点まで購入特典を登録できます。
              <br />
              購入特典はセット商品のみ付けられます。
            </>
          }
          className="mb-4"
        />

        <UploadGroup
          maxUpload={MAX_UPLOAD_SET_ITEMS}
          uploads={benefitsItems}
          setUploads={handleSetBenefitsItems}
          shopLimitation={shopLimitation}
          uploadType="benefit"
        />
        <TextAreaInput
          control={control}
          labelTitle="特典メッセージ"
          inputName="message"
          maxLength={BENEFITS_MESSAGE_MAX_LENGTH}
          inputClassName="text-regular-13 !min-h-18"
          textLength={benefitsMessageLen}
          onBlur={onBlurMessage}
          disabled={benefitsItems.length === 0}
          className="!mb-0 mt-4"
        />
      </div>
    </section>
  );
};

export default CreateBenefitSection;
