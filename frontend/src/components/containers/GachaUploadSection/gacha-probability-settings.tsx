'use client';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import clsx from 'clsx';
import { useParams } from 'next/navigation';
import FloatButton from '@/components/atoms/button/float-button';
import CustomToast from '@/components/atoms/toast/custom-toast';
import WarningMessage from '@/components/atoms/typography/warning-message';
import SectionTitleWithIcon from '@/components/atoms/typography/section-title-with-icon';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import ProbabilityInputItem from './probability-input-item';
import {
  PROBABILITY_PATTERNS,
  type ProbabilityPatternKey,
  mapProbabilitiesToFormValues,
  EMPTY_PROBABILITY_PATTERNS,
  GACHA_MIN_PROBABILITY,
  GACHA_PATTERN_KEYS,
} from '@/consts/gacha-data';
import { LEAST_ITEM_COUNT } from '@/consts/sizes';
import { useGroupGachaItems } from '@/hooks/useGroupGachaItems';
import { deepClone } from '@/utils/base';
import { getAwardsInPattern, getCurrentPatternKey, validateProbabilityHierarchy } from '@/utils/gacha-helpers';
import type { ExhibitGachaItem } from '@/types/exhibitItem';
import type { Award, AwardProbability } from '@/types/gacha';
import { AWARD_TYPE, MAX_AWARD_COUNT } from '@/types/gacha';
import type { ProbabilityFormValues } from './probability-input-item';

type GachaProbabilitySettingsProps = {
  _totalGachaCountSectionRef?: React.RefObject<HTMLDivElement>;
  isEdit?: boolean;
  isDuplicated: boolean;
};

const GachaProbabilitySettings = ({ isEdit, isDuplicated }: GachaProbabilitySettingsProps) => {
  const params = useParams();
  const itemId = params.id as string;
  const { exhibits, setAwardProbability, setIsAwardProbabilitiesValid, setHasEnoughItems } = useExhibitsStore();

  const exhibitItem = exhibits.find((e) => e.itemId === itemId) as ExhibitGachaItem;
  const tempItemFiles = exhibitItem?.itemFiles || [];
  const { awardProbabilities } = exhibitItem || {};
  // 現在の確率設定が有効かどうか
  const isAwardProbabilitiesValid = exhibitItem?.isAwardProbabilitiesValid ?? false;

  // アイテムがない場合の早期リターンを削除
  const groupedItems = useGroupGachaItems(tempItemFiles);

  const hasItems = useMemo(
    () => ({
      [AWARD_TYPE.S]: groupedItems[0].length > 0,
      [AWARD_TYPE.A]: groupedItems[1].length > 0,
      [AWARD_TYPE.B]: groupedItems[2].length > 0,
      [AWARD_TYPE.C]: groupedItems[3].length > 0,
    }),
    [groupedItems],
  );

  const activeAwardCount = useMemo(() => Object.values(hasItems).filter(Boolean).length, [hasItems]);

  // 全パターンの確率を管理する中心的な状態
  const [temporaryProbabilitiesByPattern, setTemporaryProbabilitiesByPattern] = useState<
    Record<ProbabilityPatternKey, Record<Award, number>>
  >(() => deepClone(PROBABILITY_PATTERNS));

  // 表示用と入力フォーム用の現在の確率
  const [temporaryProbabilities, setTemporaryProbabilities] = useState<Record<Award, number>>(() => {
    const currentKey = getCurrentPatternKey(hasItems);
    // アイテムがない場合、空のデフォルト値を使用
    if (currentKey === GACHA_PATTERN_KEYS.EMPTY) {
      return EMPTY_PROBABILITY_PATTERNS;
    }
    // パターンの確率値を直接参照（コピーではなく）
    return temporaryProbabilitiesByPattern[currentKey];
  });

  const isSOnly = useMemo(() => getCurrentPatternKey(hasItems) === GACHA_PATTERN_KEYS.PATTERN_1, [hasItems]);

  const { control, reset } = useForm<ProbabilityFormValues>({
    defaultValues: mapProbabilitiesToFormValues(temporaryProbabilities),
  });

  const initialCalculationRef = useRef(false);
  const userInputRef = useRef<Partial<Record<Award, boolean>>>({});
  const prevActiveAwardsRef = useRef<Award[]>([]);
  // 前回のパターンキーを保存
  const prevPatternKeyRef = useRef<ProbabilityPatternKey | 'empty'>(GACHA_PATTERN_KEYS.EMPTY);

  // パターンまたは中心的な確率が変更されたときに表示用確率を更新
  useEffect(() => {
    if (!initialCalculationRef.current && Object.keys(temporaryProbabilitiesByPattern).length > 0) {
      // 現在のアクティブなパターンに基づく初期設定
      const currentKey = getCurrentPatternKey(hasItems);
      // アイテムがない場合、空のデフォルト値を使用
      if (currentKey === GACHA_PATTERN_KEYS.EMPTY) {
        setTemporaryProbabilities(EMPTY_PROBABILITY_PATTERNS);
        reset(mapProbabilitiesToFormValues(EMPTY_PROBABILITY_PATTERNS));
        initialCalculationRef.current = true;
        return;
      }

      const initialProbsForDisplay = { ...temporaryProbabilitiesByPattern[currentKey] };

      // アイテムがない賞の確率を0に設定
      const awardsInCurrentPattern = getAwardsInPattern(currentKey);
      [AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C].forEach((award) => {
        if (awardsInCurrentPattern.includes(award) && !hasItems[award]) {
          initialProbsForDisplay[award] = 0;
        }
      });

      setTemporaryProbabilities(initialProbsForDisplay);
      reset(mapProbabilitiesToFormValues(initialProbsForDisplay));
      initialCalculationRef.current = true;
      prevPatternKeyRef.current = currentKey;
      return;
    }

    if (initialCalculationRef.current) {
      const newCurrentPatternKey = getCurrentPatternKey(hasItems);
      const prevPatternKey = prevPatternKeyRef.current;

      // パターンが変わった場合のみ更新を行う
      if (newCurrentPatternKey !== prevPatternKey) {
        prevPatternKeyRef.current = newCurrentPatternKey;

        // アイテムがない場合の処理
        if (newCurrentPatternKey === GACHA_PATTERN_KEYS.EMPTY) {
          setTemporaryProbabilities(EMPTY_PROBABILITY_PATTERNS);
          reset(mapProbabilitiesToFormValues(EMPTY_PROBABILITY_PATTERNS));
          return;
        }
        // 変更された値をすべて保持するために現在のパターンの確率を直接使用
        const newProbsToDisplay = { ...temporaryProbabilitiesByPattern[newCurrentPatternKey] };

        // 必要に応じて値を調整（例：このパターンに含まれていない賞を0に設定）
        const awardsDefinedInPattern = getAwardsInPattern(newCurrentPatternKey);
        [AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C].forEach((award) => {
          if (!awardsDefinedInPattern.includes(award)) {
            newProbsToDisplay[award] = 0;
          }
          // アイテムがない賞の確率を0に設定
          if (awardsDefinedInPattern.includes(award) && !hasItems[award]) {
            newProbsToDisplay[award] = 0;
          }
        });

        setTemporaryProbabilities(newProbsToDisplay);
        const awardProbabilitiesToSave: AwardProbability[] = Object.entries(newProbsToDisplay).map(
          ([awardType, probability]) => ({
            awardType: Number(awardType) as Award,
            probability: Number(probability) || 0,
          }),
        );
        setAwardProbability(itemId, awardProbabilitiesToSave);
        reset(mapProbabilitiesToFormValues(newProbsToDisplay));
      }
    }
  }, [hasItems, temporaryProbabilitiesByPattern, reset, itemId, setAwardProbability]);

  const lastActiveAward = useMemo(() => {
    const currentKey = getCurrentPatternKey(hasItems);

    // アイテムがない場合、最後のアクティブな賞はなし
    if (currentKey === GACHA_PATTERN_KEYS.EMPTY) return undefined;

    const awardsInCurrentPattern = getAwardsInPattern(currentKey);
    // 現在のパターン定義内でアクティブでアイテムも持つ、最も低いランクの賞を探す
    for (let i = awardsInCurrentPattern.length - 1; i >= 0; i--) {
      const award = awardsInCurrentPattern[i];
      if (hasItems[award]) {
        return award;
      }
    }
    // 定義された賞にアイテムがない場合、またはパターン定義が最小（例：sOnly）の場合のフォールバック
    return awardsInCurrentPattern.length > 0 ? awardsInCurrentPattern[awardsInCurrentPattern.length - 1] : undefined;
  }, [hasItems]);

  useEffect(() => {
    if (!exhibitItem) return;
    if (activeAwardCount > 0 && activeAwardCount <= MAX_AWARD_COUNT) {
      const count = activeAwardCount as keyof typeof LEAST_ITEM_COUNT;
      setHasEnoughItems(itemId, count);
    }
  }, [itemId, activeAwardCount, exhibitItem, setHasEnoughItems]);

  // 新しい賞が追加されたときを検出するuseEffect
  useEffect(() => {
    if (!initialCalculationRef.current) return;
    const currentKey = getCurrentPatternKey(hasItems);
    // アイテムがない場合はエラー設定をスキップ
    if (currentKey === GACHA_PATTERN_KEYS.EMPTY) return;
    const currentActiveAwards = getAwardsInPattern(currentKey).filter((award) => hasItems[award]);

    // 次の比較のために現在のアクティブな賞でrefを更新
    prevActiveAwardsRef.current = [...currentActiveAwards];
  }, [hasItems]);

  const totalProbability = useMemo(() => {
    const currentKey = getCurrentPatternKey(hasItems);
    if (currentKey === GACHA_PATTERN_KEYS.EMPTY) return 0;

    const awardsInPattern = getAwardsInPattern(currentKey);
    return awardsInPattern.reduce((sum, award) => {
      return hasItems[award] ? sum + (temporaryProbabilities[award] || 0) : sum;
    }, 0);
  }, [temporaryProbabilities, hasItems]);

  // すべての確率検証条件を一箇所に集約したuseEffect
  useEffect(() => {
    // 1. アイテムがない場合またはパターンがEMPTYの場合はtrue
    if (activeAwardCount === 0 || getCurrentPatternKey(hasItems) === GACHA_PATTERN_KEYS.EMPTY) {
      setIsAwardProbabilitiesValid(itemId, true);
      return;
    }

    // 2. 重複が許可されていない場合はtrue
    if (!isDuplicated) {
      setIsAwardProbabilitiesValid(itemId, true);
      return;
    }

    // 3. その他の場合は各種条件を検証
    const currentKey = getCurrentPatternKey(hasItems);
    const awardsInCurrentPattern = currentKey === GACHA_PATTERN_KEYS.EMPTY ? [] : getAwardsInPattern(currentKey);

    // 3.1 アイテムがあるのに確率が0の賞があるかチェック
    const hasZeroProbabilityError = awardsInCurrentPattern.some(
      (award) => hasItems[award] && temporaryProbabilities[award] === 0,
    );

    // 3.2 確率合計が100でない場合もエラー
    const totalIsNot100 = totalProbability !== 100;

    // 3.3 確率階層チェック: C >= B >= A >= S >= 5（最小確率チェックも含む）
    const hierarchyValidation = validateProbabilityHierarchy(temporaryProbabilities, hasItems);

    // すべての条件をチェックして有効性を決定
    const isValid = !(totalIsNot100 || hasZeroProbabilityError || !hierarchyValidation.isValid);
    setIsAwardProbabilitiesValid(itemId, isValid);
  }, [
    temporaryProbabilities,
    activeAwardCount,
    hasItems,
    itemId,
    setIsAwardProbabilitiesValid,
    totalProbability,
    isDuplicated,
  ]);

  const handleProbabilityChange = (changedAward: Award, newValue: number) => {
    const currentPatternKey = getCurrentPatternKey(hasItems);
    // アイテムがない場合はスキップ
    if (currentPatternKey === GACHA_PATTERN_KEYS.EMPTY) return;

    const newGlobalProbs = deepClone(temporaryProbabilitiesByPattern);

    // 1. 現在のパターンのスライスを更新
    const currentSlice = { ...newGlobalProbs[currentPatternKey] };
    currentSlice[changedAward] = newValue;

    const awardsInCurrentPatternDef = getAwardsInPattern(currentPatternKey); // このパターンタイプで定義されている賞
    const actualLastActiveInCurrentPattern =
      awardsInCurrentPatternDef.filter((a) => hasItems[a]).sort((a, b) => a - b)[0] ||
      awardsInCurrentPatternDef[awardsInCurrentPatternDef.length - 1];

    let sumExcludingLastInCurrent = 0;
    awardsInCurrentPatternDef.forEach((award) => {
      if (award !== actualLastActiveInCurrentPattern) {
        sumExcludingLastInCurrent += currentSlice[award] || 0;
      }
    });
    if (actualLastActiveInCurrentPattern) {
      currentSlice[actualLastActiveInCurrentPattern] = Math.max(0, 100 - sumExcludingLastInCurrent);
    }

    // このパターンタイプで定義されている賞のみに値があり、他は0であることを確認
    [AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C].forEach((award) => {
      if (!awardsInCurrentPatternDef.includes(award)) {
        currentSlice[award] = 0;
      }
      // アイテムがない賞は確率を0に設定
      if (awardsInCurrentPatternDef.includes(award) && !hasItems[award]) {
        currentSlice[award] = 0;
      }
    });
    newGlobalProbs[currentPatternKey] = currentSlice;

    // 2. 変更を他のパターンに伝播
    const propagationValues: Partial<Record<Award, number>> = {};
    // 現在のパターンから直接変更された値と自動計算されたパートナーを伝播
    propagationValues[changedAward] = currentSlice[changedAward];
    if (actualLastActiveInCurrentPattern && currentSlice[actualLastActiveInCurrentPattern] !== undefined) {
      propagationValues[actualLastActiveInCurrentPattern] = currentSlice[actualLastActiveInCurrentPattern];
    }

    (Object.keys(newGlobalProbs) as ProbabilityPatternKey[]).forEach((pKey) => {
      if (pKey === currentPatternKey) return;

      const otherSlice = { ...newGlobalProbs[pKey] };
      let changedInOtherSlice = false;
      const awardsInOtherPatternDef = getAwardsInPattern(pKey);

      awardsInOtherPatternDef.forEach((awardInOther) => {
        if (propagationValues[awardInOther] !== undefined) {
          otherSlice[awardInOther] = propagationValues[awardInOther]!;
          changedInOtherSlice = true;
        }
      });

      if (changedInOtherSlice) {
        // この他のスライスを再調整
        const actualLastActiveInOtherPattern =
          awardsInOtherPatternDef
            .filter((a) => hasItems[a] || PROBABILITY_PATTERNS[pKey][a] > 0) // アイテムまたはパターン定義を考慮
            .sort((a, b) => a - b)[0] || awardsInOtherPatternDef[awardsInOtherPatternDef.length - 1];

        let sumExcludingLastInOther = 0;
        awardsInOtherPatternDef.forEach((award) => {
          if (award !== actualLastActiveInOtherPattern) {
            sumExcludingLastInOther += otherSlice[award] || 0;
          }
        });
        if (actualLastActiveInOtherPattern) {
          otherSlice[actualLastActiveInOtherPattern] = Math.max(0, 100 - sumExcludingLastInOther);
        }
      }
      // この他のパターンタイプで定義されている賞のみに値があることを確認
      [AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C].forEach((award) => {
        if (!awardsInOtherPatternDef.includes(award)) {
          otherSlice[award] = 0;
        }
        // 重要: アイテムがない賞は確率を0に設定
        if (awardsInOtherPatternDef.includes(award) && !hasItems[award]) {
          otherSlice[award] = 0;
        }
      });

      newGlobalProbs[pKey] = otherSlice;
    });

    // 新しい値を設定
    setTemporaryProbabilitiesByPattern(newGlobalProbs);
    // 現在のパターンの値も更新して表示を即時反映
    setTemporaryProbabilities(newGlobalProbs[currentPatternKey]);
    userInputRef.current[changedAward] = true;
  };

  const saveProbabilities = (probabilitiesToSave: Record<Award, number>) => {
    const currentKey = getCurrentPatternKey(hasItems);

    // アイテムがない場合はスキップ
    if (currentKey === GACHA_PATTERN_KEYS.EMPTY) return;

    const activeAwardsForPattern = getAwardsInPattern(currentKey).filter((award) => hasItems[award]);

    // 最後の賞の確率を計算
    const lastActiveAward =
      activeAwardsForPattern.sort((a, b) => a - b)[0] || activeAwardsForPattern[activeAwardsForPattern.length - 1];

    // lastActiveAward以外の賞の確率の総和を計算
    const sumExcludingLast = activeAwardsForPattern.reduce(
      (sum, award) => (award !== lastActiveAward ? sum + (Number(probabilitiesToSave[award]) || 0) : sum),
      0,
    );

    // 最後の賞の確率を計算して保存
    probabilitiesToSave[lastActiveAward] = 100 - sumExcludingLast;

    // 確率合計が100でない場合は保存しない
    if (
      activeAwardsForPattern.length > 0 &&
      Math.round(sumExcludingLast + probabilitiesToSave[lastActiveAward]) !== 100
    )
      return;

    // 確率階層チェック: C >= B >= A >= S >= 5（最小確率チェックも含む）
    const hierarchyValidation = validateProbabilityHierarchy(probabilitiesToSave, hasItems);

    if (!hierarchyValidation.isValid) {
      toast.custom((t) => CustomToast(t, 'error', hierarchyValidation.message!));
    }

    const awardProbabilitiesToSave: AwardProbability[] = activeAwardsForPattern.map((awardType) => ({
      awardType,
      probability: Number(probabilitiesToSave[awardType]) || 0,
    }));
    if (awardProbabilitiesToSave.length > 0) {
      setAwardProbability(itemId, awardProbabilitiesToSave);
    } else if (activeAwardCount > 0 && awardProbabilitiesToSave.length === 0) return;
  };

  const handleProbabilityBlur = (value?: number, awardType?: Award) => {
    if (value !== undefined && awardType !== undefined) {
      // 一時的な確率オブジェクトを作成して更新
      const updatedProbabilities = { ...temporaryProbabilities };
      updatedProbabilities[awardType] = value;
      // 更新された確率値でバリデーション
      saveProbabilities(updatedProbabilities);
    } else {
      // 値が渡されない場合は現在の値を使用
      saveProbabilities(temporaryProbabilities);
    }
  };

  const autoCalculateProbability = () => {
    const currentPatternKey = getCurrentPatternKey(hasItems);

    // アイテムがない場合はスキップ
    if (currentPatternKey === GACHA_PATTERN_KEYS.EMPTY) {
      setTemporaryProbabilities(EMPTY_PROBABILITY_PATTERNS);
      reset(mapProbabilitiesToFormValues(EMPTY_PROBABILITY_PATTERNS));
      return;
    }

    // 現在のパターンに対応するデフォルト確率を適用
    const defaultPatternValues = deepClone(PROBABILITY_PATTERNS[currentPatternKey]);

    // アイテムがない賞の確率を0に設定
    const awardsInPattern = getAwardsInPattern(currentPatternKey);
    [AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C].forEach((award) => {
      if (awardsInPattern.includes(award) && !hasItems[award]) {
        defaultPatternValues[award] = 0;
      }
    });

    // 現在のパターンの確率をグローバル状態に反映
    const newGlobalProbs = deepClone(temporaryProbabilitiesByPattern);
    newGlobalProbs[currentPatternKey] = defaultPatternValues;
    setTemporaryProbabilitiesByPattern(newGlobalProbs);

    // UI表示用の確率も更新
    setTemporaryProbabilities(defaultPatternValues);
    reset(mapProbabilitiesToFormValues(defaultPatternValues));
    // デフォルトの確率を保存
    saveProbabilities(defaultPatternValues);
    // ユーザー入力フラグをクリア
    userInputRef.current = {};
  };

  return (
    // アイテムがない場合は何も表示しない
    tempItemFiles.length === 0 ? null : (
      <section className="px-4 pb-4">
        <div className="mb-4 flex items-center gap-2">
          <SectionTitleWithIcon title="ガチャ確率設定" icon="/images/gacha/icons/Gacha.svg" />
          <WarningMessage message="出品後は変更できません" />
        </div>

        {isDuplicated ? (
          <div className="rounded-lg border border-gray-300 bg-white p-4">
            <p className="mb-4 text-medium-13">
              各賞[{GACHA_MIN_PROBABILITY}%以上]で設定してください。
              <br />
              一番下の賞は、確率が自動計算されます。
            </p>
            <div className="mx-auto w-52 space-y-4">
              {[AWARD_TYPE.S, AWARD_TYPE.A, AWARD_TYPE.B, AWARD_TYPE.C].map((awardNum) => {
                const awardType = awardNum as Award;
                const minValue =
                  awardType === AWARD_TYPE.S
                    ? GACHA_MIN_PROBABILITY
                    : awardType === AWARD_TYPE.A
                      ? GACHA_MIN_PROBABILITY
                      : 0;
                // isDisabled ロジックはそのまま: アイテムがない場合、または自動計算フィールドの場合、
                // または S-Only モードの S 賞の場合に無効化
                let isDisabled = !hasItems[awardType] || awardType === lastActiveAward;
                if (isSOnly && awardType === AWARD_TYPE.S) isDisabled = true;
                const probabilityValue = awardProbabilities?.find((p) => p.awardType === awardType)?.probability;
                return (
                  <ProbabilityInputItem
                    key={awardType}
                    award={awardType}
                    isShowProbability={isDuplicated && hasItems[awardType]}
                    probability={probabilityValue || temporaryProbabilities[awardType] || 0}
                    control={control}
                    onBlur={(value) => handleProbabilityBlur(value, awardType)}
                    onChange={(value) => {
                      const numValue = Number(value) || 0;
                      handleProbabilityChange(awardType, numValue);
                    }}
                    minValue={minValue}
                    disabled={isDisabled || isEdit}
                  />
                );
              })}
              {!isEdit && (
                <>
                  <div
                    className={clsx(
                      'mb-3 flex items-center justify-center py-2 text-white',
                      isAwardProbabilitiesValid && 'bg-secondary',
                      !isAwardProbabilitiesValid && 'bg-error',
                    )}
                  >
                    <span className="text-bold-18">{totalProbability}</span>
                    <span className="text-medium-14">%</span>
                  </div>
                  <FloatButton
                    buttonSize="lg"
                    onClick={autoCalculateProbability}
                    disabled={activeAwardCount === 0}
                    className="mx-auto"
                  >
                    <span className="text-bold-12">確率を自動でふりわけ</span>
                  </FloatButton>
                </>
              )}
            </div>
          </div>
        ) : (
          <p className="text-medium-14">景品の出現確率はすべて均一になります</p>
        )}
      </section>
    )
  );
};

export default GachaProbabilitySettings;
