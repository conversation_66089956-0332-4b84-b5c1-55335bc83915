'use client';
import React, { useRef, useMemo } from 'react';
import { useParams } from 'next/navigation';
import type { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import GachaDuplicateSettings from './gacha-duplicate-settings';
import GachaProbabilitySettings from './gacha-probability-settings';
import GachaTotalCount from './gacha-total-count';
import GachaUploadItems from './gacha-upload-items';
import type { ExhibitGachaItem } from '@/types/exhibitItem';

type GachaUploadSectionProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
  isEdit?: boolean;
};

const GachaUploadSection = ({ numbering, shopLimitation, isEdit }: GachaUploadSectionProps) => {
  // 合計ガチャ数セクションの参照を作成
  const totalGachaCountSectionRef = useRef<HTMLDivElement>(null);

  const params = useParams();
  const itemId = params.id as string;
  const { exhibits, setIsDuplicated } = useExhibitsStore();

  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]) as ExhibitGachaItem;

  const isDuplicated = exhibitItem?.isDuplicated ?? true;

  const hasItems = exhibitItem?.itemFiles && exhibitItem.itemFiles.length > 0;
  const tempItemFiles = exhibitItem?.itemFiles || [];
  const { hasEnoughItems = false } = exhibitItem || {};

  const onChangeDuplicate = (value: boolean) => {
    setIsDuplicated(itemId, value);
  };

  return (
    <div>
      <div className="section-double-border">
        <GachaUploadItems
          numbering={numbering}
          shopLimitation={shopLimitation}
          totalGachaCountSectionRef={totalGachaCountSectionRef}
          isEdit={isEdit}
        />
        {hasItems && (
          <>
            <GachaTotalCount
              itemFiles={tempItemFiles}
              hasEnoughItems={hasEnoughItems}
              totalGachaCountSectionRef={totalGachaCountSectionRef}
            />
            <GachaDuplicateSettings
              isDuplicated={isDuplicated}
              onChangeDuplicate={onChangeDuplicate}
              disabled={isEdit}
            />
            <GachaProbabilitySettings isEdit={isEdit} isDuplicated={isDuplicated} />
          </>
        )}
      </div>
    </div>
  );
};

export default GachaUploadSection;
