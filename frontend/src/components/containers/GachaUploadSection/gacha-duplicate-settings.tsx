'use client';
import React, { useState } from 'react';
import { Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import Button from '@/components/atoms/button';
import RadioGroup from '@/components/atoms/radioGroup';
import RadioGroupItem from '@/components/atoms/radioGroup/radio-group-item';
import Instruction from '@/components/atoms/typography/instruction';
import WarningMessage from '@/components/atoms/typography/warning-message';
import SectionTitleWithIcon from '@/components/atoms/typography/section-title-with-icon';
import ShopPublicImage from '@/components/ShopImage';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import 'swiper/css';
import 'swiper/css/pagination';

type GachaDuplicateSettingsProps = {
  isDuplicated: boolean;
  onChangeDuplicate: (value: boolean) => void;
  disabled?: boolean;
};

const GachaDuplicateSettings = ({ isDuplicated, onChangeDuplicate, disabled }: GachaDuplicateSettingsProps) => {
  const { openInstructionModal } = useInstructionModalStore();
  const [slideIndex, setSlideIndex] = useState(0);

  const handleOpenIntroductionModal = () => {
    const instructionContent = (
      <>
        <div className="flex h-18 items-center justify-center text-center text-bold-15">
          {slideIndex === 0 && <div>景品の重複設定</div>}
          {slideIndex === 1 && <div>景品の重複設定 (2/2)</div>}
        </div>
        <Swiper
          pagination={{
            clickable: true,
          }}
          modules={[Pagination]}
          className="instructions-swiper !h-auto !pb-5"
          onSlideChange={(swiper) => setSlideIndex(swiper.activeIndex)}
        >
          <SwiperSlide>
            <div className="!flex flex-col items-center justify-center bg-navy-50 px-4 py-2 text-medium-14">
              <div className="mb-4 text-left">
                <p className="mb-2">❶ 「重複して出現する」設定</p>
                <p>同じ景品が複数回出現する可能性があります。</p>
              </div>
              <div className="text-left">
                <p className="mb-2">❷ 重複して出現しない</p>
                <p>
                  同じ景品が複数回出現することはありません。
                  <br />
                  この設定をした場合、景品の出現確率はすべて均一になります。
                </p>
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
      </>
    );

    openInstructionModal(null, instructionContent, null);
  };

  const duplicateLabel = (
    <div className="ml-2">
      <p>同じ景品が重複して出現する</p>
      <Instruction>同じ景品が出現する可能性があります。</Instruction>
    </div>
  );

  const noDuplicateLabel = (
    <div className="ml-2">
      <p>重複して出現しない</p>
      <Instruction>同じ景品は出現しません。</Instruction>
    </div>
  );

  const selectedValue = isDuplicated ? 'true' : 'false';

  return (
    <div className="px-4 pb-4">
      <div className="mb-4 flex items-center justify-between gap-2">
        <div className="flex items-center justify-start gap-2">
          <SectionTitleWithIcon title="景品の重複設定" icon="/images/gacha/icons/Gacha.svg" />
          <WarningMessage message="出品後は変更できません" />
        </div>
        <Button
          className="ml-auto"
          buttonType="light-small"
          buttonShape="circle"
          buttonSize="xxs"
          onClick={handleOpenIntroductionModal}
        >
          <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
        </Button>
      </div>

      <div className="rounded-lg bg-white p-4">
        <RadioGroup name="isDuplicate" defaultValue="true" direction="column">
          <>
            <RadioGroupItem
              label={duplicateLabel}
              value="true"
              name="isDuplicate"
              selectedValue={selectedValue}
              onChange={(value) => onChangeDuplicate(value === 'true')}
              disabled={disabled}
            />
            <RadioGroupItem
              label={noDuplicateLabel}
              value="false"
              name="isDuplicate"
              selectedValue={selectedValue}
              onChange={(value) => onChangeDuplicate(value === 'true')}
              disabled={disabled}
            />
          </>
        </RadioGroup>
      </div>
    </div>
  );
};

export default GachaDuplicateSettings;
