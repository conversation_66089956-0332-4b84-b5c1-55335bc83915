import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useParams, useSearchParams } from 'next/navigation';
import NumberInput from '@/components/atoms/inputs/number-input';
import TextAreaInput from '@/components/atoms/inputs/text-area-input';
import TextInput from '@/components/atoms/inputs/text-input';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import { useExhibitsStore } from '@/store/useExhibit';
import {
  ITEM_DESCRIPTION_MAX_LENGTH,
  ITEM_MAX_PRICE,
  ITEM_MIN_PRICE,
  ITEM_MIN_PRICE_FOR_CHEKI,
  ITEM_TITLE_MAX_LENGTH,
} from '@/consts/inputLength';
import { shopRequests } from '@/services/shopRequests';
import { ITEM_TYPE } from '@/types/item';
import { itemMainInfoSchema, itemMainInfoSchemaForCheki, ItemMainInfoType } from '@/types/item-main-info-schema';
import { ExhibitType } from '@/types/shopItem';

type ItemMainInfoProps = {
  numbering: string;
};

const PhysicalItemDefaultDescription = `
※海外発送には対応しておりませんので、あらかじめご了承ください。
※ご希望のお名前は備考欄に必ずご記入ください。
（ご記入のない場合、配送先登録済みのお名前を使用させていただきます。）
※配送まで○○日ほどお時間をいただく場合がございます。
`.trim();

const ItemMainInfo = ({ numbering }: ItemMainInfoProps) => {
  const useExhibits = useExhibitsStore();
  const { exhibits, setTitle, setPriceSet, setDescription, marginRate, setMarginRate, isNew } = useExhibits;
  const params = useParams();
  const searchParams = useSearchParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { title, description, priceSet } = exhibitItem ?? {};
  const itemType = (searchParams.get('item_type') as ExhibitType) || 'digitalBundle';

  const itemPriceLimit = useMemo(() => {
    return {
      min: itemType === ITEM_TYPE.CHEKI.str ? ITEM_MIN_PRICE_FOR_CHEKI : ITEM_MIN_PRICE,
      max: ITEM_MAX_PRICE,
    };
  }, [itemType]);

  const zodSchema = itemType === ITEM_TYPE.CHEKI.str ? itemMainInfoSchemaForCheki : itemMainInfoSchema;

  const {
    control,
    formState: { errors },
    watch,
    reset,
    trigger,
    setValue,
  } = useForm<ItemMainInfoType>({
    mode: 'onBlur',
    resolver: zodResolver(zodSchema),
    defaultValues: {
      title,
      description,
      priceSet,
    },
  });

  const watchedTitle = watch('title');
  const watchedDescription = watch('description');
  const watchedPriceValue = watch('priceSet');

  const fee = useMemo(() => {
    return Math.ceil(watchedPriceValue * marginRate);
  }, [watchedPriceValue, marginRate]);

  const profit = useMemo(() => {
    return watchedPriceValue - fee;
  }, [watchedPriceValue, fee]);

  useEffect(() => {
    // チェキの出品時にデフォルトの説明文をセット
    if (isNew(itemId) && !description && itemType === ITEM_TYPE.CHEKI.str) {
      setDescription(itemId, PhysicalItemDefaultDescription);
    }
    reset({
      title,
      description,
      priceSet,
    });
  }, [title, description, priceSet, reset, setDescription, itemId, isNew, itemType]);

  useEffect(() => {
    (async () => {
      try {
        const response = await shopRequests.getShopMarginRate();
        setMarginRate(response.data?.marginRate || 0);
      } catch (e) {
        console.error(JSON.stringify({ e }));
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onBlurPriceAll = (price: string | undefined) => {
    const numberValue = Number(price);
    if (!Number.isNaN(numberValue)) {
      setPriceSet(itemId, numberValue);
      setValue('priceSet', numberValue);
    }
    trigger('priceSet');
  };

  const onBlurTitle = () => {
    setTitle(itemId, watchedTitle);
  };

  const onBlurDescription = (description: string | undefined) => {
    if (description) {
      setDescription(itemId, description);
    }
  };

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <SectionTitleWithNumber title="商品タイトルと価格を入力" numbering={numbering} />
        <TextInput
          labelTitle="商品タイトル"
          inputName="title"
          required
          maxLength={ITEM_TITLE_MAX_LENGTH}
          errorMsg={errors.title?.message}
          defaultValue={title}
          control={control}
          error={!!errors.title}
          textLength={watchedTitle?.length || 0}
          inputClassName="text-medium-15"
          onBlur={onBlurTitle}
        />
        <TextAreaInput
          control={control}
          labelTitle="商品説明"
          inputName="description"
          maxLength={ITEM_DESCRIPTION_MAX_LENGTH}
          inputClassName="text-regular-13 !min-h-18"
          defaultValue={description}
          errorMsg={errors.description?.message}
          textLength={watchedDescription?.length}
          onBlur={(value) => {
            onBlurDescription(value);
          }}
        />
        <div className="mt-4 rounded-lg bg-navy-100 p-2">
          <div className="rounded-lg bg-white py-2 pl-4 pr-2">
            <NumberInput
              labelTitle="出品価格"
              prefix="¥"
              suffix="（税込）"
              control={control}
              step={100}
              inputName="priceSet"
              max={itemPriceLimit.max}
              min={itemPriceLimit.min}
              required
              labelClassName="!mb-0"
              inputClassName="text-bold-22 bg-gray-50 col-span-2"
              defaultValue={priceSet}
              error={!!errors.priceSet}
              errorMsg={
                errors.priceSet?.message ||
                `出品価格は¥${itemPriceLimit.min.toLocaleString()}(税込)〜¥${itemPriceLimit.max.toLocaleString()}(税込)です`
              }
              className="!mb-0 grid grid-cols-3 items-center gap-x-7"
              coverValue={watchedPriceValue}
              onBlur={onBlurPriceAll}
            />
            <ul className="mt-3 pr-2 text-regular-11">
              <li className="flex justify-between">
                <span>販売手数料</span>
                <span className="w-36 overflow-hidden text-right">￥{fee.toLocaleString()}</span>
              </li>
              <li className="mt-3 flex justify-between">
                <span>販売利益</span>
                <span className="w-36 overflow-hidden text-right text-orange-200">￥{profit.toLocaleString()}</span>
              </li>
            </ul>
            <p className="mt-2 text-regular-10 text-gray-500">
              *出品価格は¥{itemPriceLimit.min.toLocaleString()}(税込)〜¥{itemPriceLimit.max.toLocaleString()}
              (税込)です
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ItemMainInfo;
