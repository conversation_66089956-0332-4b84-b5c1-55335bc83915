import SectionTitle from '@/components/atoms/typography/section-title';
import PreviewSquareItem from '@/components/views/PreviewSquareItem';
import { GACHA_BENEFIT_CONDITIONS } from '@/consts/gacha-data';
import { GachaBenefit } from '@/types/gacha';
type GachaConfirmBenefitSectionProps = {
  benefits: GachaBenefit[];
};

const GachaConfirmBenefitSection = ({ benefits }: GachaConfirmBenefitSectionProps) => {
  return (
    <>
      {benefits.map((benefit) => (
        <div key={benefit.id}>
          <SectionTitle
            title={GACHA_BENEFIT_CONDITIONS.find((condition) => condition.value === benefit.conditionType)?.label}
          />
          <p className="mt-4 overflow-hidden text-ellipsis whitespace-pre-wrap break-words text-regular-14">
            {benefit.description}
          </p>
          <div className="mt-4 grid grid-cols-3 items-start gap-4 PC:grid-cols-4 PC:gap-2">
            {benefit.benefitFiles.map((item) => (
              <PreviewSquareItem key={item.id} item={item} />
            ))}
          </div>
        </div>
      ))}
    </>
  );
};

export default GachaConfirmBenefitSection;
