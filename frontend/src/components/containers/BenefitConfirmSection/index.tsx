import type { Benefit } from '@/types/shopItem';
import type { GachaBenefit } from '@/types/gacha';
import CommonConfirmBenefitSection from './common-confirm-benefit-section';
import GachaConfirmBenefitSection from './gacha-confirm-benefit-section';

type BenefitConfirmSectionProps = {
  isGacha: boolean;
  benefits: Benefit[] | GachaBenefit[];
};
const BenefitConfirmSection = ({ isGacha, benefits }: BenefitConfirmSectionProps) => {
  if (isGacha) {
    return <GachaConfirmBenefitSection benefits={benefits as GachaBenefit[]} />;
  }
  return <CommonConfirmBenefitSection firstBenefit={benefits[0] as Benefit} />;
};

export default BenefitConfirmSection;
