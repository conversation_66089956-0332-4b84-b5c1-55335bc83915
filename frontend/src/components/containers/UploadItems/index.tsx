'use client';

import React, { useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'react-hot-toast';
import { useParams } from 'next/navigation';
import { Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import Button from '@/components/atoms/button';
import CustomToast from '@/components/atoms/toast/custom-toast';
import Instruction from '@/components/atoms/typography/instruction';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import UploadedItemList from '@/components/containers/UploadedItemsList';
import ShopPublicImage from '@/components/ShopImage';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import { ACCEPTED_FILE_TYPES, THUMBNAIL_BLUR_LEVEL, THUMBNAIL_WATERMARK_LEVEL } from '@/consts/file';
import { MAX_ITEM_COUNT } from '@/consts/sizes';
import { useUploadProgress } from '@/hooks/useUploadProgress';
import { getUserIdentityId } from '@/utils/base';
import { validateUploadedFiles } from '@/utils/item';
import { isLimitations } from '@/utils/limitation';
import { generatePreSignedThumbnails, generateProcessedThumbnail } from '@/utils/thumbnail';
import { handleMediaUpload } from '@/utils/upload';
import { DigitalBundle } from '@/types/exhibitItem';
import { ItemFiles, SingleItem } from '@/types/shopItem';
import 'swiper/css';
import 'swiper/css/pagination';

type UploadItemsProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
};

const UploadItems = ({ numbering, shopLimitation }: UploadItemsProps) => {
  const params = useParams();
  const identityId = getUserIdentityId(params.identityId as string);
  const itemId = params.id as string;
  const useExhibits = useExhibitsStore();
  const { exhibits, isNew, setItemFiles, setSelectedIndex } = useExhibits;
  const uploadRef = useRef<HTMLInputElement>(null);
  const [sorting, setSorting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]) as DigitalBundle;
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const tempItemFiles = (exhibitItem?.itemFiles as ItemFiles) ?? [];

  const { uploadProgress, handleProgress, resetProgress } = useUploadProgress();
  const { isFileQuantityDefault } = isLimitations(shopLimitation);

  useEffect(() => {
    const hasLoadingFiles = tempItemFiles.some((item) => item.isLoading);
    setIsLoading(hasLoadingFiles);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tempItemFiles]);

  const handleClickUpload = () => {
    uploadRef.current?.setAttribute('accept', ACCEPTED_FILE_TYPES);
    uploadRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const currentFiles = event.target.files;
    if (!!currentFiles?.length) {
      const validationRes = validateUploadedFiles(currentFiles, tempItemFiles, shopLimitation);
      if (validationRes) {
        toast.custom((t) => CustomToast(t, 'error', validationRes));
        return;
      }

      if (currentFiles && tempItemFiles) {
        try {
          resetProgress();
          let resultFiles: SingleItem[] = [];
          resultFiles = await handleMediaUpload(
            currentFiles,
            tempItemFiles,
            (files: ItemFiles) => setItemFiles(itemId, files),
            handleProgress,
          );

          resultFiles = await generatePreSignedThumbnails(resultFiles, identityId);

          resultFiles = await Promise.all(
            resultFiles.map(async (file) => {
              return await generateProcessedThumbnail(file, THUMBNAIL_BLUR_LEVEL.NONE, THUMBNAIL_WATERMARK_LEVEL.WHITE);
            }),
          );

          if (!resultFiles.some((file) => file.selected)) {
            resultFiles[0].selected = true;
            setSelectedIndex(itemId, 0);
          }
          // アップロード完了後の最終的なファイルリストをセット
          setItemFiles(itemId, resultFiles);
        } catch (error) {
          console.error('Error processing media files:', error);
        }
      }
    }
  };

  const InstructionContent = () => {
    const [slideIndex, setSlideIndex] = useState(0);

    return (
      <>
        <div className="flex h-18 items-center justify-center text-center text-bold-15">
          {slideIndex === 0 && (
            <div>
              アップロード可能な
              <br />
              ファイル形式と容量
            </div>
          )}
          {slideIndex === 1 && <div>セット販売と単品販売が同時にできます</div>}
          {slideIndex === 2 && <div>単品販売の設定</div>}
        </div>
        <Swiper
          pagination={{
            clickable: true,
          }}
          modules={[Pagination]}
          className="instructions-swiper"
          onSlideChange={(swiper) => setSlideIndex(swiper.activeIndex)}
        >
          <SwiperSlide>
            <div className="!flex !h-42 items-center justify-center bg-navy-50">
              <ShopPublicImage src={'/images/upload1.webp'} width={308} height={116} alt="modal" />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="!flex !h-42 items-center justify-center bg-navy-50">
              <ShopPublicImage src={'/images/upload2.webp'} width={304} height={145} alt="modal" />
            </div>
          </SwiperSlide>
          <SwiperSlide>
            <div className="!flex !h-42 items-center justify-center bg-navy-50">
              <ShopPublicImage src={'/images/upload3.webp'} width={224} height={152} alt="modal" />
            </div>
          </SwiperSlide>
        </Swiper>
        <div className="h-18 px-4 pt-3 text-regular-12">
          {slideIndex === 0 && <div>商品データに登録できるファイル形式とデータ容量は上記の通りです。</div>}
          {slideIndex === 1 && (
            <div>
              商品データのアップロード数が2点以上の場合、
              <br />
              2点まとめての「セット販売」と、1点1点ばら売りする「単品販売」の設定を同時に行うことができます。
            </div>
          )}
          {slideIndex === 2 && (
            <div>
              商品データのアップロード数が2点以上の場合、
              <br />
              ページ下にある【06】の項目、「単品販売」を設定することで商品データごとに販売できます。設定をしない場合は、セット販売での出品となります。
            </div>
          )}
        </div>
      </>
    );
  };

  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    openInstructionModal(null, <InstructionContent />, null);
  };

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithNumber title="商品データをアップロード" numbering={numbering} required className="!mb-0" />
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>
        {tempItemFiles.length > 0 && (
          <div className="mb-6 mt-3">
            <UploadedItemList
              uploadProgress={uploadProgress}
              sorting={sorting}
              setSorting={setSorting}
              isLoading={isLoading}
            />
          </div>
        )}
        {!isNew(itemId) && exhibitItem?.singleSaleDefault ? (
          // 単品販売の編集時は商品の追加削除は不可
          <Instruction className="mb-4 text-red-500">*単品販売をしているため商品の追加・削除ができません</Instruction>
        ) : (
          <div className="flex items-center justify-center">
            <Button buttonType="light-shadow" onClick={handleClickUpload} disabled={!!sorting}>
              <div className="absolute left-1 flex size-9.5 items-center justify-center rounded-full bg-gray-50">
                <ShopPublicImage
                  src="/images/icons/shoppingbag.svg"
                  width={22}
                  height={22}
                  alt="upload"
                  className={!!sorting ? 'opacity-50' : ''}
                />
              </div>
              <span>アップロード</span>
            </Button>
            <input
              type="file"
              className="hidden"
              ref={uploadRef}
              onChange={(e) => {
                handleFileChange(e);
                e.target.value = ''; // 同じファイルを選択してもonChangeが発火させるようクリア
              }}
              multiple
            />
          </div>
        )}
        <div className="relative mt-2 flex items-center justify-center">
          <span
            className="cursor-pointer pl-4 pt-2 text-regular-12 text-blue-160 underline"
            onClick={handleOpenIntroductionModal}
          >
            ファイルの種類と容量について
          </span>
          <div className="absolute right-0 flex items-center gap-1">
            <ShopPublicImage
              src="/images/icons/GrayBox.svg"
              width={14}
              height={14}
              alt="upload"
              className="brightness-130"
            />
            <span className="text-regular-14 text-gray-400">
              <em className="text-regular-16 not-italic">{tempItemFiles.length}</em>
              {isFileQuantityDefault && <>/{MAX_ITEM_COUNT}</>}
            </span>
          </div>
        </div>
      </div>
    </section>
  );
};

export default UploadItems;
