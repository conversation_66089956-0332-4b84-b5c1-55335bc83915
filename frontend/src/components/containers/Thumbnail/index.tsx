'use client';
import React, { useEffect, useRef, useState } from 'react';
import clsx from 'clsx';
import GoodsIcon from '@/components/atoms/badges/goods-icon';
import OutlinedButton from '@/components/atoms/button/outlined-button';
import ShopPublicImage from '@/components/ShopImage';
import Button from '../../atoms/button';
import LoadingOverlay from '../itemForm/LoadingOverlay';
import { useThumbnailStore } from '@/store/useCropThumbnail';
import { compressImage } from '@/utils/base';

interface IThumbnailProps {
  size: 'sm' | 'md' | 'lg';
  thumbImage: string;
  setThumbImage: (image: string) => void;
  setFile?: (file: File) => void;
  className?: string;
  isClickUpload?: boolean;
  isNeedCrop?: boolean;
  type?: 'audio' | 'video' | 'image';
  originalImage?: string;
  onClick?: () => void;
  showType?: boolean;
  isNeedEditIcon?: boolean;
  isLoading?: boolean;
  progress?: number;
  shape?: 'circle' | 'square';
}

const Thumbnail = ({
  size,
  thumbImage,
  originalImage,
  setThumbImage,
  setFile,
  isClickUpload = true,
  isNeedCrop = false,
  isNeedEditIcon = false,
  type,
  className,
  onClick,
  showType,
  isLoading = false,
  progress,
  shape = 'square',
}: IThumbnailProps) => {
  const cropStore = useThumbnailStore();
  const { onCropOpen, setCropperProps, cropProps, onCropClose } = cropStore;
  const [bodyWidth, setBodyWidth] = useState(0);
  const [thumb, setThumb] = useState<string>();
  const [imgClass, setImgClass] = useState<string>();

  const uploadImage = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const updateBodyWidth = () => {
      setBodyWidth(document.body.clientWidth);
    };
    // 初回レンダリング時に幅を設定
    updateBodyWidth();
    // ウィンドウリサイズ時に幅を更新
    window.addEventListener('resize', updateBodyWidth);
    // クリーンアップ
    return () => {
      window.removeEventListener('resize', updateBodyWidth);
    };
  }, []);

  const getSize = (size: 'sm' | 'md' | 'lg') => {
    switch (size) {
      case 'sm':
        return { width: 104, height: 104 };
      case 'md':
        return { width: 160, height: 160 };
      case 'lg':
        const h = bodyWidth / 3;
        return { width: bodyWidth, height: h };
      default:
        return { width: 160, height: 160 };
    }
  };
  const sizes = getSize(size);
  const handleChooseImage = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onClick) {
      onClick();
    }
    const file = e.target.files?.[0];
    if (file) {
      const compressedFile = (await compressImage(file)) as File;
      if (setFile) {
        setFile(compressedFile);
      }
      setThumb(URL.createObjectURL(compressedFile));
    }
  };
  useEffect(() => {
    if (thumb) {
      onCropOpen();
      setCropperProps({
        ...cropProps,
        aspect: size === 'lg' ? 3 / 1 : 1,
        image: isClickUpload ? thumb : originalImage,
        onCropComplete: (image: string) => {
          setThumbImage(image);
          onCropClose();
        },
      });
    }
    return () => {
      onCropClose();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [thumb]);

  useEffect(() => {
    const img = new window.Image();
    img.src = thumbImage;
    img.onload = () => {
      const imgClass = img.width ? (img.width > img.height ? 'w-full' : 'h-full w-auto') : 'w-full';
      setImgClass(imgClass);
    };
  }, [thumbImage]);

  const handleClick = () => {
    if (!isNeedCrop && !isClickUpload) return;
    else if (isClickUpload) uploadImage.current?.click();
    else {
      onCropOpen();
      setCropperProps({
        ...cropProps,
        aspect: size === 'lg' ? 3 / 1 : 1,
        image: originalImage,
        onCropComplete: (image: string) => {
          setThumbImage(image);
          onCropClose();
        },
      });
    }
  };
  return (
    <div
      className={clsx(
        'relative flex items-center justify-center overflow-hidden',
        isLoading ? 'bg-black' : thumbImage ? '' : 'transparent-bg',
        shape === 'circle' && 'rounded-full',
        className,
      )}
      style={sizes}
      onClick={handleClick}
    >
      {thumbImage && !isLoading ? <img src={thumbImage} alt="thumbnail" className={imgClass} /> : null}

      {isLoading && <LoadingOverlay progress={progress} />}

      {isLoading ? null : thumbImage ? (
        !!isNeedEditIcon && (
          <OutlinedButton
            buttonColor="transparent"
            buttonShape="circle"
            buttonType="edit"
            buttonSize={size === 'sm' ? 'xs' : 'md'}
            className="absolute inset-0 m-auto"
          />
        )
      ) : (
        <Button buttonType="light-small" buttonShape="circle" buttonSize="lg">
          <ShopPublicImage src="/images/icons/PhotoIcn.svg" width={22} height={22} alt="edit cover" />
        </Button>
      )}
      {showType && type && <GoodsIcon type={type} className="absolute bottom-1 left-1" />}
      <input
        type="file"
        name="thumb"
        accept="image/jpeg, image/png, image/gif, image/jpg"
        onChange={(e) => {
          handleChooseImage(e);
          e.target.value = ''; // 同じファイルを選択してもonChangeが発火させるようクリア
        }}
        ref={uploadImage}
        hidden
      />
    </div>
  );
};

export default Thumbnail;
