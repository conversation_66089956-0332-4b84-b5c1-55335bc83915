'use client';

import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'next/navigation';
import Button from '@/components/atoms/button';
import TextInput from '@/components/atoms/inputs/text-input';
import Thumbnail from '@/components/containers/Thumbnail';
import ShopPublicImage from '@/components/ShopImage';
import { useExhibitsStore } from '@/store/useExhibit';
import { extractBenefitFiles } from '@/utils/extractDatas';
import { isDigitalItem, isDigitalGacha } from '@/utils/itemTypes';
import { GachaBenefitFile, ConditionType } from '@/types/gacha';
import { SingleItem, Benefit } from '@/types/shopItem';

type ItemTitleInput = {
  title: string;
};

interface IUploadedItemProps {
  id: string;
  src: string;
  title: string;
  type?: 'image' | 'video' | 'audio';
  setFile?: (file: File) => void;
  setThumbImage: (image: string) => void;
  thumbnail?: string;
  handleDelete?: () => void;
  showType?: boolean;
  uploadType?: string;
  isLoading?: boolean;
  sorting?: boolean;
  progress: number;
  showTitle?: boolean;
  conditionType?: ConditionType;
}

// 型守卫関数
const isGachaBenefitFile = (item: SingleItem | GachaBenefitFile): item is GachaBenefitFile => {
  return 'conditionType' in item && typeof item.conditionType === 'number';
};

const isSingleItem = (item: SingleItem | GachaBenefitFile): item is SingleItem => {
  return 'src' in item;
};

// アイテムを SingleItem に変換する関数
const convertToSingleItem = (item: SingleItem | GachaBenefitFile): SingleItem => {
  if (isSingleItem(item)) {
    return item;
  } else {
    // GachaBenefitFile を SingleItem に変換
    return {
      id: item.id || '',
      title: item.title || '',
      src: item.objectUri || '',
      thumbnail: item.thumbnail || undefined,
      type: (item.type as 'image' | 'video' | 'audio') || 'image',
      size: item.size,
      duration: item.duration,
    };
  }
};

const UploadedItem = ({
  src,
  title,
  type,
  setFile,
  setThumbImage,
  thumbnail,
  handleDelete,
  showType,
  id,
  uploadType,
  isLoading = false,
  sorting = false,
  progress,
  showTitle = true,
  conditionType,
}: IUploadedItemProps) => {
  const useExhibits = useExhibitsStore();
  const { exhibits, setItemFiles, setBenefits, setSamples } = useExhibits;
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { itemFiles = [], benefits, samples } = isDigitalItem(exhibitItem) ? exhibitItem : {};
  const [titleLength, setTitleLength] = useState(title.length);

  const {
    control,
    formState: { errors },
    watch,
  } = useForm<ItemTitleInput>({
    mode: 'onBlur',
    defaultValues: {
      title,
    },
  });
  useEffect(() => {
    setTitleLength(watch('title').length);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watch('title')]);

  const handleChangeTitle = () => {
    // digitalGacha の場合は早期リターン
    if (isDigitalGacha(exhibitItem)) {
      return;
    }

    const benefitFiles: (SingleItem | GachaBenefitFile)[] = extractBenefitFiles(benefits);
    const categoryItems = uploadType === 'sample' ? samples : uploadType === 'benefit' ? benefitFiles : itemFiles;
    if (!categoryItems) return;

    let tempItems = [...categoryItems];
    tempItems = tempItems.map((item) => {
      if (item.id === id) {
        const tempItem = { ...item };
        tempItem.title = watch('title');
        if (uploadType === 'benefit' && conditionType !== undefined && isGachaBenefitFile(tempItem)) {
          tempItem.conditionType = conditionType;
        }
        return tempItem;
      }
      return item;
    });

    if (uploadType === 'sample') {
      setSamples(itemId, tempItems as SingleItem[]);
    } else if (uploadType === 'benefit') {
      // 通常のアイテムの場合、Benefit[] 形式で更新
      const convertedFiles: SingleItem[] = tempItems.map(convertToSingleItem);

      // benefits の型を統一する
      const normalizedBenefits: Benefit[] = Array.isArray(benefits)
        ? benefits.map((benefit, index) => {
            if (index === 0) {
              // 最初の benefit のファイルを更新
              return {
                description: benefit.description || '',
                conditionType: typeof benefit.conditionType === 'number' ? benefit.conditionType : 0,
                benefitFiles: convertedFiles,
              };
            }
            // 他の benefit はそのまま保持（ファイルは SingleItem[] に変換）
            const existingFiles = 'benefitFiles' in benefit ? benefit.benefitFiles.map(convertToSingleItem) : [];
            return {
              description: benefit.description || '',
              conditionType: typeof benefit.conditionType === 'number' ? benefit.conditionType : 0,
              benefitFiles: existingFiles,
            };
          })
        : [
            {
              benefitFiles: convertedFiles,
              description: '',
              conditionType: 0,
            },
          ];
      setBenefits(itemId, normalizedBenefits);
    } else {
      setItemFiles(itemId, tempItems as SingleItem[]);
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center ${sorting ? 'animate-shake' : ''}`}>
      <div className="relative flex size-26 cursor-default items-center justify-center overflow-hidden rounded-lg">
        <div className="absolute left-0 top-0">
          <Thumbnail
            size="sm"
            thumbImage={thumbnail || src}
            isLoading={isLoading}
            progress={progress}
            setFile={setFile!}
            setThumbImage={setThumbImage}
            isClickUpload={false}
            isNeedCrop={false}
            originalImage=""
            /*src*/
            type={type}
            showType={showType}
          />
        </div>
        {!sorting && handleDelete && (
          <Button
            buttonType="light-small"
            buttonShape="circle"
            buttonSize="xxs"
            className="absolute right-1 top-1"
            onClick={handleDelete}
          >
            <ShopPublicImage src="/images/icons/CloseBtn.svg" width={12} height={12} alt="delete" />
          </Button>
        )}
      </div>
      {showTitle && (
        <TextInput
          inputName="title"
          defaultValue={title}
          control={control}
          error={errors.title ? true : false}
          errorMsg={errors.title?.message}
          textLength={titleLength}
          maxLength={30}
          required
          className="!mb-2 mt-2 max-w-26"
          onBlur={handleChangeTitle}
          disabled={sorting}
        />
      )}
    </div>
  );
};

export default UploadedItem;
