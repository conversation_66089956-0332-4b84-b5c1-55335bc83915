'use client';

import { useMemo } from 'react';
// import toast from 'react-hot-toast';
// import { sendGTMEvent } from '@next/third-parties/google';
// import clsx from 'clsx';
// import { useRouter } from 'next/navigation';
import Instruction from '@/components/atoms/typography/instruction';
import SectionTitleWithIcon from '@/components/atoms/typography/section-title-with-icon';
import ReadMoreButton from '@/components/containers/ReadMoreButton';
import ShopPublicImage from '@/components/ShopImage';
// import { useCurrentUser } from '@/store/useCurrentUser';
// import { useModalsStore } from '@/store/useModals';
import AwardProbabilityDisplay from './award-probability-display';
import BenefitItemList from '@/app/[identityId]/viewer/[itemId]/_components/benefit-item-list';
// import { checkItemPasswordUnlockCache, createItemPasswordUnlockCache } from '@/app/actions/shopItem';
import { useGroupGachaItems } from '@/hooks/useGroupGachaItems';
// import { usePeriodState } from '@/hooks/usePeriodState';
import { bytesToMB } from '@/utils/base';
// import { getIsNowOnSale } from '@/utils/item';
import { handleAwardProbability } from '@/utils/gacha-helpers';
import type { GachaBenefit, GachaItemFile, GachaBenefitFile, AwardProbability } from '@/types/gacha';
import type { Period } from '@/types/shopItem';

type GachaDetailItemSectionProps = {
  itemFiles: GachaItemFile[];
  itemId?: number;
  thumbnail: string;
  title: string;
  price: number;
  currentPrice?: number;
  discountRate?: number;
  discount?: {
    percentage: number;
    start?: Date | string;
    end?: Date | string;
  };
  thumbnailRatio: number;
  benefit?: GachaBenefit;
  period?: Period;
  isPreview?: boolean;
  identityId: string;
  isPurchased?: boolean;
  isCheckout?: boolean;
  creatorId?: string;
  isSoldOut: boolean;
  hasPassword?: boolean;
  forceShowItemOption?: boolean;
  totalCapacity: number;
  awardProbabilities: AwardProbability[];
  isDuplicated?: boolean;
};

const GachaDetailItemsSection = ({
  props: { itemFiles, benefit, totalCapacity, awardProbabilities, isDuplicated, isPreview },
}: {
  props: GachaDetailItemSectionProps;
}) => {
  const benefitItems = useMemo(
    () =>
      benefit?.benefit.filter((file: { conditionType: number; benefitFile: GachaBenefitFile }) => file.conditionType),
    [benefit],
  );

  const groupedItemsByAwardType = useGroupGachaItems(itemFiles);

  // TODO: 以下を一旦コメントアウト、追加ロジックがあるので

  // const { currentUser } = useCurrentUser();

  // const isButtonDisabled = useMemo(() => {
  //   return isPurchased || isCheckout;
  // }, [isPurchased, isCheckout]);

  // const buttonText = useMemo(() => {
  //   if (isPurchased) return '購入済み';
  //   if (isCheckout) return 'お支払い待ち';
  //   if (isSoldOut) return '売り切れ';
  //   return '購入';
  // }, [isPurchased, isCheckout, isSoldOut]);

  // const dialogTextBody = (
  //   <div className="grid items-center justify-center p-6 text-center">
  //     <p className="text-lg text-red-500">ただいま他の画面で決済手続き中です</p>
  //     <p> PayPayで決済を完了してください。</p>
  //     <p>５分以内に決済が行われない場合、</p>
  //     自動的に決済をキャンセルします。
  //   </div>
  // );
  // const handleOpenDialogWithMark = () => {
  //   onModalOpen();
  //   setModalProps({
  //     onClose: onModalClose,
  //     type: 'error',
  //     children: dialogTextBody,
  //   });
  // };

  // const gotoLoginPage = () => {
  //   const returnUrl = window.location.href;
  //   const loginUrl = `${process.env.NEXT_PUBLIC_OLD_FANME_API_HOST}/creators/auth/fanme?return_url=${encodeURIComponent(
  //     returnUrl,
  //   )}`;
  //   router.push(loginUrl);
  // };

  // ログインチェック
  // const checkLoginStatus = useCallback(
  //   () => {
  //     if (!currentUser) {
  //       // toast.custom((t) => CustomToast(t, 'error', '商品を購入するにはログインが必要です'), addCartFailedToastOption);
  //       setTimeout(() => gotoLoginPage(), 1500);
  //       return false;
  //     }
  //     return true;
  //   },
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  //   [currentUser],
  // );

  return (
    <>
      <div className="mb-4 mt-6 px-4">
        <div className="mb-4 flex items-center justify-between">
          <SectionTitleWithIcon title="ガチャ内容" icon="/images/gacha/icons/Gacha.svg" />
          <span className="text-medium-12 text-gray-550">合計容量: {bytesToMB(totalCapacity).toFixed(2)}MB</span>
        </div>
        {/* 内容 */}
        <div>
          {groupedItemsByAwardType.map((awardProb, idx) => {
            const items = awardProb;
            if (items.length === 0) return null;
            const { awardType, probability } = handleAwardProbability(items, awardProbabilities);

            return (
              <div key={idx} className="mb-5">
                <div className="mb-2.5 flex items-center justify-start gap-1.5">
                  <AwardProbabilityDisplay
                    awardType={awardType}
                    probability={probability}
                    isShowProbability={isDuplicated}
                  />
                </div>
                <div className="mb-2.5 flex flex-wrap items-center gap-3">
                  {items.map((item: GachaItemFile) => (
                    <div key={item.id} className="relative">
                      <img
                        src={
                          item.isSecret
                            ? '/shop/images/gacha/icons/Secret.svg'
                            : item.type === 'audio'
                              ? '/shop/images/voice.png'
                              : item.maskedThumbnailUri
                        }
                        alt={item.title}
                        width={52}
                        height={52}
                      />
                      {item.receivedFileCount !== undefined && item.receivedFileCount > 0 && !isPreview && (
                        <ShopPublicImage
                          src={'/images/gacha/icons/Finish.svg'}
                          alt="finish"
                          width={64}
                          height={64}
                          className="absolute inset-0 m-auto"
                        />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
          <div className="mt-4">
            <Instruction>
              *各賞に記載している出現率は各賞全体の出現率です。
              <br /> 各賞に含まれる景品の出現確率は均等確率となります。
              <br /> 例） S賞が10％、景品2点の場合、1点の確率は5%
              <br /> *確率は1回ごとに、各賞の当選確率にもとづいて抽選を行います。
            </Instruction>
          </div>
        </div>
        <div className="mt-3 grid place-items-center">
          <div className="w-full">
            {!!benefitItems?.length && (
              <div className="bg-yellow-50 px-3 pb-2 pt-3">
                <div className="mb-2 flex items-center justify-between">
                  <SectionTitleWithIcon title="特典" icon="/images/icons/Present.svg" className="text-orange-300" />
                </div>
                {benefit?.description && (
                  <div className="mb-2 whitespace-pre-wrap rounded-lg bg-white px-3 py-2 text-gray-800">
                    <ReadMoreButton description={benefit.description} />
                  </div>
                )}
                {!!benefitItems?.length && <BenefitItemList items={benefitItems} readOnly={true} />}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default GachaDetailItemsSection;
