'use client';
import React, { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useParams } from 'next/navigation';
import CheckBoxGroup from '@/components/atoms/checkboxGroup/checkbox-group';
import CheckBoxGroupItem from '@/components/atoms/checkboxGroup/checkbox-group-item';
import NumberInput from '@/components/atoms/inputs/number-input';
import ItemIcon from '@/components/atoms/itemIcon';
import { useExhibitsStore } from '@/store/useExhibit';
import { ITEM_MAX_PRICE, ITEM_MIN_PRICE } from '@/consts/inputLength';
import { isDigitalBundle } from '@/utils/itemTypes';
import { type SingleItem } from '@/types/shopItem';

const SingleItemSection = ({ item }: { item: SingleItem }) => {
  const { exhibits, isNew, setItemFiles, validateSinglePrice } = useExhibitsStore();
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { itemFiles = [] } = isDigitalBundle(exhibitItem) ? exhibitItem : {};
  const {
    control,
    setValue,
    formState: { dirtyFields },
  } = useForm<{ singleItemPrice: number }>({
    mode: 'onBlur',
    defaultValues: { singleItemPrice: item.price },
  });

  useEffect(() => {
    setValue('singleItemPrice', item.price || 0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [item.price]);

  const handleBlurSinglePrice = (value: string | undefined) => {
    if (dirtyFields.singleItemPrice) {
      const numberValue = Number(value);
      if (!Number.isNaN(numberValue)) {
        updateItemFiles(item.isSingleSale || false, numberValue);
      }
    }
  };

  const handleSingleSaleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    updateItemFiles(checked, item.price || 0);
  };

  const updateItemFiles = (checked: boolean, price: number) => {
    let tempFiles = [...itemFiles];
    tempFiles = tempFiles.map((tempFile) => {
      if (tempFile.id === item.id) {
        return { ...tempFile, isSingleSale: checked, price: price };
      }
      return tempFile;
    });
    setItemFiles(itemId, tempFiles);
  };

  return (
    <div className="flex items-center">
      <CheckBoxGroupItem
        id={item.title}
        checked={item.isSingleSale}
        onCheckbox={handleSingleSaleChange}
        disabled={!isNew(itemId)}
      />
      <div className="flex w-full items-start justify-between">
        <ItemIcon
          thumbnail={item.preSignedThumbnailUrl || item.thumbnail!}
          thumbnailRatio={1}
          title={item.title}
          size={72}
          radius="lg"
        />
        <div className="ml-3 flex w-full flex-col items-start">
          <p className="mb-2 w-full text-left text-regular-13">{item.title}</p>
          <div className="flex w-full items-center gap-2">
            {item.isSingleSale && (
              <>
                <div className="flex w-8 flex-col items-center text-regular-11">
                  <span>価格</span>
                  <span className="text-regular-9">(税込)</span>
                </div>
                <NumberInput
                  control={control}
                  inputName="singleItemPrice"
                  prefix="¥"
                  min={ITEM_MIN_PRICE}
                  max={ITEM_MAX_PRICE}
                  shortInput={true}
                  className="col-span-2 !mb-0 w-full min-w-38"
                  inputClassName="text-bold-22 !min-w-38"
                  defaultValue={item.price}
                  coverValue={item.price || 0}
                  error={!validateSinglePrice(itemId, item)}
                  errorMsg={`金額は${ITEM_MIN_PRICE.toLocaleString()}円以上、${(exhibitItem?.priceSet || 0) > 0 ? `出品価格(${exhibitItem?.priceSet.toLocaleString()}円)` : `${ITEM_MAX_PRICE.toLocaleString()}円`}以下で指定してください`}
                  onBlur={handleBlurSinglePrice}
                />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const SingleSaleSection = ({ defaultChecked, defaultPrice }: { defaultChecked: boolean; defaultPrice: number }) => {
  const useExhibits = useExhibitsStore();
  const { exhibits, isNew, setItemFiles, setSinglePrice, validateSingleSale } = useExhibits;
  const params = useParams();
  const itemId = params.id as string;
  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);
  const { itemFiles = [] } = isDigitalBundle(exhibitItem) ? exhibitItem : {};
  const [singleSaleAllChecked, setSingleSaleAllChecked] = useState<boolean>(defaultChecked);
  const singlePrice = isDigitalBundle(exhibitItem) ? (exhibitItem?.singlePrice ?? 0) : 0;
  const {
    control,
    formState: { dirtyFields },
    watch,
  } = useForm<{ singlePrice: number }>({
    mode: 'onBlur',
    defaultValues: {
      singlePrice: defaultPrice,
    },
  });

  const handleSetAllChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const isChecked = e.target.checked;
    setSingleSaleAllChecked(isChecked);
    updateAllItemFiles(isChecked, undefined);
  };

  useEffect(() => {
    if (setSinglePrice) {
      setSinglePrice(itemId, watch('singlePrice'));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watch('singlePrice')]);

  const handleBlurSetAllSinglePrice = (value: string | number | undefined) => {
    if (dirtyFields.singlePrice) {
      const numberValue = Number(value);
      if (!Number.isNaN(numberValue)) {
        updateAllItemFiles(undefined, numberValue);
      }
    }
  };

  const updateAllItemFiles = (checked: boolean | undefined, price: number | undefined) => {
    let tempFiles = [...itemFiles];
    tempFiles = tempFiles.map((tempFile) => {
      if ('isSingleSale' in tempFile)
        return {
          ...tempFile,
          isSingleSale: checked === undefined ? tempFile.isSingleSale : checked,
          price: price === undefined ? tempFile.price : price,
        };
      return tempFile;
    });
    setItemFiles(itemId, tempFiles);
  };

  return (
    <div>
      <div className="mt-4 grid h-14 grid-cols-3 items-center gap-x-6 rounded-lg bg-navy-50 pl-4 pr-2">
        <span className="col-span-1 text-medium-13">単品商品の金額一括設定</span>
        <NumberInput
          control={control}
          inputName="singlePrice"
          suffix="（税込）"
          prefix="¥"
          min={ITEM_MIN_PRICE}
          max={exhibitItem?.priceSet || ITEM_MAX_PRICE}
          className="col-span-2 !mb-0"
          inputClassName="text-bold-22 bg-white"
          defaultValue={singlePrice}
          coverValue={singlePrice}
          // 一括設定の方はエラーメッセージを表示しない（単品商品の方で表示するため、ただし入力値のチェックだけはする）
          error={isNew(itemId) ? !validateSingleSale(itemId) : false}
          onBlur={handleBlurSetAllSinglePrice}
        />
      </div>
      <p className="mb-2 mt-6 text-medium-14">販売を有効にする商品を選択</p>
      {!validateSingleSale(itemId) && (
        <p className="mb-3 w-full text-left text-regular-10 text-error">単品販売をするには1つ以上選択が必要です</p>
      )}
      <CheckBoxGroup>
        <CheckBoxGroupItem
          id="checkAllSingle"
          label="全ての商品を選択"
          className="mb-4"
          checked={singleSaleAllChecked}
          onCheckbox={handleSetAllChange}
          name="setSingleSalePrice"
          disabled={!isNew(itemId)}
        />
      </CheckBoxGroup>
      <CheckBoxGroup direction="column" className="justify-start">
        {itemFiles.map((file) => {
          return <SingleItemSection key={file.title} item={file} />;
        })}
      </CheckBoxGroup>
    </div>
  );
};

export default SingleSaleSection;
