'use client';

import { useEffect, useMemo, useRef } from 'react';
import { sendGTMEvent } from '@next/third-parties/google';
import clsx from 'clsx';
import { useRouter } from 'next/navigation';
import Button from '@/components/atoms/button';
import ValidationErrorMessages from '@/components/containers/ValidationErrorMessages';
import FixedBar from '@/components/layouts/FixedBar';
import { FeatureFlags } from '@/lib/feature';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useExhibitsStore } from '@/store/useExhibit';
import ChekiExhibitItemForm from './cheki-exhibit-item-form';
import DigitalBundleItemForm from './digital-bundle-item-form';
import DigitalGachaItemForm from './digital-gacha-item-form';
import { THUMBNAIL_BLUR_LEVEL, THUMBNAIL_WATERMARK_LEVEL } from '@/consts/file';
import { useIsFixedAtBottom } from '@/hooks/useIsFixedAtBottom';
import { isDigitalBundle, isDigitalGacha, isPhysicalItem } from '@/utils/itemTypes';
import { generatePreSignedThumbnails, generateProcessedThumbnail } from '@/utils/thumbnail';
import { DigitalBundle, ExhibitChekiItem, ExhibitGachaItem } from '@/types/exhibitItem';
import { GachaBenefitFile } from '@/types/gacha';
import { ITEM_TYPE } from '@/types/item';
import { ExhibitType, SingleItem } from '@/types/shopItem';

type ExhibitItemFormProps = {
  itemParams: {
    itemId: string;
    itemType: ExhibitType;
  };
  identityId: string;
  pageData?: DigitalBundle | ExhibitGachaItem | ExhibitChekiItem;
  shopLimitation: ShopLimitation;
  isEdit?: boolean;
};

const ExhibitItemForm = ({ itemParams, identityId, pageData, shopLimitation, isEdit }: ExhibitItemFormProps) => {
  const midDiv = useRef<HTMLDivElement>(null);
  const isFixed = useIsFixedAtBottom(midDiv);
  const router = useRouter();
  const { itemId, itemType } = itemParams;

  const {
    exhibits,
    isConfirmed,
    setIsConfirmed,
    setItemFiles,
    setThumbnail,
    setThumbnailType,
    setThumbnailRatio,
    setSamples,
    setBenefits,
    setTitle,
    setDescription,
    setTags,
    setPriceSet,
    setSingleSale,
    setSingleSaleDefault,
    setSinglePrice,
    setLimited,
    setLimitedPerUser,
    setPeriod,
    setDiscount,
    setPassword,
    setAvailable,
    addExhibit,
    setAwardProbability,
    setThumbnailCustomImage,
    setIsDuplicated,
    validateExhibitItem,
  } = useExhibitsStore();

  const exhibitItem = useMemo(() => exhibits.find((e) => e.itemId === itemId), [exhibits, itemId]);

  useEffect(() => {
    const initializeItem = async () => {
      if (pageData && !exhibitItem) {
        addExhibit(itemId, itemType);
        const isEditableItemType = isDigitalBundle(pageData) || isDigitalGacha(pageData);
        if (isEditableItemType && pageData.itemFiles) {
          try {
            let resultFiles: SingleItem[] = pageData.itemFiles;
            // 署名付きURLを取得
            if (!resultFiles.every((file) => file.preSignedThumbnailUrl)) {
              resultFiles = await generatePreSignedThumbnails(resultFiles, identityId);
              const filePromises = resultFiles.map(async (file) => {
                if (file.processedThumbnail) return file;
                return await generateProcessedThumbnail(
                  file,
                  file.selected ? pageData.thumbnailBlurLevel : THUMBNAIL_BLUR_LEVEL.NONE,
                  file.selected ? pageData.thumbnailWatermarkLevel : THUMBNAIL_WATERMARK_LEVEL.WHITE,
                );
              });
              resultFiles = await Promise.all(filePromises);
              setItemFiles(itemId, resultFiles);
            }
          } catch (error) {
            console.error('Failed to fetch signed URLs or process thumbnails:', error);
          }
        }

        if (pageData.thumbnail) setThumbnail(itemId, pageData.thumbnail);
        if (pageData.thumbnailType) setThumbnailType(itemId, pageData.thumbnailType);
        if (pageData.thumbnailBlurLevel) setThumbnailRatio(itemId, pageData.thumbnailBlurLevel);
        if (pageData.thumbnailWatermarkLevel) setThumbnailRatio(itemId, pageData.thumbnailWatermarkLevel);
        if (pageData.thumbnailType === 'custom') setThumbnailCustomImage(itemId, pageData.thumbnailCustomImage);
        if (pageData.thumbnailRatio) setThumbnailRatio(itemId, pageData.thumbnailRatio);
        if (pageData.samples) setSamples(itemId, pageData.samples);
        if (pageData.benefits) {
          const updatedBenefits = await Promise.all(
            pageData.benefits.map(async (benefit) => {
              let resultFiles: SingleItem[] | GachaBenefitFile[] = benefit.benefitFiles;
              // 署名付きURLを取得
              if (!resultFiles.every((file) => file.preSignedThumbnailUrl)) {
                resultFiles = await generatePreSignedThumbnails(resultFiles, identityId);
                return { ...benefit, benefitFiles: resultFiles };
              }
              return benefit;
            }),
          );
          setBenefits(itemId, updatedBenefits);
        }
        if (pageData.title) setTitle(itemId, pageData.title);
        if (pageData.description) setDescription(itemId, pageData.description);
        if (pageData.tags) setTags(itemId, pageData.tags);
        if (pageData.priceSet) setPriceSet(itemId, pageData.priceSet);
        if (isDigitalBundle(pageData) && pageData.singleSale !== undefined) {
          setSingleSale(itemId, pageData.singleSale);
          setSingleSaleDefault(itemId, pageData.singleSale);
        }
        if (isDigitalGacha(pageData) && pageData.awardProbabilities !== undefined) {
          setAwardProbability(itemId, pageData.awardProbabilities);
        }
        if (isDigitalBundle(pageData) && pageData.singlePrice) setSinglePrice(itemId, pageData.singlePrice);
        if (pageData.limited) setLimited(itemId, pageData.limited);
        if (isPhysicalItem(pageData) && pageData.limitedPerUser) setLimitedPerUser(itemId, pageData.limitedPerUser);
        if (pageData.period) setPeriod(itemId, pageData.period);
        if (pageData.discount) setDiscount(itemId, pageData.discount);
        if (!isDigitalGacha(pageData) && pageData.password) setPassword(itemId, pageData.password);
        if (isDigitalGacha(pageData) && pageData.isDuplicated !== undefined)
          setIsDuplicated(itemId, pageData.isDuplicated);
        if (pageData.available !== undefined) setAvailable(itemId, pageData.available);
      }
    };
    initializeItem();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pageData]);

  useEffect(() => {
    if (!exhibitItem) return;
    if (validateExhibitItem(itemId)) setIsConfirmed(true);
    else setIsConfirmed(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [exhibitItem, itemId, validateExhibitItem, setIsConfirmed]);

  const formComponent = (() => {
    switch (itemType) {
      case ITEM_TYPE.CHEKI.str:
        return FeatureFlags.cheki() ? (
          <ChekiExhibitItemForm shopLimitation={shopLimitation} />
        ) : (
          <DigitalBundleItemForm shopLimitation={shopLimitation} />
        );
      case ITEM_TYPE.DIGITAL_GACHA.str:
        return FeatureFlags.gacha() ? (
          <DigitalGachaItemForm shopLimitation={shopLimitation} isEdit={isEdit} />
        ) : (
          <DigitalBundleItemForm shopLimitation={shopLimitation} />
        );
      default:
        return <DigitalBundleItemForm shopLimitation={shopLimitation} />;
    }
  })();

  return (
    <div className={clsx('relative flex-1', { ['pb-18']: !isFixed })} ref={midDiv}>
      {formComponent}

      <ValidationErrorMessages />

      <FixedBar isFixed={isFixed}>
        <Button
          buttonType={isConfirmed ? 'dark' : 'disabled'}
          disabled={!isConfirmed}
          onClick={() => {
            sendGTMEvent({ event: 'fanme_listing_item_check', user_name: identityId });
            router.push(`/@${identityId}/item/${itemId}/confirm?item_type=${itemType}`);
          }}
        >
          出品確認
        </Button>
      </FixedBar>
    </div>
  );
};

export default ExhibitItemForm;
