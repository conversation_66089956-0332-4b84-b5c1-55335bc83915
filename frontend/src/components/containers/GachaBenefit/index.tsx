'use client';

import Button from '@/components/atoms/button';
import SectionTitleWithNumber from '@/components/atoms/typography/section-title-with-number';
import ShopPublicImage from '@/components/ShopImage';
import WarningMessage from '@/components/atoms/typography/warning-message';
import { ShopLimitation } from '@/lib/server-api/shop-api.schemas';
import { useInstructionModalStore } from '@/store/useInstructionModal';
import BenefitItem from './benefit-item';
import { useGachaBenefit } from '@/hooks/use-gacha-benefit';

type GachaBenefitProps = {
  numbering: string;
  shopLimitation: ShopLimitation;
};

const MODAL_CONFIG = {
  header: '購入特典の設定',
  imageSrc: '/images/gacha/benefitgacha.webp',
  content:
    '購入特典の条件と特典を登録でき、購入者が条件を満たすと受け取れます。特典を付けると、売上金額が上がりやすくなりますので、上手に活用しましょう。',
} as const;

const GachaBenefit = ({ numbering, shopLimitation }: GachaBenefitProps) => {
  const {
    benefitItems,
    textLengths,
    control,
    handleConditionChange,
    handleDescriptionChange,
    handleFileUpload,
    handleFileDelete,
    handleUploadProgress,
    deleteBenefit,
    getAvailableConditions,
  } = useGachaBenefit();

  const { openInstructionModal } = useInstructionModalStore();

  const handleOpenIntroductionModal = () => {
    const popupContent = (
      <div className="flex items-center justify-center gap-1 bg-navy-50 py-5">
        <ShopPublicImage src={MODAL_CONFIG.imageSrc} width={241} height={168} alt="modal" />
      </div>
    );

    openInstructionModal(MODAL_CONFIG.header, popupContent, MODAL_CONFIG.content);
  };

  return (
    <section className="section-double-border py-4">
      <div className="px-4">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center justify-start gap-2">
            <SectionTitleWithNumber title="ガチャ特典の設定" numbering={numbering} className="!mb-0" />
            <WarningMessage message="出品後は変更できません" />
          </div>
          <Button buttonType="light-small" buttonShape="circle" buttonSize="xxs" onClick={handleOpenIntroductionModal}>
            <ShopPublicImage src="/images/icons/Help.svg" width={12} height={12} alt="help" />
          </Button>
        </div>

        {/* 特典項目一覧 */}
        <div className="space-y-6">
          {benefitItems.map((benefitItem, index) => (
            <BenefitItem
              key={`benefit-${benefitItem.id}-${index}`}
              benefitId={benefitItem.id || 0}
              index={index}
              condition={benefitItem.conditionType}
              description={benefitItem.description || ''}
              benefitFiles={benefitItem.benefitFiles}
              uploadProgress={benefitItem.uploadProgress}
              availableConditions={getAvailableConditions(benefitItem.id || 0)}
              textLength={textLengths[benefitItem.id || 0] || 0}
              canDelete={benefitItem.benefitFiles && benefitItem.benefitFiles.length > 0}
              isLastItem={index === benefitItems.length - 1}
              control={control}
              shopLimitation={shopLimitation}
              onConditionChange={handleConditionChange}
              onDescriptionChange={handleDescriptionChange}
              onFileUpload={handleFileUpload}
              onFileDelete={handleFileDelete}
              onDelete={deleteBenefit}
              onUploadProgress={handleUploadProgress}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default GachaBenefit;
