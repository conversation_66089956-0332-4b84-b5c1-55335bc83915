import React from 'react';
import StateBadge from '@/components/atoms/badges/state-badge';
import ShopPublicImage from '@/components/ShopImage';

const BenefitBadge = () => {
  return (
    <StateBadge type="square-radius-filled" color="pink" size="sm" className="absolute bottom-2.5 right-2.5">
      <ShopPublicImage src={'/images/icons/Benefit.svg'} alt="benefit" width={15} height={15} className="ml-[4px]" />
      <span className="m-[3px]">特典付き</span>
    </StateBadge>
  );
};

export default BenefitBadge;
