import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { FeatureFlags } from '@/lib/feature';
import { getFanmeToken } from '@/app/actions/user';

const COOKIE_MAX_AGE = 365 * 24 * 60 * 60; // 1年
// Protected routes that require authentication
const protectedRoutesRegex: RegExp[] = [
  /^\/create$/, // /create/[path]
  /^\/@[^/]+\/cart(?:\/.*)?$/, // /[identityId]/cart
  /^\/@[^/]+\/order(?:\/.*)?$/, // /[identityId]/order
  /^\/@[^/]+\/order\/success$/, // /[identityId]/order/success
  /^\/@[^/]+\/payment(?:\/.*)?$/, // /[identityId]/payment

  /^\/@[^/]+\/edit$/, // /[identityId]/edit
  /^\/@[^/]+\/item\/[^/]+\/edit$/,
  /^\/@[^/]+\/item\/[^/]+\/create$/,
  /^\/@[^/]+\/item\/[^/]+\/preview$/,
  /^\/@[^/]+\/item\/[^/]+\/confirm$/,
  /^\/@[^/]+\/viewer(?:\/.*)?$/,
  /^\/@[^/]+\/purchased-item(?:\/.*)?$/,
];

const redirectRootRegex = /^\/[a-zA-Z0-9._]+(?:\/[^/]+)*$/;
// @にリダイレクトしないパス
const excludedRoots = [
  '/create',
  '/comps',
  '/images',
  '/movies',
  '/api',
  '/_next',
  '/favicon.ico',
  '/hc',
  '/shop/hc',
  '/payment',
  '/tokushoho',
  '/address/edit',
];

export async function middleware(req: NextRequest) {
  // TODO: チェキリリース時に削除する
  if (!FeatureFlags.cheki()) {
    if (req.nextUrl.pathname.startsWith('/address/edit')) {
      return NextResponse.redirect(new URL('/', req.url));
    }
  }

  const response = NextResponse.next();
  const paypayCallbackTokenKey = 'paypay_callback_token';
  const paypayCallbackToken = req.cookies.get(paypayCallbackTokenKey)?.value;

  if (paypayCallbackToken !== undefined) {
    setCookie(response, 'fanme_token', paypayCallbackToken, false);
    setCookie(response, 'fanme_token-http-only', paypayCallbackToken, true);
    deleteCookie(response, paypayCallbackTokenKey);
    return response;
  }

  // BASIC認証
  if (
    process.env.APP_ENV !== 'production' &&
    process.env.APP_ENV !== 'development' &&
    !req.nextUrl.pathname.endsWith('/hc')
  ) {
    const authHeader = req.headers.get('authorization');
    const basicAuth = authHeader?.split(' ')[1];
    const credentials = Buffer.from(basicAuth || '', 'base64').toString();
    const [user, pass] = credentials.split(':');

    if (user !== process.env.BASIC_AUTH_USER || pass !== process.env.BASIC_AUTH_PASS) {
      const response = new NextResponse('Unauthorized', { status: 401 });
      response.headers.set('WWW-Authenticate', 'Basic realm="Secure Area"');
      return response;
    }
  }

  const { pathname, search } = req.nextUrl;
  // @マークがない場合はリダイレクト
  if (!excludedRoots.some((route) => pathname.startsWith(route)) && redirectRootRegex.test(pathname)) {
    const host = process.env.NEXT_PUBLIC_FRONT_API_BASE_URL || 'http://host.docker.internal:27002';
    const redirectPath = `/shop/@${pathname.replace(/^\//, '')}${search}`;
    return NextResponse.redirect(new URL(redirectPath, host));
  }

  // 認証後にCookieにトークンがセットされていない状態で帰ってきたら、トークンを取得してCookieにセット
  const authQuery = req.nextUrl.searchParams.get('authorize');
  if (authQuery) {
    const redirectUrl = new URL(req.nextUrl.toString());
    redirectUrl.searchParams.delete('authorize'); // クエリパラメータ 'authorize' を削除
    const response = NextResponse.redirect(redirectUrl);

    // トークン取得
    // TODO トークン取得失敗したら再度認証ページにリダイレクト
    const result = await getFanmeToken();

    const { data: token, error } = await result.json();
    if (error) {
      return response;
    }

    if (token) {
      setCookie(response, 'fanme_token', token, false);
      setCookie(response, 'fanme_token-http-only', token, true);
    }
    return response;
  }

  const token = cookies().get('fanme_token')?.value;
  const httpOnlyToken = cookies().get('fanme_token-http-only')?.value;
  if (token && !httpOnlyToken) {
    setCookie(response, 'fanme_token-http-only', token, true);
    return response;
  } else if (httpOnlyToken && !token) {
    setCookie(response, 'fanme_token', httpOnlyToken, false);
    return response;
  }

  // 認証が必要なパスにトークンなしでアクセスした場合、認証ページにリダイレクト
  if (protectedRoutesRegex.some((route) => route.test(pathname)) && !httpOnlyToken) {
    const host = process.env.NEXT_PUBLIC_FRONT_API_BASE_URL || 'http://host.docker.internal:27002';
    const returnToUrl = new URL(`/shop${pathname.replace(/(\??)authorize=true(&?)/gm, '$1')}`, host).toString();
    const redirectUrl = new URL('/shop/api/fanme/users/auth', host);
    redirectUrl.searchParams.set('return_url', returnToUrl);
    return NextResponse.redirect(redirectUrl);
  }
  return NextResponse.next();
}

const setCookie = (response: NextResponse, key: string, value: string, isHttpOnly: boolean) => {
  response.cookies.set(key, value, {
    httpOnly: isHttpOnly,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: COOKIE_MAX_AGE,
  });
};

const deleteCookie = (response: NextResponse, key: string) => {
  const host = process.env.NEXT_PUBLIC_FRONT_API_BASE_URL || 'http://host.docker.internal:27002';
  const frameHost = new URL(host).hostname;

  // ドメインの前にピリオドを追加
  const domain = `.${frameHost}`;
  response.cookies.set(key, '', {
    expires: new Date(0),
    domain: domain,
  });
};

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - images, videos, audio (media files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    '/((?!api|_next/static|_next/image|images|videos|audio|favicon.ico|sitemap.xml|robots.txt).*)',
  ],
};
