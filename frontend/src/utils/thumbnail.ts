import { blurImageData } from '@kayahr/stackblur';
import { ITEM_FILENAME_MAX_LENGTH } from '@/consts/inputLength';
import { MAX_BLOB_SIZE } from '@/consts/sizes';
import { fileService } from '@/services/file';
import { compressImage } from '@/utils/base';
import { getS3KeyFromUrl } from '@/utils/file';
import { GachaBenefitFile } from '@/types/gacha';
import { MediaType, SingleItem } from '@/types/shopItem';

// ユーティリティ関数：dataURLをBlobに変換する
const dataURLToBlob = async (dataurl: string, mimeType?: string) => {
  //@ts-ignore
  const type = mimeType || dataurl.match(/data:(.+);/) ? dataurl.match(/data:(.+);/)[1] : '';

  const base64 = dataurl.split(',')[1];
  const binStr = atob(base64);
  const u8a = new Uint8Array(binStr.length);
  let p = binStr.length;

  while (p) {
    p--;
    //@ts-ignore
    u8a[p] = binStr.codePointAt(p);
  }

  // Blobの生成
  let blob = new Blob([u8a], { type: mimeType || type });

  // 画像タイプかつサイズが500KBより大きい場合は圧縮を試みる
  if (type.startsWith('image/') && blob.size > MAX_BLOB_SIZE) {
    blob = await compressImage(blob);
  }

  return blob;
};

const anyUrlToBlob = async (url: string): Promise<Blob> => {
  //　data:imageを処理する
  if (url.startsWith('data:')) {
    return dataURLToBlob(url);
  }

  // http(s)、ローカルファイルURLを処理する
  try {
    const response = await fetch(url, { cache: 'no-store' });
    return await response.blob();
  } catch (error) {
    console.error(JSON.stringify({ error }));
    throw error;
  }
};

const fileToBase64 = async (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
};

// ユーティリティ関数：遅延実行
const sleep = (waitSeconds: number): Promise<void> => {
  return new Promise((resolve) => {
    setTimeout(resolve, waitSeconds);
  });
};

// 画像ファイルを処理する
const handleImage = async (mediaItem: SingleItem, uploadUrl: string, originalUrl?: string): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem }; // readOnlyのためコピーを生成して更新
  if (uploadUrl) {
    newMediaItem.src = originalUrl || uploadUrl;
    newMediaItem.thumbnail = uploadUrl;
    return newMediaItem;
  }
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      newMediaItem.src = e.target?.result as string;
      newMediaItem.thumbnail = mediaItem.src;
      resolve(newMediaItem);
    };
    reader.readAsDataURL(newMediaItem.file as Blob);
  });
};
// 動画ファイルを処理する
const handleVideo = async (mediaItem: SingleItem, uploadUrl: string, isPublic?: boolean): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem }; // readOnlyのためコピーを生成して更新
  const localUrl = URL.createObjectURL(mediaItem.file as Blob);
  const video = document.createElement('video');
  const canvas = document.createElement('canvas');

  video.src = localUrl;
  video.preload = 'auto';
  video.muted = true;
  video.autoplay = true;
  video.currentTime = 1;
  video.crossOrigin = 'anonymous';
  video.style.position = 'fixed';
  video.style.zIndex = '-1';
  video.load();
  try {
    await new Promise<void>((resolve, reject) => {
      video.addEventListener('loadedmetadata', () => {
        video.onseeked = () => resolve();
      });
      video.onerror = reject;
    });
    await sleep(500);
    video.pause();
    const ctx = canvas.getContext('2d');

    if (ctx) {
      // 動画のアスペクト比を計算する
      const videoRatio =
        video.videoWidth > video.videoHeight
          ? video.videoWidth / video.videoHeight
          : video.videoHeight / video.videoWidth;
      const isWgth = video.videoWidth > video.videoHeight;

      // サムネイルを生成する (750x750)
      canvas.width = 750;
      canvas.height = 750;
      const videoWidth = isWgth ? canvas.width * videoRatio : canvas.width;
      const videoHeight = isWgth ? canvas.height : canvas.height * videoRatio;
      ctx.drawImage(video, 0, 0, videoWidth, videoHeight);
      const videoThumbnail = canvas.toDataURL('image/jpeg');

      const videoThumbnailUrl = await uploadThumbnail({
        img: videoThumbnail,
        fileId: mediaItem.id,
        isPublic: isPublic,
      });

      newMediaItem.thumbnail = videoThumbnailUrl.url;

      // フルサイズのサムネイルを生成する
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      const newFile = canvas.toDataURL('image/jpeg');
      const compressedFile = (await compressImage(newFile as unknown as File)) as File;
      newMediaItem.fullThumbnail = compressedFile as unknown as string;
    }

    newMediaItem.duration = video.duration;
    newMediaItem.src = uploadUrl ? uploadUrl : localUrl;

    return newMediaItem;
  } finally {
    setTimeout(() => {
      URL.revokeObjectURL(localUrl);
      video.remove();
      canvas.remove();
    }, 1000);
  }
};

// オーディオファイルを処理する
const handleAudio = async (mediaItem: SingleItem, uploadUrl: string): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem }; // readOnlyのためコピーを生成して更新
  const localUrl = URL.createObjectURL(mediaItem.file as Blob);
  const audio = document.createElement('audio');

  audio.src = localUrl;
  audio.preload = 'auto';

  try {
    await new Promise<void>((resolve, reject) => {
      audio.addEventListener('loadedmetadata', () => {
        resolve();
      });
      audio.onerror = reject;
    });
    newMediaItem.duration = audio.duration;
    newMediaItem.src = uploadUrl ? uploadUrl : localUrl;
    newMediaItem.thumbnail = '/shop/images/voice.png';
    return newMediaItem;
  } catch (error) {
    console.error(JSON.stringify({ error }));
    return newMediaItem;
  } finally {
    setTimeout(() => {
      URL.revokeObjectURL(localUrl);
      audio.remove();
    }, 1000);
  }
};

// メディアサムネイルを生成するメイン関数
// - 前処理（ローディングを表示するためのsrc=loadingのSingleItemを生成する）
const generatePreMediaThumbnail = (file: File): SingleItem => {
  const fileType = file.type.split('/')[0] as MediaType;
  return {
    file,
    type: fileType,
    src: '',
    thumbnail: '',
    title: file.name.replace(/\.[^/.]+$/, '').slice(0, ITEM_FILENAME_MAX_LENGTH), // 拡張子を除去
    id: `${file.name}-${Date.now()}`, // ユニークなIDを日付を足すことで生成
    size: file.size,
    isLoading: true,
  };
};
// - アップロード処理が終われば実際のsrcを更新する
const generateMediaThumbnail = async (
  mediaItem: SingleItem,
  fileUrl: string,
  originalUrl?: string,
  isPublic?: boolean,
): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem };
  newMediaItem.isLoading = false;
  switch (mediaItem.type) {
    case 'image':
      return await handleImage(newMediaItem, fileUrl, originalUrl);
    case 'video':
      return await handleVideo(newMediaItem, fileUrl, isPublic);
    case 'audio':
      return await handleAudio(newMediaItem, fileUrl);
    default:
      throw new Error('Unsupported media type');
  }
};

const uploadThumbnail = async (data: {
  img: string | File;
  fileId: string;
  fileName?: string;
  existUploadUrl?: string;
  isCover?: boolean;
  isPublic?: boolean;
}) => {
  try {
    let thumbnailFile = data.img;
    if (typeof data.img === 'string') {
      const blob = await anyUrlToBlob(data.img);

      thumbnailFile = new File([blob], data.fileName || `temp_${data.fileId}`, { type: blob.type });
    }
    const uploadUrl = data.existUploadUrl
      ? data.existUploadUrl
      : await fileService
          .getUploadUrl({
            metadataList: [{ id: data.fileId, name: data.fileName || null }],
            isCover: data.isCover,
            isPublic: data.isPublic,
          })
          .then((res) => res.url);

    if (!uploadUrl) {
      throw new Error('Failed to get upload URL');
    }
    await fileService.uploadFileToS3(uploadUrl, thumbnailFile as File);
    return { id: data.fileId, uploadUrl, url: uploadUrl.split('?')[0] };
  } catch (error) {
    console.error(JSON.stringify({ error }));
    throw error;
  }
};

const generateProcessedImageString = async (src: string, blurLevel: number, watermarkLevel: number) => {
  const canvas = document.createElement('canvas');
  try {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 画像を読み込む
    const img = new window.Image();
    img.crossOrigin = 'anonymous';
    img.src = src;
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
    });

    // --- 画像のリサイズ処理 ---
    const size = Math.min(img.width, img.height);
    const sx = (img.width - size) / 2;
    const sy = (img.height - size) / 2;
    const targetSize = 352;
    canvas.width = targetSize;
    canvas.height = targetSize;

    // **ぼかし用の CSS フィルタ適用**
    ctx.drawImage(img, sx, sy, size, size, 0, 0, targetSize, targetSize);

    const radius = 10 * blurLevel;
    const imageData = ctx.getImageData(0, 0, targetSize, targetSize);
    blurImageData(imageData, radius, true);
    ctx.putImageData(imageData, 0, 0);

    if (watermarkLevel === 0) {
      return canvas.toDataURL('image/jpeg');
    }

    // **ウォーターマークを適用**
    const watermarkImg = new window.Image();
    watermarkImg.crossOrigin = 'anonymous';
    watermarkImg.src =
      watermarkLevel === 1 ? '/shop/images/icons/setting/MaskWhite.svg' : '/shop/images/icons/setting/MaskBlack.svg';
    await new Promise((resolve, reject) => {
      watermarkImg.onload = resolve;
      watermarkImg.onerror = reject;
    });

    ctx.drawImage(watermarkImg, 0, 0, targetSize, targetSize);

    return canvas.toDataURL('image/jpeg');
  } catch (error) {
    console.error(JSON.stringify(error));
  } finally {
    canvas.remove();
  }
};

const generateProcessedThumbnail = async (
  mediaItem: SingleItem,
  blurLevel: number,
  watermarkLevel: number,
): Promise<SingleItem> => {
  const newMediaItem = { ...mediaItem };
  if (!mediaItem.thumbnail || mediaItem.type == 'audio' || !mediaItem.preSignedThumbnailUrl) return newMediaItem;
  const processedThumbnailStr = await generateProcessedImageString(
    mediaItem.preSignedThumbnailUrl,
    blurLevel,
    watermarkLevel,
  );
  if (processedThumbnailStr) {
    const blob = await anyUrlToBlob(processedThumbnailStr);
    const thumbnailFile = new File([blob], mediaItem.title, { type: blob.type });
    newMediaItem.blur = blurLevel;
    newMediaItem.watermark = watermarkLevel;
    newMediaItem.processedThumbnail = URL.createObjectURL(thumbnailFile);
  }
  return newMediaItem;
};

const generatePreSignedThumbnails = async (mediaItems: SingleItem[] | GachaBenefitFile[], identityId: string) => {
  // 署名が必要なファイル
  const filesNeedToBeSigned = mediaItems.filter(
    (file) => !!file.thumbnail && file.type !== 'audio' && !isPublicBucketUrl(file.thumbnail),
  );
  let preSignedUrlsMap: Record<string, string> = {};
  if (filesNeedToBeSigned.length > 0) {
    try {
      const preSignedUrls = await fileService.getPreSignedUrl(
        identityId,
        filesNeedToBeSigned.map((file) => ({
          id: file.id,
          key: getS3KeyFromUrl(file.thumbnail!),
        })),
      );
      preSignedUrlsMap = Object.fromEntries(preSignedUrls.map(({ id, url }) => [id, url]));
    } catch (error) {
      console.error(JSON.stringify({ error }));
    }
  }
  return mediaItems.map((file) => ({
    ...file,
    // サムネイルがパブリックになっている場合はpreSignedThumbnailUrlにそのままurlをセット
    preSignedThumbnailUrl: preSignedUrlsMap[file.id] || file.thumbnail,
  }));
};

const isPublicBucketUrl = (url: string) => {
  try {
    const parsedUrl = new URL(url);
    const pathSegments = parsedUrl.pathname.split('/').filter(Boolean);
    return pathSegments.includes('public');
  } catch {
    return false;
  }
};

export {
  generateMediaThumbnail,
  generateProcessedThumbnail,
  dataURLToBlob,
  anyUrlToBlob,
  uploadThumbnail,
  fileToBase64,
  generatePreMediaThumbnail,
  generatePreSignedThumbnails,
};
