import type { File, ItemFiles } from '@/lib/client-api/client-shop-api.schemas';
import type {
  CreateDigitalGachaItemRequest,
  UpdateDigitalGachaItemRequest,
} from '@/lib/client-api/client-shop-api.schemas';
import type { FileForPullDigitalGachaItems } from '@/lib/fanme-api/fanme-api.schemas';
import type { Item } from '@/lib/fanme-api/fanme-api.schemas';
import { PROBABILITY_PATTERNS, type ProbabilityPatternKey } from '@/consts/gacha-data';
import { GACHA_PATTERN_KEYS, GACHA_MIN_PROBABILITY } from '@/consts/gacha-data';
import { formatDateTime } from '@/utils/base';
import type { ExhibitGachaItem } from '@/types/exhibitItem';
import { Award, AWARD_TYPE } from '@/types/gacha';
import type { GachaItemFile, AwardProbability } from '@/types/gacha';
import { ITEM_TYPE } from '@/types/item';

// Helper to get award hierarchy (e.g., for SA pattern: [4, 3])
const getAwardsInPattern = (patternKey: ProbabilityPatternKey): Award[] => {
  const pattern = PROBABILITY_PATTERNS[patternKey];
  // Object.keys returns string[], so we map them to numbers, then cast to Award[]
  return (Object.keys(pattern) as string[])
    .map(Number)
    .filter((awardValue: number): awardValue is Award => {
      // Type guard for Award
      const award = awardValue as Award;
      // Filter based on pattern definition, ensuring only relevant awards for the pattern type are included
      if (patternKey === GACHA_PATTERN_KEYS.PATTERN_1) return award === AWARD_TYPE.S;
      if (patternKey === GACHA_PATTERN_KEYS.PATTERN_2) return award === AWARD_TYPE.S || award === AWARD_TYPE.A;
      if (patternKey === GACHA_PATTERN_KEYS.PATTERN_3)
        return award === AWARD_TYPE.S || award === AWARD_TYPE.A || award === AWARD_TYPE.B;
      if (patternKey === GACHA_PATTERN_KEYS.PATTERN_4)
        return award === AWARD_TYPE.S || award === AWARD_TYPE.A || award === AWARD_TYPE.B || award === AWARD_TYPE.C;
      return false;
    })
    .sort((a: Award, b: Award) => b - a);
};

const getCurrentPatternKey = (hasItems: Record<Award, boolean>): ProbabilityPatternKey | 'empty' => {
  if (hasItems[AWARD_TYPE.S] && hasItems[AWARD_TYPE.A] && hasItems[AWARD_TYPE.B] && hasItems[AWARD_TYPE.C])
    return GACHA_PATTERN_KEYS.PATTERN_4;
  if (hasItems[AWARD_TYPE.S] && hasItems[AWARD_TYPE.A] && hasItems[AWARD_TYPE.B]) return GACHA_PATTERN_KEYS.PATTERN_3;
  if (hasItems[AWARD_TYPE.S] && hasItems[AWARD_TYPE.A]) return GACHA_PATTERN_KEYS.PATTERN_2;
  if (hasItems[AWARD_TYPE.S]) return GACHA_PATTERN_KEYS.PATTERN_1;
  return GACHA_PATTERN_KEYS.EMPTY; // アイテムなし
};

/**
 * 確率階層チェック関数: C >= B >= A >= S >= 5（最小確率チェックも含む）
 * @param probabilities 各賞の確率
 * @param hasItems 各賞にアイテムがあるかどうか
 * @returns 検証結果とエラーメッセージ
 */
const validateProbabilityHierarchy = (
  probabilities: Record<Award, number>,
  hasItems: Record<Award, boolean>,
): { isValid: boolean; message: string | null } => {
  const probS = hasItems[AWARD_TYPE.S] ? Number(probabilities[AWARD_TYPE.S]) : GACHA_MIN_PROBABILITY;
  const probA = hasItems[AWARD_TYPE.A] ? Number(probabilities[AWARD_TYPE.A]) : GACHA_MIN_PROBABILITY;
  const probB = hasItems[AWARD_TYPE.B] ? Number(probabilities[AWARD_TYPE.B]) : 0;
  const probC = hasItems[AWARD_TYPE.C] ? Number(probabilities[AWARD_TYPE.C]) : 0;
  const errorMessages = '上位の賞より確率を低く設定できません。';
  // S賞がある場合、S >= 5をチェック
  if (hasItems[AWARD_TYPE.S] && probS < GACHA_MIN_PROBABILITY) {
    return { isValid: false, message: `S賞は${GACHA_MIN_PROBABILITY}%以上で設定してください` };
  }

  const hierarchyChecks = [
    { higher: AWARD_TYPE.A, lower: AWARD_TYPE.S, higherProb: probA, lowerProb: probS },
    { higher: AWARD_TYPE.B, lower: AWARD_TYPE.A, higherProb: probB, lowerProb: probA },
    { higher: AWARD_TYPE.C, lower: AWARD_TYPE.B, higherProb: probC, lowerProb: probB },
  ];

  // 各階層をチェック
  for (const { higher, lower, higherProb, lowerProb } of hierarchyChecks) {
    if (hasItems[higher] && hasItems[lower] && higherProb < lowerProb) {
      return { isValid: false, message: errorMessages };
    }
  }

  return { isValid: true, message: null };
};
const hasSAwardType = (items: GachaItemFile[]): boolean => {
  return items.some((item) => item.awardType === AWARD_TYPE.S);
};

/**
 * ガチャアイテムの統計情報を計算する
 */
function calculateGachaStatistics(files: GachaItemFile[]): { totalCapacity: number; duplicatedCount: number } {
  const totalCapacity = files.length;
  const duplicatedCount = files.filter((f) => (f.receivedFileCount ?? 0) > 1).length;
  return { totalCapacity, duplicatedCount };
}

/**
 * 今回引いたファイルIDごとの個数を集計する
 */
const countPulledFiles = (pulledFiles: FileForPullDigitalGachaItems[]): Record<string, number> => {
  const countById: Record<string, number> = {};
  for (const file of pulledFiles) {
    const id = String(file.id);
    countById[id] = (countById[id] ?? 0) + 1;
  }
  return countById;
};

/**
 * 今回初めて手に入れたかどうかを判定する
 * @param afterCount 引いた後の所持数
 * @param pulledCount 今回引いた個数
 */
const isNewlyObtained = (afterCount: number, pulledCount: number): boolean => {
  const beforeCount = afterCount - pulledCount;
  return pulledCount > 0 && beforeCount === 0;
};

/**
 * File1[]（キャメルケース）からGachaItemFile[]へ変換する
 */
const toGachaItemFilesFromFile = (files: File[]): GachaItemFile[] => {
  return files.map((file) => ({
    id: String(file.id),
    title: file.name,
    src: file.objectUri && !!file.receivedFileCount ? file.objectUri : '',
    thumbnail: file.thumbnailUri && !!file.receivedFileCount ? file.thumbnailUri : '',
    type: file.fileType as any,
    size: file.size && !!file.receivedFileCount ? file.size : 0,
    duration: file.duration && !!file.receivedFileCount ? file.duration : 0,
    sortOrder: null,
    awardType: (file.awardType as Award) ?? null,
    isSecret: file.isSecret ?? false,
    receivedFileCount: file.receivedFileCount ?? 0,
    isNew: false,
  }));
};

/**
 * ガチャアイテムの詳細情報からコレクション情報を構築する
 */
const buildGachaCollection = (item: Item, id: string) => {
  // File1[]型をそのまま使う
  const allPulledFilesRaw = item.files || [];
  // 必要に応じてFile1→GachaItemFileへの変換関数を使う（toGachaItemFilesFromFile）
  const allPulledFiles = toGachaItemFilesFromFile(allPulledFilesRaw);
  const sortedItemFiles = allPulledFiles.map(({ isNew: _isNew, ...rest }) => rest);

  const { totalCapacity, duplicatedCount } = calculateGachaStatistics(allPulledFiles);

  // アイテムオプション情報の構築
  const option = item.itemOption || {};
  const sharedOptionFields = {
    hasPassword: !!option.password,
    period: option.forSale
      ? {
          start: option.forSale.startAt || undefined,
          end: option.forSale.endAt || undefined,
        }
      : undefined,
    discount: option.onSale
      ? {
          percentage: option.onSale.discountRate,
          start: option.onSale.startAt || undefined,
          end: option.onSale.endAt || undefined,
        }
      : undefined,
  };

  return {
    id,
    title: item.name || '',
    description: item.description || '',
    thumbnail: item.thumbnailUri || '',
    thumbnailRatio: 1,
    price: item.price || 0,
    currentPrice: item.currentPrice ?? item.price,
    available: item.available || false,
    itemType: item.itemType || ITEM_TYPE.DIGITAL_BUNDLE.value,
    tags: item.tags || [],
    isPurchased: item.isPurchased || false,
    isCheckout: item.isCheckout || false,
    thumbnailBlurLevel: item.thumbnailBlurLevel || 0,
    thumbnailWatermarkLevel: item.thumbnailWatermarkLevel || 0,
    totalCapacity,
    collectedUniqueItemsCount: item.collectedUniqueItemsCount ?? 0,
    remainingUniquePullCount: item.remainingUniquePullCount ?? 0,
    isCompleted: item.isCompleted || false,
    duplicatedCount,
    isDuplicated: item.isDuplicatedDigitalGachaItems || false,
    awardProbabilities: item.awardProbabilities || [],
    thumbnailType: 'custom' as const,
    ...sharedOptionFields,
    itemFiles: sortedItemFiles,
  };
};

/**
 * 今回引いたファイルIDごとの個数集計・isNew判定などを行い、GachaItemFile[]を構築する
 */
const buildCurrentPulledFiles = (
  currentPulledFilesFromApi: FileForPullDigitalGachaItems[],
  allPulledFilesRaw: ItemFiles | undefined,
): GachaItemFile[] => {
  const allPulledFiles = allPulledFilesRaw ?? [];
  const countById: Record<string, number> = {};
  for (const pulledFile of currentPulledFilesFromApi) {
    const id = String(pulledFile.id);
    countById[id] = (countById[id] ?? 0) + 1;
  }

  const currentPulledFiles = Object.keys(countById).map((id) => {
    const pulledFile = currentPulledFilesFromApi.find((f) => String(f.id) === id)!;
    const ownedFile = allPulledFiles.find((f) => Number(f.id) === Number(id));
    const totalCount = ownedFile?.receivedFileCount ?? 0;
    const receivedFileCount = countById[id];
    return {
      id: String(pulledFile.id),
      title: pulledFile.name,
      src: pulledFile.objectUri ?? '',
      thumbnail: pulledFile.thumbnailUri ?? '',
      type: pulledFile.fileType as any,
      size: pulledFile.size ?? 0,
      duration: pulledFile.duration ?? 0,
      sortOrder: null,
      awardType: (pulledFile.awardType as Award) ?? null,
      isSecret: pulledFile.isSecret ?? false,
      receivedFileCount,
      isNew: isNewlyObtained(totalCount, receivedFileCount),
    };
  });

  return currentPulledFiles;
};

/**
 * 賞のタイプに基づいて対応する画像パスを返す（レアリティアイコン用）
 * @param rarity 賞タイプの数値 (1-4)
 * @returns 画像パス
 */
export const getRaritySrc = (rarity: Award): string => {
  switch (rarity) {
    case AWARD_TYPE.S:
      return '/images/gacha/icons/RarityS.svg';
    case AWARD_TYPE.A:
      return '/images/gacha/icons/RarityA.svg';
    case AWARD_TYPE.B:
      return '/images/gacha/icons/RarityB.svg';
    case AWARD_TYPE.C:
      return '/images/gacha/icons/RarityC.svg';
    default:
      return '/images/gacha/icons/RarityS.svg';
  }
};

const convertItemFiles = (files: GachaItemFile[] = [], withId: boolean = false) =>
  files.map((file) => ({
    ...(withId ? { id: file.id ? Number(file.id) : undefined } : {}),
    name: file.title || '',
    objectUri: file.src || '',
    thumbnailUri: file.thumbnail || '',
    maskedThumbnailUri: file.maskedThumbnailUri || null,
    fileType: file.type || 'image',
    size: file.size,
    duration: file.duration,
    awardType: file.awardType || undefined,
    isSecret: file.isSecret ?? false,
  }));

const convertSamples = (samples: GachaItemFile[] = []) =>
  samples
    .filter((file) => file.id !== 'empty')
    .map((file) => ({
      name: file.title || '',
      objectUri: file.src || '',
      thumbnailUri: file.thumbnail || '',
      fileType: file.type || 'image',
      size: file.size,
      duration: file.duration,
      awardType: file.awardType || undefined,
      isSecret: file.isSecret ?? false,
    }));

const convertBenefit = (benefits: any) =>
  benefits
    ? {
        description: benefits.description || '',
        files: (benefits.benefit ?? []).map((b: any) => ({
          name: b.benefitFile?.title || '',
          objectUri: b.benefitFile?.src || '',
          fileType: b.benefitFile?.type || 'image',
          size: b.benefitFile?.size,
          duration: b.benefitFile?.duration,
        })),
      }
    : undefined;

const buildBaseDigitalGachaItemRequest = (form: ExhibitGachaItem, withId: boolean) => ({
  name: form.title,
  description: form.description,
  thumbnailUri: form.thumbnail || '',
  thumbnailFrom: form.thumbnailType === 'upload' ? 1 : 0,
  thumbnailBlurLevel: form.thumbnailBlurLevel ?? 0,
  thumbnailWatermarkLevel: form.thumbnailWatermarkLevel ?? 0,
  available: form.available,
  price: form.priceSet,
  itemFiles: convertItemFiles(form.itemFiles ?? [], withId),
  samples: convertSamples(form.samples ?? []),
  benefit: convertBenefit(form.benefits),
  tags: form.tags ?? [],
  itemOption: {
    qtyTotal: form.limited || undefined,
    forSale:
      form.period?.end || form.period?.start
        ? {
            startAt: form.period.start ? formatDateTime(form.period.start, 'display') + ':00' : undefined,
            endAt: form.period.end ? formatDateTime(form.period.end, 'display') + ':00' : undefined,
          }
        : undefined,
    onSale: form.discount?.percentage
      ? {
          discountRate: form.discount.percentage / 100,
          startAt: form.discount.start ? formatDateTime(form.discount.start, 'display') + ':00' : undefined,
          endAt: form.discount.end ? formatDateTime(form.discount.end, 'display') + ':00' : undefined,
        }
      : undefined,
  },
});

export const toCreateDigitalGachaItemRequest = (form: ExhibitGachaItem): CreateDigitalGachaItemRequest => {
  const base = buildBaseDigitalGachaItemRequest(form, false);
  return {
    ...base,
    isDuplicated: form.isDuplicated ?? false,
    awardProbabilities: form.isDuplicated ? (form.awardProbabilities ?? []) : [],
  };
};

export const toUpdateDigitalGachaItemRequest = (form: ExhibitGachaItem): UpdateDigitalGachaItemRequest => {
  const base = buildBaseDigitalGachaItemRequest(form, true);
  return {
    ...base,
  };
};

const handleAwardProbability = (items: GachaItemFile[], awardProbabilities?: AwardProbability[]) => {
  if (!awardProbabilities) return { awardType: items[0].awardType!, probability: 0 };
  const awardType = items[0].awardType!;
  const probability = awardProbabilities?.find((award) => award.awardType === awardType)?.probability;
  return { awardType, probability };
};

/**
 * ガチャ結果アニメーションのファイルパスを条件に応じて返す
 */
export const getResultAnimationSrc = (hasS: boolean, pulledCount: number): string => {
  const isTenOrMore = pulledCount >= 10;
  if (hasS && isTenOrMore) return '/movies/digital-gacha/GachaResultS10.mp4';
  if (hasS && !isTenOrMore) return '/movies/digital-gacha/GachaResultS01.mp4';
  if (!hasS && isTenOrMore) return '/movies/digital-gacha/GachaResultNormal10.mp4';
  return '/movies/digital-gacha/GachaResultNormal01.mp4';
};

export {
  getAwardsInPattern,
  getCurrentPatternKey,
  validateProbabilityHierarchy,
  hasSAwardType,
  calculateGachaStatistics,
  countPulledFiles,
  isNewlyObtained,
  toGachaItemFilesFromFile,
  buildGachaCollection,
  buildCurrentPulledFiles,
  handleAwardProbability,
};
