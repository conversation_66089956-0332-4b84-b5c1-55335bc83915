import { UseFormSetValue } from 'react-hook-form';
import { CONDITION_TYPE, type ConditionType, type GachaBenefit } from '@/types/gacha';
import { GACHA_BENEFIT_CONDITIONS } from '@/consts/gacha-data';

export interface BenefitItem extends GachaBenefit {
  uploadProgress: number;
}

export type FormValues = {
  benefits: {
    [key: string]: {
      conditionType: ConditionType;
      description: string;
    };
  };
};

/**
 * 指定した特典で利用可能な条件オプションを取得
 */
export const getAvailableConditions = (
  currentBenefitId: number,
  benefitItems: BenefitItem[],
  isCompBenefitEnabled: boolean,
  isDuplicated?: boolean,
) => {
  const currentBenefit = benefitItems.find((item) => item.id === currentBenefitId);

  let minAllowedValue = 0;
  for (let i = 0; i < currentBenefitId; i++) {
    const previousBenefit = benefitItems.find((item) => item.id === i);
    if (previousBenefit?.conditionType && previousBenefit.conditionType !== CONDITION_TYPE.COMPLETED) {
      minAllowedValue = Math.max(minAllowedValue, previousBenefit.conditionType);
    }
  }

  const isCompletedUsedByOthers = benefitItems.some(
    (benefit) => benefit.id !== currentBenefitId && benefit.conditionType === CONDITION_TYPE.COMPLETED,
  );

  const filteredOptions = GACHA_BENEFIT_CONDITIONS.filter((option) => {
    if (option.value === CONDITION_TYPE.COMPLETED) {
      const isCurrentlySelected = currentBenefit?.conditionType === CONDITION_TYPE.COMPLETED;
      // 重複出品が有効な場合、コンプ特典は表示しない
      if (isDuplicated === true) {
        return false;
      }
      return isCompBenefitEnabled && (isCurrentlySelected || !isCompletedUsedByOthers);
    }

    const currentValue = option.value;
    if (currentValue) {
      const allowed = currentValue >= minAllowedValue;
      return allowed;
    }

    return true;
  });

  return filteredOptions;
};

/**
 * 無効な後続条件をクリアする
 * @param benefitItems - 特典項目の配列
 * @param changedBenefitId - 変更された特典のID
 * @param newCondition - 新しい条件
 * @param setValue - フォームのsetValue関数
 * @param previousCondition - 前の条件
 */
export const clearInvalidSubsequentConditions = (
  benefitItems: BenefitItem[],
  changedBenefitId: number,
  newCondition: ConditionType,
  setValue: UseFormSetValue<FormValues>,
  previousCondition?: ConditionType,
): BenefitItem[] => {
  // 変更された条件が以前より小さくなった場合、後続の特典をリセット
  if (previousCondition && newCondition < previousCondition) {
    return benefitItems.map((item) => {
      if (item.id !== undefined && item.id > changedBenefitId) {
        // 後続の特典が現在設定されている条件より小さい場合、リセット
        if (item.conditionType && item.conditionType <= newCondition) {
          const resetCondition = CONDITION_TYPE.TIMES_10;
          setValue(`benefits.${item.id}.conditionType`, resetCondition);
          return { ...item, conditionType: resetCondition };
        }
      }
      return item;
    });
  }

  return benefitItems;
};
