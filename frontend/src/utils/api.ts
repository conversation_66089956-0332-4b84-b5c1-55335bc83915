import { AxiosError, AxiosResponse } from 'axios';
import { NextResponse } from 'next/server';

export const handleApiResponse = async (fetch: Promise<AxiosResponse>) => {
  try {
    const res = await fetch;
    const data = res.data;

    return NextResponse.json(data);
  } catch (error) {
    if (error instanceof AxiosError) {
      console.error(
        JSON.stringify({
          AxiosError: error,
        }),
      );
      const errors = error.response?.data.errors;
      if (errors && errors.length > 0) {
        return NextResponse.json(errors, { status: error.response?.status });
      }
    } else {
      console.error(JSON.stringify({ error }));
    }
    return new NextResponse('An unexpected error occurred', { status: 500 });
  }
};
