/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * backend API
 * OpenAPI spec version: 1.0.0-SNAPSHOT
 */
import useSwr from 'swr';
import useSWRMutation from 'swr/mutation';
import { customClientInstance } from '../../../../config/custom-client-instance';
import type {
  AgenciesResponseBody,
  AgencySalesResponseBody,
  AuditGroupsResponseBody,
  AuditStatusResponseBody,
  BaseResponseBody,
  ConsoleUserResponseBody,
  ConsoleUsersResponseBody,
  GetAgencySalesParams,
  UpdateStatusRequest,
  UsersResponseBody,
} from '../client-shop-api.schemas';
import type { Key, SWRConfiguration } from 'swr';
import type { SWRMutationConfiguration } from 'swr/mutation';

/**
 * @summary Get Agencies
 */
export const getAgencies = () => {
  return customClientInstance<AgenciesResponseBody>({ url: `/console/agencies`, method: 'GET' });
};

export const getGetAgenciesKey = () => [`/console/agencies`] as const;

export type GetAgenciesQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencies>>>;
export type GetAgenciesQueryError = void;

/**
 * @summary Get Agencies
 */
export const useGetAgencies = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencies>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgenciesKey() : null));
  const swrFn = () => getAgencies();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Agency Sales
 */
export const getAgencySales = (agencyId: number, params?: GetAgencySalesParams) => {
  return customClientInstance<AgencySalesResponseBody>({
    url: `/console/agencies/${agencyId}/sales`,
    method: 'GET',
    params,
  });
};

export const getGetAgencySalesKey = (agencyId: number, params?: GetAgencySalesParams) =>
  [`/console/agencies/${agencyId}/sales`, ...(params ? [params] : [])] as const;

export type GetAgencySalesQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencySales>>>;
export type GetAgencySalesQueryError = void;

/**
 * @summary Get Agency Sales
 */
export const useGetAgencySales = <TError = void>(
  agencyId: number,
  params?: GetAgencySalesParams,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencySales>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!agencyId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgencySalesKey(agencyId, params) : null));
  const swrFn = () => getAgencySales(agencyId, params);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Agency Users
 */
export const getAgencyUsers = (agencyId: number) => {
  return customClientInstance<UsersResponseBody>({ url: `/console/agencies/${agencyId}/users`, method: 'GET' });
};

export const getGetAgencyUsersKey = (agencyId: number) => [`/console/agencies/${agencyId}/users`] as const;

export type GetAgencyUsersQueryResult = NonNullable<Awaited<ReturnType<typeof getAgencyUsers>>>;
export type GetAgencyUsersQueryError = void;

/**
 * @summary Get Agency Users
 */
export const useGetAgencyUsers = <TError = void>(
  agencyId: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getAgencyUsers>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!agencyId;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAgencyUsersKey(agencyId) : null));
  const swrFn = () => getAgencyUsers(agencyId);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Audit Groups
 */
export const getAuditGroups = () => {
  return customClientInstance<AuditGroupsResponseBody>({ url: `/console/audit-groups`, method: 'GET' });
};

export const getGetAuditGroupsKey = () => [`/console/audit-groups`] as const;

export type GetAuditGroupsQueryResult = NonNullable<Awaited<ReturnType<typeof getAuditGroups>>>;
export type GetAuditGroupsQueryError = void;

/**
 * @summary Get Audit Groups
 */
export const useGetAuditGroups = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getAuditGroups>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetAuditGroupsKey() : null));
  const swrFn = () => getAuditGroups();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Update Audit Status
 */
export const updateAuditStatus = (auditGroupId: number, updateStatusRequest: UpdateStatusRequest) => {
  return customClientInstance<AuditStatusResponseBody>({
    url: `/console/audit-groups/${auditGroupId}/status`,
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    data: updateStatusRequest,
  });
};

export const getUpdateAuditStatusMutationFetcher = (auditGroupId: number) => {
  return (_: Key, { arg }: { arg: UpdateStatusRequest }): Promise<AuditStatusResponseBody> => {
    return updateAuditStatus(auditGroupId, arg);
  };
};
export const getUpdateAuditStatusMutationKey = (auditGroupId: number) =>
  [`/console/audit-groups/${auditGroupId}/status`] as const;

export type UpdateAuditStatusMutationResult = NonNullable<Awaited<ReturnType<typeof updateAuditStatus>>>;
export type UpdateAuditStatusMutationError = void;

/**
 * @summary Update Audit Status
 */
export const useUpdateAuditStatus = <TError = void>(
  auditGroupId: number,
  options?: {
    swr?: SWRMutationConfiguration<
      Awaited<ReturnType<typeof updateAuditStatus>>,
      TError,
      Key,
      UpdateStatusRequest,
      Awaited<ReturnType<typeof updateAuditStatus>>
    > & { swrKey?: string };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const swrKey = swrOptions?.swrKey ?? getUpdateAuditStatusMutationKey(auditGroupId);
  const swrFn = getUpdateAuditStatusMutationFetcher(auditGroupId);

  const query = useSWRMutation(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get Console Users
 */
export const getConsoleUsers = () => {
  return customClientInstance<ConsoleUsersResponseBody>({ url: `/console/users`, method: 'GET' });
};

export const getGetConsoleUsersKey = () => [`/console/users`] as const;

export type GetConsoleUsersQueryResult = NonNullable<Awaited<ReturnType<typeof getConsoleUsers>>>;
export type GetConsoleUsersQueryError = void;

/**
 * @summary Get Console Users
 */
export const useGetConsoleUsers = <TError = void>(options?: {
  swr?: SWRConfiguration<Awaited<ReturnType<typeof getConsoleUsers>>, TError> & { swrKey?: Key; enabled?: boolean };
}) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetConsoleUsersKey() : null));
  const swrFn = () => getConsoleUsers();

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
/**
 * @summary Get By Id
 */
export const getById = (id: number) => {
  return customClientInstance<ConsoleUserResponseBody>({ url: `/console/users/${id}`, method: 'GET' });
};

export const getGetByIdKey = (id: number) => [`/console/users/${id}`] as const;

export type GetByIdQueryResult = NonNullable<Awaited<ReturnType<typeof getById>>>;
export type GetByIdQueryError = BaseResponseBody | void;

/**
 * @summary Get By Id
 */
export const useGetById = <TError = BaseResponseBody | void>(
  id: number,
  options?: {
    swr?: SWRConfiguration<Awaited<ReturnType<typeof getById>>, TError> & { swrKey?: Key; enabled?: boolean };
  },
) => {
  const { swr: swrOptions } = options ?? {};

  const isEnabled = swrOptions?.enabled !== false && !!id;
  const swrKey = swrOptions?.swrKey ?? (() => (isEnabled ? getGetByIdKey(id) : null));
  const swrFn = () => getById(id);

  const query = useSwr<Awaited<ReturnType<typeof swrFn>>, TError>(swrKey, swrFn, swrOptions);

  return {
    swrKey,
    ...query,
  };
};
