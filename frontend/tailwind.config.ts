import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      screens: {
        SE: '375px',
        // => @media (min-width: 375px) { ... }
        PRO: '390px',
        // => @media (min-width: 390px) { ... }
        PC: '480px',
        // => @media (min-width: 480px) { ... }
      },
      aspectRatio: {
        auto: 'auto',
        square: '1 / 1',
      },
      spacing: {
        0.4: '0.1rem', //4px
        1.25: '0.3125rem', //5px
        3.75: '0.9375rem', //15px
        4.5: '1.125rem', //18px
        5.5: '1.375rem', //22px
        6.5: '1.625rem', //26px
        7.5: '1.875rem', //30px
        8.5: '2.125rem', //34px
        9.5: '2.375rem', //38px
        10.5: '2.625rem', //42px
        11.5: '2.875rem', //46px
        12: '3rem', //48px
        12.5: '3.125rem', //50px
        13: '3.25rem', //52px
        14: '3.5rem', //56px
        15: '3.75rem', //60px
        15.5: '3.875rem', //62px
        17: '4.25rem', //68px
        18: '4.5rem', //72px
        22: '5.5rem', //88px
        22.5: '5.625rem', //90px
        25: '6.25rem', //100px
        26: '6.5rem', //104px
        27.5: '6.75rem', //110px
        29: '7.25rem', //116px
        30: '7.5rem', //120px
        31: '7.75rem', //124px
        32.5: '8.125rem', //130px
        35: '8.75rem', //140px
        36: '9rem', //144px
        37.5: '9.375rem', //150px
        38: '9.5rem', //152px
        39: '9.75rem', //156px
        41: '10.25rem', //164px
        42: '10.5rem', //168px
        54: '13.5rem', //216px
        55: '13.75rem', //220px
        63: '15.75rem', //252px
        70: '17.5rem', //280px
        71: '17.75rem', //284px
        74: '18.5rem', //296px
        81: '20.5rem', //328px
        86: '21.5rem', //344px
        88: '22rem', //352px
        90: '22.5rem', //360px
        91: '23rem', //364px
        92: '23.5rem', //368px
        93: '24rem', //372px
        94: '24.5rem', //376px
        95: '25rem', //380px
        96: '25.5rem', //384px
        120: '30rem', //480px
        122: '30.5rem', //488px
        160: '40rem', //640px
        '1/5': '20%',
        '2/5': '40%',
        '9/10': '90%',
      },
      fontSize: {
        'regular-8': ['8px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-9': ['9px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-10': ['10px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-11': ['11px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-12': ['12px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-13': ['13px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-14': ['14px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-15': ['15px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-16': ['16px', { lineHeight: '150%', fontWeight: 400 }],
        'regular-22': ['22px', { lineHeight: '150%', fontWeight: 400 }],
        'medium-9': ['9px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-10': ['10px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-11': ['11px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-12': ['12px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-13': ['13px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-14': ['14px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-15': ['15px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-16': ['16px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-17': ['17px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-18': ['18px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-20': ['20px', { lineHeight: '150%', fontWeight: 500 }],
        'medium-22': ['22px', { lineHeight: '150%', fontWeight: 500 }],
        'semibold-12': ['12px', { lineHeight: '150%', fontWeight: 600 }],
        'semibold-16': ['16px', { lineHeight: '150%', fontWeight: 600 }],
        'bold-14': ['14px', { lineHeight: '150%', fontWeight: 600 }],
        'bold-18': ['18px', { lineHeight: '150%', fontWeight: 600 }],
        'bold-20': ['20px', { lineHeight: '150%', fontWeight: 600 }],
        'bold-22': ['22px', { lineHeight: '150%', fontWeight: 600 }],
        'bold-28': ['28px', { lineHeight: '150%', fontWeight: 600 }],
        'bold-12': ['12px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-13': ['13px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-15': ['15px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-16': ['16px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-17': ['17px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-21': ['21px', { lineHeight: '150%', fontWeight: 700 }],
        'extra-bold-18': ['18px', { lineHeight: '150%', fontWeight: 700 }],
        'extra-bold-20': ['20px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-30': ['30px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-32': ['32px', { lineHeight: '150%', fontWeight: 700 }],
        'bold-40': ['40px', { lineHeight: '100%', fontWeight: 700 }],
      },
      colors: {
        transparent: 'transparent',
        primary: 'var(--yellow-200)',
        secondary: 'var(--gray-800)',
        error: 'var(--red)',
        success: 'var(--green-100)',
        'transparent-white': 'var(--white-transparent)',
        'transparent-black': 'var(--black-transparent)',
        mask: 'var(--black-transparent)',
        'white-mask': 'var(--white-mask)',
        background: 'var(--gray-100)',
        gray: {
          50: 'var(--gray-50)',
          100: 'var(--gray-100)',
          150: 'var(--gray-150)',
          200: 'var(--gray-200)',
          300: 'var(--gray-300)',
          400: 'var(--gray-400)',
          500: 'var(--gray-500)',
          550: 'var(--gray-550)',
          600: 'var(--gray-600)',
          700: 'var(--gray-700)',
          800: 'var(--gray-800)',
          850: 'var(--gray-850)',
        },
        navy: {
          50: 'var(--navy-50)',
          100: 'var(--navy-100)',
          200: 'var(--navy-200)',
          300: 'var(--navy-300)',
        },
        pink: {
          50: 'var(--pink-50)',
          100: 'var(--pink-100)',
          200: 'var(--pink-200)',
          300: 'var(--pink-300)',
        },
        blue: {
          50: 'var(--blue-50)',
          100: 'var(--blue-100)',
          150: 'var(--blue-150)',
          160: 'var(--blue-160)',
          200: 'var(--blue-200)',
          250: 'var(--blue-250)',
          300: 'var(--blue-300)',
        },
        green: {
          10: 'var(--green-10)',
          50: 'var(--green-50)',
          100: 'var(--green-100)',
          150: 'var(--green-150)',
          200: 'var(--green-200)',
          300: 'var(--green-300)',
        },
        yellow: {
          10: 'var(--yellow-10)',
          50: 'var(--yellow-50)',
          100: 'var(--yellow-100)',
          200: 'var(--yellow-200)',
          300: 'var(--yellow-300)',
        },
        brown: {
          50: 'var(--brown-50)',
          100: 'var(--brown-100)',
        },
        orange: {
          50: 'var(--orange-50)',
          100: 'var(--orange-100)',
          200: 'var(--orange-200)',
        },
        ranking: {
          'first-border': 'var(--ranking-first-border)',
          'first-decoration': 'var(--ranking-first-decoration)',
          'first-avatar-border': 'var(--ranking-first-avatar-border)',
          'second-border': 'var(--ranking-second-border)',
          'second-decoration': 'var(--ranking-second-decoration)',
          'second-avatar-border': 'var(--ranking-second-avatar-border)',
          'third-border': 'var(--ranking-third-border)',
          'third-decoration': 'var(--ranking-third-decoration)',
          'third-avatar-border': 'var(--ranking-third-avatar-border)',
          'highlight-bg': 'var(--ranking-highlight-bg)',
        },
      },
      boxShadow: {
        dark: 'var(--gray-800) 0px 4px, var(--shadow) 0px 8px',
        'dark-none': 'var(--gray-800) 0px 4px',
        'dark-small': 'var(--gray-800) 0px 2px, var(--shadow) 0px 4px',
        'shadow-dark-small-none': 'var(--gray-800) 0px 2px',
        light: 'var(--navy-100) 0px 4px, var(--shadow) 0px 8px',
        'light-none': 'var(--navy-100) 0px 4px',
        'light-small': 'var(--navy-100) 0px 2px, var(--shadow) 0px 4px',
        'light-small-none': 'var(--navy-100) 0px 2px',
        disabled: 'var(--gray-300) 0px 4px, var(--shadow) 0px 8px',
        'disabled-small': 'var(--gray-300) 0px 2px, var(--shadow) 0px 4px',
        main: 'var(--brown-50) 0px 4px, var(--shadow) 0px 8px',
        'main-none': 'var(--brown-50) 0px 4px',
        sub: 'var(--orange-200) 0px 4px, var(--shadow) 0px 8px',
        'sub-none': 'var(--orange-200) 0px 4px',
        err: 'var(--pink-200) 0px 4px, var(--shadow) 0px 8px',
        'err-none': 'var(--pink-200) 0px 4px',
        tiny: 'var(--shadow) 0px 1px',
        'toast-success': 'var(--toast-success-shadow) 0px 0px 10px 0px',
        'toast-error': 'var(--toast-error-shadow) 0px 0px 10px 0px',
        'toast-info': 'var(--toast-info-shadow) 0px 0px 10px 0px',
        'toast-warning': 'var(--toast-warning-shadow) 0px 0px 10px 0px',
        'header-shadow': 'var(--header-shadow) 0px 0px 16px 0px',
        pink: 'var(--pink-200) 0px 4px, var(--shadow) 0px 8px',
        'section-shadow': 'var(--shadow) 0px 0px 16px 0px',
        'nav-float': 'var(--shadow) 0 2px 8px',
      },
      dropShadow: {
        xxs: '0 1px 1px var(--shadow)',
        xs: '0px 0px 5px var(--header-shadow)',
        s: '0px 0px 16px var(--header-shadow)',
      },
      borderWidth: {
        1.5: '1.5px',
        3: '3px',
      },
      borderRadius: {
        s: '4px',
        base: '8px',
        m: '10px',
        '3xl': '18px',
        '4xl': '20px',
        '5xl': '28px',
        '6xl': '30px',
        '7xl': '100px',
      },
      brightness: {
        1000: '1000%',
        98: '0.98',
        95: '0.95',
        130: '1.3',
      },
      gradientColorStops: {
        'light-gray-from': '#FAFAFA',
        'light-gray-to': '#F2F2F2',
      },
      zIndex: {
        1: '1',
        2: '2',
        3: '3',
        4: '4',
        5: '5',
        400: '400',
        401: '401',
        402: '402',
        403: '403',
        404: '404',
        405: '405',
        995: '995',
        996: '996',
        997: '997',
        998: '998',
        999: '999',
        1000: '1000',
      },
      scale: {
        98: '0.98',
        99: '0.99',
      },
      clipPath: {
        'inset-50': 'inset(50%)',
      },
      animation: {
        enter: 'enter .2s ease-out',
        leave: 'leave .15s ease-in forwards',
        'spin-reverse': 'spin-reverse 1s linear infinite',
        shake: 'shake 0.2s ease-in-out infinite',
        shake_weekly: 'shake_weekly 0.2s ease-in-out infinite',
      },
      keyframes: {
        enter: {
          '0%': {
            transform: 'translateY(-100%)',
            opacity: '0',
          },
          '100%': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
        leave: {
          '0%': {
            opacity: '1',
            transform: 'scale(1)',
          },
          '100%': {
            opacity: '0',
            transform: 'scale(.9)',
          },
        },
        'spin-reverse': {
          from: { transform: 'rotate(360deg)' },
          to: { transform: 'rotate(0deg)' },
        },
        shake: {
          '0%, 100%': { transform: 'rotate(1deg)' },
          '50%': { transform: 'rotate(-1deg)' },
        },
        shake_weekly: {
          '0%, 100%': { transform: 'rotate(0.5deg)' },
          '50%': { transform: 'rotate(-0.5deg)' },
        },
      },
      backgroundImage: {
        'ranking-pattern': "url('/shop/images/gacha/Complete_Ranking_Background.webp')",
      },
    },
  },
  plugins: [
    function ({ addUtilities }: { addUtilities: (utilities: Record<string, Record<string, string>>) => void }) {
      const newUtilities = {
        '.clip-rect-0': {
          clip: 'rect(0 0 0 0)',
        },
      };
      addUtilities(newUtilities);
    },
  ],
};
export default config;
